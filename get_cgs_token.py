import base64
import hashlib
import json
import re
import time
import urllib
from urllib.parse import urlencode

from Crypto.Cipher import AES


def generate_timestamp():
    return int(time.time())


def lstrip_subpath(path: str):
    # NOTE: 只有trident是切掉subpath后计算的token
    # '/scriber/api/v1/user/unify-login' -> '/api/v1/user/unify-login'
    cut_subpath = True
    return re.sub(r".*?(/api/v\d+/.*)", r"\1", path) if cut_subpath else path


def aes_encrypt(plaintext, key, fill=False):
    key = key.encode("utf8")
    blocksize = 16
    reminder_len = len(plaintext) % blocksize
    reminder = b""
    if reminder_len > 0:
        if fill:
            plaintext += b"\0" * (blocksize - reminder_len)
        else:
            plaintext, reminder = plaintext[:-reminder_len], plaintext[-reminder_len:]
    aes = AES.new(key, AES.MODE_CBC, key[11:27])
    return aes.encrypt(plaintext) + reminder


def revise_url(url, extra_params=None, excludes=None, exclude_domain=False, exclude_path=False):
    extra_params = extra_params or {}
    excludes = excludes or []
    url_ret = urllib.parse.urlparse(url)
    main_url = lstrip_subpath(url_ret.path)
    if url_ret.netloc:
        main_url = f"{url_ret.scheme}://{url_ret.netloc}{main_url}"
    query = url_ret.query

    if exclude_path:
        main_url = ""
    elif exclude_domain:
        main_url = urllib.parse.urlsplit(main_url).path

    params = urllib.parse.parse_qs(query) if query else {}
    params.update(extra_params)
    keys = list(params.keys())
    keys.sort()
    params_strings = []
    for key in keys:
        if key in excludes:
            continue
        values = params[key]
        if isinstance(values, list):
            values.sort()
            params_strings.extend([urllib.parse.urlencode({key: value}) for value in values])
        else:
            params_strings.append(urllib.parse.urlencode({key: values}))
    return "{}?{}".format(main_url, "&".join(params_strings)) if params_strings else main_url


def _generate_token(url, appid, secret, extra_params=None, timestamp=None, exclude_path=True):
    url = revise_url(
        url,
        extra_params=extra_params,
        excludes=["_token", "_timestamp"],
        exclude_domain=True,
        exclude_path=exclude_path,
    )
    timestamp_now = timestamp or generate_timestamp()
    source = "{}#{}#{}#{}".format(url, appid, secret, timestamp_now)
    print(source)
    token = hashlib.md5(source.encode()).hexdigest()
    return token


def get_param_u(secret):
    user = {
        "uid": "1111111",
        "uname": "显示名称test2",
        "sys_code": "PIF",  # PIF, OAS, FMP
    }
    return base64.b64encode(aes_encrypt(json.dumps(user).encode("utf-8"), key=secret, fill=True)).decode("utf-8")


def encode_url(url, appid, secret_key, exclude_path=True, need_u=False):
    _timestamp = generate_timestamp()

    params = {"_timestamp": _timestamp}
    if need_u:
        params["_u"] = get_param_u(secret_key)

    params["_token"] = _generate_token(
        url, appid, secret_key, timestamp=_timestamp, exclude_path=exclude_path, extra_params=params
    )
    join_char = "&" if "?" in url else "?"
    url = url + join_char + f"{urlencode(params)}"
    return url


def main():
    import sys

    # url = 'http://trident-cgs-pre.test.paodingai.com/scriber/api/v1/plugins/cgs/files/358/schemas/4/ai-status'
    url = sys.argv[1]

    # app_id = "cgs"
    #    app_id = 'scriber_ccxi'
    app_id = "remarkable"
    # secret_key = "069f0ba4d5c635e9b98fb94b7cd98cc4"
    secret_key = "hello_remarkable"
    url = encode_url(url, app_id, secret_key, exclude_path=False, need_u=False)
    print(url)


if __name__ == "__main__":
    main()
