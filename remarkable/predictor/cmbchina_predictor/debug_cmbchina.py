# pylint:skip-file
import logging

from remarkable.common.util import loop_wrapper
from remarkable.db import pw_db
from remarkable.models.new_file import NewFile
from remarkable.predictor.helpers import AnswerInspector, predict_mold_answer, stat


@loop_wrapper
async def inspect_answer(mid, start_id, stop_id, schema_word):
    # p_first = [r'联接基金的基金合同', '基金管理人管理本基金的联接基金', r'鉴于本基金是目标ETF的联接基金', r'鉴于本基金和本基金的联接基金',]
    # p_next = [
    #                     r'暂停资产管理计划的参与、退出时',
    #                     r'[和及]处理方式',
    #                 ],
    answer_inspector = AnswerInspector(mid, start_id, stop_id, schema_word, p_content="")
    await answer_inspector.inspect_answer()


@loop_wrapper
async def get_fids_by_tid(tid):
    return await pw_db.execute(NewFile.select(NewFile.id).where(NewFile.tree_id == tid))


def debug_predict(mid, debug_schema, fids, do_stat=0, skip_reg=r"单位|币种", strict=False):
    if do_stat:
        start_id, stop_id = None, None
        # predict_mold_answer(mid, start_id, stop_id, special_rules=debug_schema, skip_no_answer=do_stat)  # 预测
        stat(mid, None, None, skip_reg=skip_reg, strict=strict, files_ids=fids)  # 准确率统计
        return

    for fid in fids:
        start_id = fid
        stop_id = fid
        predict_mold_answer(mid, start_id, stop_id, special_rules=debug_schema)  # 预测


def main():
    do_stat = 0

    # mid, tree_id = 1, 4  # 申购调整公告 cmbchina_subscription
    mid, tree_id = 2, 1  # 招募说明书 cmbchina_prospectus
    # mid, tree_id = 3, 10  # 基金合同 cmbchina_fund
    # mid, tree_id = 4, 17  # 产品资料概要 cmbchina_information
    # mid, tree_id = 5, 16  # 产品发售公告 cmbchina_release
    # mid, tree_id = 8, 15  # 费率调整公告 cmbchina_rate_adjustment

    fids_string = [
        # 1337
        # 898
        # 888
        # 874
        # 884
        # 886
        # 882
        881
        # 879
        # 878
        # 906
    ]

    # ids = get_fids_by_tid(tree_id)
    # fids_string = [str(s1) for s1 in ids]

    # prophet_config_assist(mid)
    debug_schema = [
        # "赎回费率"
        "申购费率"
    ]
    # debug_schema = []
    # debug_schema = None
    # prepare_dataset(mid, start_id, stop_id)  # 准备数据

    # 训练数据
    # from remarkable.predictor.helpers import train_answer_data

    #
    # train_answer_data(mid, vid=0, special_rules=debug_schema)

    # prophet_config_assist(mid) # 生成模型schema

    # 预测数据
    debug_predict(mid, debug_schema, fids_string, do_stat)

    # inspect_answer(mid, None, None, debug_schema)
    # gen_customer_answer(mid, 357, 357)
    # train_v2(mid)


if __name__ == "__main__":
    logging.getLogger("remarkable.predictor.ecitic_predictor.__init__").setLevel(logging.DEBUG)
    for name in ("remarkable.common.pattern", "remarkable.predictor.models.base_model"):
        logging.getLogger(name).setLevel(logging.DEBUG)
    main()
