from remarkable.common.util import clean_txt
from remarkable.predictor.base_prophet import BaseProphet
from remarkable.predictor.predictor import JudgeByRegex


class FinancialData(JudgeByRegex):
    col_patterns = {
        "上市标准": {
            "一般企业：（1）净利润": [r"最近[两2]年净利润均为正.*?且累计(净利润)?不低于(人民币)?.*?5,?\s?000\s?万"],
            "一般企业：（2）市值+净利润+营业收入": [
                r"预计市值不低于(人民币)?10亿元，最近一年净利润为正且营业收入不低于(人民币)?1亿元"
            ],
            "一般企业（3）市值+营业收入": [r"预计市值不低于(人民币)?50亿元，且最近一年营业收入不低于(人民币)?3亿元"],
            "红筹企业：（1）市值+净利润": [r"预计市值不低于(人民币)?100亿元，且最近一年净利润为正"],
            "红筹企业：（2）市值+净利润+营业收入": [
                r"预计市值不低于(人民币)?50亿元，最近一年净利润为正且营业收入不低于(人民币)?5亿元"
            ],
            "特殊股权结构：（1）市值+净利润": [r"预计市值不低于(人民币)?100亿元，且最近一年净利润为正"],
            "特殊股权结构：（2）市值+净利润+营业收入": [
                r"预计市值不低于(人民币)?50亿元，最近一年净利润为正且营业收入不低于(人民币)?5亿元"
            ],
        },
        "机构类型": {
            "发行人": [r"发行人(?!律师|会计师)"],
            "保荐机构": [r"保荐[人|机构](（主承销商）)?(?!.*收款银行)"],
            "律师事务所": [r"律师事务所|发行人律师"],
            "会计师事务所": [r"会计师事务所|发行人会计师"],
            "资产评估机构": [r"资产评估机构|资产评估及资产评估复核机构"],
            "其他": [r""],
        },
        "身份": {
            "董事长": [r"(?<!副)董事长"],
            "总经理": [r"(?<!副)总经理"],
            "董秘": [r"董秘"],
            "保荐业务负责人": [r"保荐业务负责人"],
            "保荐代表人": [r"保荐代表人"],
            "项目协办人": [r"项目协办人"],
            "会计师事务所负责人": [r"会计师事务所负责人"],
            "签字会计师": [r"签字会计师"],
            "律师事务所负责人": [r"律师事务所负责人"],
            "签字律师": [r"签字律师"],
            "评估事务所负责人": [r"评估事务所负责人"],
            "签字评估师": [r"签字评估师"],
            "其他": [r""],
        },
        "采用的会计准则": {
            "中国企业会计准则": [
                r"(财政部.*|按照)企业会计准则|公司财务报表以持续经营(假设)?为(编制)?基础|合并财务报表以母公司和其子公司的财务报表为基础|企业会计准则第\s?33\s?号——合并财务报表"
            ],
            "等效会计准则": [],
            "国际财务报告准则": [],
            "美国会计准则": [],
        },
        "股东类型": {
            "国有股东": [r"国有法人股"],
            "境内民营机构或自然人": [],
            "境外股东": [],
            "其他": [r""],
        },
        "是否存在员工持股计划": {
            "是": [r"(设立|有).*?员工持股平台|持有公司股份|授予公司权益工具|.*?是公司的持股平台"],
            "否": [r"(不存在|无|没有未|).*员工.*持股"],
        },
        "是否存在股权激励计划": {
            "是": [r"正在执行的股权激励|实施了股权激励"],
            "否": [r"(不存在|无|没有)正在执行的.*?股权激励"],
        },
    }

    multi_answer_col_patterns = {
        "职位": {
            "values": {
                "控股股东": [r"控股股东"],
                "实际控制人": [r"实际控制人"],
            }
        },
        "董监高身份": {
            "values": {
                "董事长": [r"(?<!副)董事长(?!助理)"],
                "总经理": [r"(?<!副)总经理(?!助理)"],
                "董事": [r"董事(?![长会])"],
                "董秘": [r"董(事会)?秘"],
                "监事": [r"监事"],
                "高管": [r"高级?管|副总经理"],
                "财务负责人": [r"财务负责人"],
            },
            "default": "其他",
        },
    }


class Prophet(BaseProphet):
    def parse_enum_value(self, predictor_result, schema):
        if self.enum_predictor:
            return self.enum_predictor.predict(predictor_result, schema)
        return None

    def get_enum_predictor(self):
        enum_classes = {
            "深交所信息抽取-创业板-注册制-财务基础数据": FinancialData,
        }
        if predictor := (enum_classes.get(self._mold.name) or enum_classes.get(clean_txt(self._mold.name))):
            return predictor()
