"""
深圳标准研究院 深圳标准研究院_标准文件 用于验证chatgpt 未应用到生产环境
"""

# todo 术语和定义
predictor_options = [
    {
        "path": ["起草单位"],
        "models": [
            {
                "name": "drafts_company_gpt",
                "prompt": "提取段落中的标准起草单位，在段落中应该是每个顿号之间的文本。起草单位不应该包含顿号句号等标点符号。"
                "结果返回一段json,json应该是个数组，其中每个item就是每一个标准起草单位。下面是这段话",
                "assistant": [
                    {
                        "role": "user",
                        "content": "本标准起草单位：中国标准化研究院、广东省标准化研究院、山东省产品质量检验研究院、中融易达(北京)物联网技术研究院。",
                    },
                    {
                        "role": "assistant",
                        "content": '["中国标准化研究院", "广东省标准化研究院", "山东省产品质量检验研究院", "中融易达(北京)物联网技术研究院"]',
                    },
                    {
                        "role": "user",
                        "content": "本标准主要起草单位：深圳市标准技术研究院、华测检测认证集团股份有限公司和深圳市鑫荣懋农产品股份有限公司。",
                    },
                    {
                        "role": "assistant",
                        "content": '["深圳市标准技术研究院", "华测检测认证集团股份有限公司", "深圳市鑫荣懋农产品股份有限公司"]',
                    },
                ],
            },
        ],
    },
    {
        "path": ["起草人"],
        "models": [
            {
                "name": "drafts_man_gpt",
                "prompt": "提取段落中的标准起草人，标准起草人是一个人名，在段落中应该是每个顿号之间的文本。"
                "起草人不应该包含顿号句号等标点符号。段落的前面可能是 本标准主要起草人、本文件起草人或者本部分主要起草人。"
                "结果返回一段json,json应该是个dict，key是标准起草人，value是每个起草人的span。下面是这段话",
                "assistant": [
                    {"role": "user", "content": "本标准主要起草人：王启迪、付国、于新海、刘倩颖和王丽坤。"},
                    {
                        "role": "assistant",
                        "content": '{"王启迪": [9, 12], "付国": [13, 15], "于新海" : [16,19], "刘倩颖": [20,23], "王丽坤": [24,27]}',
                    },
                    {"role": "user", "content": "本文件主要起草人：于新海、于新、黄奎付、付国豪和黄琳丽。"},
                    {
                        "role": "assistant",
                        "content": '{"于新海": [9, 12], "于新": [13, 15], "黄奎付" : [16,19], "付国豪": [20,23], "黄琳丽": [24,27]}',
                    },
                    {"role": "user", "content": "本部分主要起草人：彭滨鸿、许琨、柯国鸿、陈学章和查澍鈜。"},
                    {
                        "role": "assistant",
                        "content": '{"彭滨鸿": [9, 12], "许琨" : [13, 15], "柯国鸿" : [16,19], "陈学章": [20,23], "查澍鈜": [24,27]}',
                    },
                ],
            },
        ],
    },
    {
        "path": ["术语和定义"],
        "sub_primary_key": ["术语"],
        "strict_group": True,
        "models": [
            {
                "name": "terms_and_definitions",
            }
        ],
    },
]

prophet_config = {
    "depends": {},
    "predictor_options": predictor_options,
}
