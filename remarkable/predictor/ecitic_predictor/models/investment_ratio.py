from remarkable.common.pattern import PatternCollection
from remarkable.predictor.ecitic_predictor.models.splitter_mixin import ParaSplitterMixin
from remarkable.predictor.models.middle_paras import MiddleParas
from remarkable.predictor.models.syllabus_elt_v2 import SyllabusEltV2
from remarkable.predictor.schema_answer import CharResult

P_SERIAL_NUMBER = PatternCollection(
    [
        r"^\s*([（(]?\d+[）)、]?|[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮])",
        r"^\s*[(（【]?\s*[➢0-9一二三四五六七八九十]+\s*[)）】]",
        r"^\s*[(（【]?\s*[➢0-9一二三四五六七八九十]+\s*[,.．，、\s]+[)）】]?\s*",
    ]
)

P_SIGN = PatternCollection([r"[%％]"])

P_SPLITS = PatternCollection([r"[。；;](?![）)])"])


class RatioSplitter:
    """
    投资比例拆分器
    拆分规则：
        1、只处理带有序号、含有%的句子
        2、按。；;拆分
    """

    def split(self, predict_elements: list) -> list:
        element_results = []
        exclude_indices = set()
        for predict_element in predict_elements:
            if predict_element["index"] in exclude_indices:
                continue
            if page_merged_paragraph := predict_element.get("page_merged_paragraph"):
                if indices := page_merged_paragraph["paragraph_indices"]:
                    exclude_indices.update(indices)
                paragraph_text = page_merged_paragraph.get("text")
            else:
                paragraph_text = predict_element.get("text")
            if not paragraph_text:
                continue
            match_no = P_SERIAL_NUMBER.nexts(paragraph_text)
            match_sign = P_SIGN.nexts(paragraph_text)
            if not match_no and not match_sign:
                continue
            chars = predict_element["chars"]

            # 按照拆分符拆分
            start_index = 0
            sub_sentences_chars = []
            for match in P_SPLITS.finditer(paragraph_text):
                if temp_chars := chars[start_index : match.start()]:
                    sub_sentences_chars.append(temp_chars)
                start_index = match.end()
            # 如果有序号，把序号添加到每一个组里面，第一组不需要
            if match_no:
                # 获取序号的chars
                match_no_chars = chars[match_no.start() : match_no.end()]
                for index, sub in enumerate(sub_sentences_chars[1:]):
                    sub_sentences_chars[index + 1] = match_no_chars + sub
            for item in sub_sentences_chars:
                element_results.append(
                    CharResult(
                        element=predict_element,
                        chars=item,
                    )
                )

        return element_results


class InvestmentRatioMiddleParas(ParaSplitterMixin, MiddleParas, RatioSplitter):
    pass


class InvestmentRatioSyllabus(ParaSplitterMixin, SyllabusEltV2, RatioSplitter):
    pass
