# pylint:skip-file
import logging

from remarkable.common.util import loop_wrapper
from remarkable.db import pw_db
from remarkable.models.new_file import NewFile
from remarkable.predictor.helpers import AnswerInspector, predict_mold_answer, stat


def debug_predict(mid, debug_schema, fids, do_stat=0, skip_reg=r"单位|币种", strict=False):
    if do_stat:
        start_id, stop_id = None, None
        # predict_mold_answer(mid, start_id, stop_id, special_rules=debug_schema, skip_no_answer=do_stat)  # 预测
        stat(mid, None, None, skip_reg=skip_reg, strict=strict, files_ids=fids)  # 准确率统计
        return

    for fid in fids:
        start_id = fid
        stop_id = fid
        predict_mold_answer(mid, start_id, stop_id, special_rules=debug_schema)  # 预测


@loop_wrapper
async def inspect_answer(mid, start_id, stop_id, schema_word):
    # p_first = [r'联接基金的基金合同', '基金管理人管理本基金的联接基金', r'鉴于本基金是目标ETF的联接基金', r'鉴于本基金和本基金的联接基金',]
    # p_next = [
    #                     r'暂停资产管理计划的参与、退出时',
    #                     r'[和及]处理方式',
    #                 ],
    answer_inspector = AnswerInspector(mid, start_id, stop_id, schema_word, p_content="")
    await answer_inspector.inspect_answer()


@loop_wrapper
async def get_fids_by_tid(tid):
    return await pw_db.execute(NewFile.select(NewFile.id).where(NewFile.tree_id == tid))


def main():
    do_stat = 0
    # mid, tree_id = 129, 165  # 资管合同 ecitic_tg_asset_management
    # mid , tree_id = 5, 165  # 资管合同 ecitic_tg_asset_management
    # mid, tree_id = 130, 166  # 私募基金合同 ecitic_tg_private_fund
    mid, tree_id = 135, 171  # 私募基金合同_补充协议 ecitic_tg_supplementary_agreement
    # mid, tree_id = 138, 177 # 资管合同_补充协议 ecitic_zg_supplementary_agreement

    fids_string = [
        "3722",
    ]

    # ids = get_fids_by_tid(tree_id)
    # fids_string = [str(s1) for s1 in ids]
    # prophet_config_assist(mid)
    debug_schema = [
        # "证券交易所释义(其它-投资监督)",
        # '期货交易所释义(其它-投资监督)',
        # '交易所释义(其它-投资监督)',
        # "投资范围(其它-投资监督)",
        # "投资限制(其它-投资监督)",
        # "投资比例(其它-投资监督)",
        # "投资策略(其它-投资监督)",
        # '调整期(其它-投资监督)',
        # '建仓期(其它-投资监督)',
        # "产品风险等级(其它-投资监督)",
        # "预警线(其它-投资监督)",
        # "止损线(其它-投资监督)",
        # "预警线描述(其它-投资监督)",
        # "平仓线(其它-投资监督)",
        # "平仓线描述(其它-投资监督)",
        # "止损线描述(其它-投资监督)",
        # "关联交易(其它-投资监督)",
        # "越权交易(其它-投资监督)",
        # "禁止行为(其它-投资监督)",
        "产品名称",
        # "免责条款(其它-投资监督)"
        # "基金的备案(其它-投资监督)"
        # "期货和衍生品账户权益释义(其它-投资监督)"
        # "同一资产释义(其它-投资监督)"
        # "流动性受限资产释义(其它-投资监督)"
    ]
    # debug_schema = []
    # debug_schema = None
    # prepare_dataset(mid, start_id, stop_id)  # 准备数据

    # 训练数据
    # from remarkable.predictor.helpers import train_answer_data
    #
    # train_answer_data(mid, special_rules=debug_schema)

    # prophet_config_assist(mid) # 生成模型schema

    # 预测数据
    debug_predict(mid, debug_schema, fids_string, do_stat)

    # debug_predict(mid, debug_schema, fids_string)  # 预测数据
    # inspect_answer(mid, None, None, debug_schema)
    # gen_customer_answer(mid, 357, 357)
    # train_v2(mid)


if __name__ == "__main__":
    for name in ("remarkable.common.pattern", "remarkable.predictor.models.base_model"):
        logging.getLogger("remarkable.predictor.ecitic_predictor.__init__").setLevel(logging.DEBUG)
    logging.getLogger(name).setLevel(logging.DEBUG)
    main()
