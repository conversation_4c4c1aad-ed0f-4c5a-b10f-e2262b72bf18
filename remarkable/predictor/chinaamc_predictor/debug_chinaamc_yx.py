# pylint:skip-file
import logging

from remarkable.common.util import loop_wrapper
from remarkable.db import pw_db
from remarkable.models.new_file import NewFile
from remarkable.predictor.helpers import AnswerInspector


@loop_wrapper
async def inspect_answer(mid, start_id, stop_id, schema_word):
    # p_first = [r'联接基金的基金合同', '基金管理人管理本基金的联接基金', r'鉴于本基金是目标ETF的联接基金', r'鉴于本基金和本基金的联接基金',]
    # p_next = [
    #                     r'暂停资产管理计划的参与、退出时',
    #                     r'[和及]处理方式',
    #                 ],
    answer_inspector = AnswerInspector(mid, start_id, stop_id, schema_word, p_content="")
    await answer_inspector.inspect_answer()


@loop_wrapper
async def get_fids_by_tid(tid):
    return await pw_db.execute(NewFile.select(NewFile.id).where(NewFile.tree_id == tid))


def main():
    do_stat = 0

    mid, tree_id = 2, 3  # 基金合同V1 chinaamc_yx_fund
    # mid, tree_id = 3, 7 # 托管协议V1 chinaamc_yx_custody
    # mid, tree_id = 4, 75 # 招募说明书V1: chinaamc_yx_prospectus
    # mid, tree_id = 5, 14 # 风险评估报告V1: chinaamc_yx_risk
    # mid, tree_id = 6, 15 # 核心要素表V1: chinaamc_yx_core_table
    # mid, tree_id = 8, 17  # 港股人员说明V1 chinaamc_yx_person_description
    # mid, tree_id = 9, 18 # 承诺函V1 chinaamc_yx_promise
    # mid, tree_id = 13, 56  # 标注章节比对 招募说明书V1 chinaamc_yx_prospectus_chapter
    # mid, tree_id = 14, 57  # 标注章节比对 基金合同V1 chinaamc_yx_fund_chapter
    # mid, tree_id = 15, 58 # 标注章节比对 托管协议V1 chinaamc_yx_custody_chapter

    # ids = get_fids_by_tid(tree_id)
    # fids_string = [str(s1) for s1 in ids]

    fids_string = [
        2426,
    ]

    # prophet_config_assist(mid)
    debug_schema = [
        "014运作方式",
    ]
    # debug_schema = []
    # debug_schema = None
    # prepare_dataset(mid, start_id, stop_id)  # 准备数据

    # 训练数据
    # from remarkable.predictor.helpers import train_answer_data
    # train_answer_data(mid, special_rules=debug_schema)

    # prophet_config_assist(mid) # 生成模型schema

    # 预测数据
    # from remarkable.predictor.debug_helpers import debug_predict
    # debug_predict(mid, debug_schema, fids_string, do_stat)

    # inspect_answer(mid, None, None, debug_schema)
    # gen_customer_answer(mid, 357, 357)
    # train_v2(mid)


if __name__ == "__main__":
    for name in ("remarkable.common.pattern", "remarkable.predictor.models.base_model"):
        logging.getLogger("remarkable.predictor.ecitic_predictor.__init__").setLevel(logging.DEBUG)
        logging.getLogger(name).setLevel(logging.DEBUG)
    main()
