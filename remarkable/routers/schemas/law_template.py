import re
from functools import partial
from itertools import dropwhile
from typing import Literal

from calliper_diff.diff_data import ElementDeleteResult, ElementInsertResult, EqualResult, defaultdict, diff_data
from calliper_diff.diff_types import DiffType
from calliper_diff.word import WordDiffResultCollection
from pydantic import BaseModel, Field, PrivateAttr

from remarkable.pdfinsight.text_util import clear_syl_title
from remarkable.plugins.cgs.common.utils import get_xpath_by_outlines
from remarkable.routers.schemas import ContractRects
from remarkable.utils.rule_para import calc_diff_ratio, generate_mocked_paras

P_CHAPTER_AND = re.compile(r"[＆&]")


def reset_paras_after_diff(paras):
    for para in paras:
        para.pop("calliper_element")
        para["chars"] = para["origin_chars"]


def generate_diff_html(
    collection: WordDiffResultCollection | EqualResult | ElementInsertResult | ElementDeleteResult,
) -> str:
    """
    Generate formatted diff string with HTML tags showing deletions, unchanged text, and insertions.
    Format: "<s>删除</s>相同<u>新增</u>"
    Returns the diff string from left to right.
    """
    if isinstance(collection, ElementInsertResult):
        text = "\n".join(ele.element["text"] for ele in collection.right_eles)
        return f"<u>{text}</u>"

    if isinstance(collection, ElementDeleteResult):
        text = "\n".join(ele.element["text"] for ele in collection.left_eles)
        return f"<s>{text}</s>"

    if isinstance(collection, EqualResult) or not collection._collection:
        return "\n".join(ele.element["text"] for ele in collection.right_eles)

    # Reconstruct the text by processing each word diff result
    result_parts = []
    left_pos = 0

    # Sort diff results by their position in the original text
    sorted_diffs = sorted(collection._collection, key=lambda x: (x.left_data.idxes[0], x.right_data.idxes[0]))

    for diff in sorted_diffs:
        left_start, left_end = diff.left_data.idxes

        # Add unchanged text before this diff (from left side as reference)
        if left_start > left_pos:
            unchanged_text = "".join(str(word) for word in collection.words1[left_pos:left_start])
            if unchanged_text.strip():
                result_parts.append(unchanged_text)

        # Process the diff based on its type
        if diff.main_type == DiffType.CHARS_DELETE:
            # Only deletion - add <s> tags
            deleted_text = diff.left_text
            if deleted_text.strip():
                result_parts.append(f"<s>{deleted_text}</s>")

        elif diff.main_type == DiffType.CHARS_INSERT:
            # Only insertion - add <u> tags
            inserted_text = diff.right_text
            if inserted_text.strip():
                result_parts.append(f"<u>{inserted_text}</u>")

        elif diff.main_type == DiffType.CHARS_REPLACE:
            # Both deletion and insertion - add both tags
            deleted_text = diff.left_text
            inserted_text = diff.right_text

            if deleted_text.strip():
                result_parts.append(f"<s>{deleted_text}</s>")
            if inserted_text.strip():
                result_parts.append(f"<u>{inserted_text}</u>")

        # Update position
        left_pos = left_end

    # Add any remaining unchanged text from the end
    if left_pos < len(collection.words1):
        unchanged_text = "".join(str(word) for word in collection.words1[left_pos:])
        if unchanged_text.strip():
            result_parts.append(unchanged_text)

    return "".join(result_parts)


def make_diff(left, diff_result):
    return {
        "html": "\n".join([generate_diff_html(diff["item"]) for diff in diff_result]),
        "left": left,
        "type": "match",
        "right": "",
    }


class LawTplContent(BaseModel):
    chapters: list[str] = Field(default_factory=list)
    diff_context: bool = False
    content: str = Field(min_length=1)

    _any_chapters: list = PrivateAttr(default_factory=list)
    _any_result: dict = PrivateAttr(default_factory=dict)

    def find_chapters(self, reader, top_chapters):
        if not self.chapters:
            self.diff_context = False
            self._any_chapters = top_chapters
        else:
            for chapter in self.chapters:
                chapters = []
                for _chapter in P_CHAPTER_AND.split(chapter):
                    chapters.append(reader.find_sylls_by_clear_title(clear_syl_title(_chapter), multi=True))
                if all(chapters):
                    self._any_chapters.append(chapters)
        return self._any_chapters

    def find_diff_with_contract_paras(self, contract_paras):
        template_paras = generate_mocked_paras([self.content])
        result = self._do_diff(template_paras, {"paragraphs": contract_paras, "syllabuses": []}, "用户输入段落", False)
        self._any_result = result

    def find_diff(self, reader):
        # 或章节 取相似度最高
        for all_chapters in self._any_chapters:
            result = {}  # &多章节, 取相似度最低
            for chapters in all_chapters:
                same_result = {}
                for chapter in chapters:  # 同名章节: 取相似度最高
                    # calliper diff 会修改, 无法复用
                    template_paras = generate_mocked_paras(self.content.split("\n"))
                    paras = reader.get_elements_by_syllabus(chapter)
                    result_data = self._do_diff(
                        template_paras,
                        {"paragraphs": paras, "syllabuses": [chapter]},
                        chapter["title"],
                        self.diff_context,
                    )
                    if not same_result or result_data["ratio"] > same_result["ratio"]:
                        same_result = result_data

                if not result or same_result["ratio"] < result["ratio"]:
                    result = same_result

            if not self._any_result or result["ratio"] > self._any_result["ratio"]:
                self._any_result = result

    @staticmethod
    def _do_diff(template_paras, right, chapter_name, diff_context):
        diff_result, _ = diff_data(
            template_paras,
            right,
            {
                "ignore_header_footer": False,
                "ignore_case": True,
                "ignore_punctuations": True,
                "ignore_chapt_numbers": True,
                "include_equal": True,
                "ignore_diff_on_toc_page": False,
                "similarity_diff_offset": 0,
            },
        )
        if not diff_context:
            filtered = list(dropwhile(lambda x: x.get("type") == "para_insert", diff_result))
            diff_result = list(reversed(list(dropwhile(lambda x: x.get("type") == "para_insert", reversed(filtered)))))

        ratio = calc_diff_ratio(diff_result)
        reset_paras_after_diff(right["paragraphs"])
        return {"ratio": ratio, "data": diff_result, "chapter": chapter_name}

    @property
    def result(self):
        return self._any_result.get("data", [])

    @property
    def is_compliance(self):
        return self._any_result.get("ratio", 0) == 100

    def generate_schema_results(self, name):
        page_outlines = defaultdict(list)
        elements = []

        for diff in self.result:
            if diff["main_type"] in (DiffType.DELETE, DiffType.PARA_DELETE, DiffType.TABLE_DELETE):
                continue

            for ele in diff["right_eles"]:
                element = ele.element
                elements.append(element)
                page_outlines[str(element["page"])].append(element["outline"])

        if elements:
            schema_result = {
                "name": name,
                "page": elements[0]["page"],
                "text": "\n".join(element["text"] for element in elements),
                "outlines": dict(page_outlines),
            }
            return (elements[0]["index"], elements[-1]["index"]), schema_result

        return None, None

    def diff(self):
        return make_diff(self.content, self.result)

    @property
    def suggestion(self):
        if self.is_compliance:
            return ""
        if self._any_chapters:
            content = "\n".join(
                [
                    ele.element["text"]
                    for diff in self.result
                    if diff["main_type"] not in (DiffType.DELETE, DiffType.PARA_DELETE, DiffType.TABLE_DELETE)
                    for ele in diff["right_eles"]
                ]
            )
            return f"合同，{self._any_result['chapter']}，请将“{content}”修改为“{self.content}”"
        if self.chapters:
            return f"请在{P_CHAPTER_AND.sub('、', self.chapters[0])}中补充“ {self.content}”"
        return f"请在文档中补充“{self.content}”"


class LawTemplateSchema(BaseModel):
    label: Literal["范文", "法规"]
    contents: list[LawTplContent] = Field(default_factory=list, min_length=1)

    def template(self):
        return {
            "name": self.label,
            "content": "\n".join(f"◇{content.content}" for content in self.contents),
            "content_title": "合同范文",
        }

    def miss_reason(self):
        return {
            "type": "tpl_conflict",
            "matched": False,
            "template": self.template(),
            "reason_text": f"未找到与{self.label}相同的内容",
            "content_title": f"当前{self.label}",
        }

    def compare(self, reader, contract_paras):
        for content in self.contents:
            if contract_paras:
                content.find_diff_with_contract_paras(contract_paras)
            else:
                content.find_diff(reader)

        current_group_diff = []
        current_group_outlines = defaultdict(list)
        for content in self.contents:
            current_group_diff.append(content.diff())
            for diff in content.result:
                if diff["main_type"] in (DiffType.DELETE, DiffType.PARA_DELETE, DiffType.TABLE_DELETE):
                    continue
                for page_str, outlines in diff["right_outline"].items():
                    page = page_str.removeprefix("page")
                    current_group_outlines[page].extend(outlines)

        if not current_group_outlines:
            return self.miss_reason()

        return {
            "diff": current_group_diff,
            "template": self.template(),
            "outlines": current_group_outlines,
            "page": min(current_group_outlines.keys(), key=int),
            "reason_text": f"匹配到{self.label}的内容" if self.is_compliance else f"与{self.label}不一致",
            "xpath": get_xpath_by_outlines(reader, current_group_outlines),
        }

    @property
    def is_compliance(self):
        return all(content.is_compliance for content in self.contents)

    @property
    def suggestion(self):
        return "\n".join(content.suggestion for content in self.contents if not content.is_compliance)


class LawTemplatesSchema(BaseModel):
    groups: list[LawTemplateSchema] = Field(default_factory=list, min_length=1)

    @staticmethod
    def create_contract_paras(contract_rects):
        contract_paras = []
        for idx, (content_text, pages_boxes) in enumerate(contract_rects or []):
            min_int = partial(min, key=int)
            pages_boxes = sorted(pages_boxes, key=min_int)
            page = min_int(pages_boxes[0])
            outline = pages_boxes[0][page][0]

            para = generate_mocked_paras([content_text], page=page, start=idx)["paragraphs"][0]
            para["outline"] = outline
            contract_paras.append(para)

        return contract_paras

    def compare(self, reader, name, contract_rects: ContractRects | None):
        contract_paras = self.create_contract_paras(contract_rects)
        top_chapters = (
            [] if contract_paras else [chapter for chapter in reader.syllabus_dict.values() if chapter["level"] == 1]
        )
        is_compliance = False
        reasons = []
        for group in self.groups:
            if contract_paras:
                group_contents = []  # 直接走for-else分支
            else:
                if not top_chapters:
                    reasons.append(group.miss_reason())
                    continue
                group_contents = group.contents

            # 组内是且, 组间是或
            for content in group_contents:
                any_chapters = content.find_chapters(reader, top_chapters)
                if not any_chapters:
                    reasons.append(group.miss_reason())
                    break  # 指定章节不存在
            else:
                reason = group.compare(reader, contract_paras)
                if group.is_compliance:
                    is_compliance = True
                reasons.append(reason)

        if is_compliance:
            suggestions = []
        else:
            suggestions = [group.suggestion for group in self.groups]
            suggestions = [suggestion for suggestion in suggestions if suggestion]

        schema_results_dict = {}
        for group in self.groups:
            for content in group.contents:
                key, result = content.generate_schema_results(name)
                if key is not None:
                    schema_results_dict[key] = result

        schema_results = [schema_results_dict[key] for key in sorted(schema_results_dict)]

        return is_compliance, reasons, "\n\n".join(suggestions), schema_results
