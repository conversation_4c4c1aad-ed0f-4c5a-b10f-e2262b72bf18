import os

from invoke import task

from remarkable.common.storage import localstorage
from remarkable.common.util import loop_wrapper
from remarkable.config import get_config
from remarkable.db import db, db_binder, pw_db
from remarkable.models.new_file import NewFile


@loop_wrapper
@db_binder(with_tx=True)
async def collect_filenames() -> set:
    filenames = set()
    attrs = ["hash", "pdf", "docx", "pdfinsight"]
    sql = "select id, hash, pdf, docx, pdfinsight from file;"
    async for row in db.iterate(db.text(sql)):
        fields = [getattr(row, attr) for attr in attrs]
        for field in fields:
            if field is None:
                continue
            filenames.add(field)

    return filenames


@task
def clear_nonexistent_files(_):
    filenames_used = collect_filenames()
    localstorage.clear_orphan_files(filenames_used, clear_cache=True)

@task
async def migration_files(_):
    files: list[NewFile] = list(await pw_db.execute(NewFile.select()))
    for file in files:
        os.rename(os.path.join(get_config("web.data_dir"), file.hash[:2], file.hash[2:]), localstorage.mount(file.path()))

@task
def clean_files(_, start_date, end_date):
