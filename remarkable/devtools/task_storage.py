import os

from invoke import task

from remarkable.common.storage import localstorage
from remarkable.common.util import loop_wrapper
from remarkable.config import get_config
from remarkable.db import db, db_binder, pw_db
from remarkable.models.new_file import NewFile


@loop_wrapper
@db_binder(with_tx=True)
async def collect_filenames() -> set:
    filenames = set()
    attrs = ["hash", "pdf", "docx", "pdfinsight"]
    sql = "select id, hash, pdf, docx, pdfinsight from file;"
    async for row in db.iterate(db.text(sql)):
        fields = [getattr(row, attr) for attr in attrs]
        for field in fields:
            if field is None:
                continue
            filenames.add(field)

    return filenames


@task
def clear_nonexistent_files(_):
    filenames_used = collect_filenames()
    localstorage.clear_orphan_files(filenames_used, clear_cache=True)

@task
async def migration_files(_):
    files: list[NewFile] = list(await pw_db.execute(NewFile.select()))
    for file in files:
        os.rename(os.path.join(get_config("web.data_dir"), file.hash[:2], file.hash[2:]), localstorage.mount(file.path()))

@task
def clean_files(ctx, start_date, end_date):
    """
    清除指定日期范围的本地文件缓存

    Args:
        ctx: invoke 上下文
        start_date: 起始日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-01)
        end_date: 终止日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-07)

    Usage:
        invoke clean-files --start-date=2025-08-01 --end-date=2025-08-07
    """
    import os
    import shutil
    from datetime import datetime, timedelta
    from remarkable.common.storage import localstorage
    from remarkable.common.config import get_config

    try:
        # 解析日期
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # 验证日期范围
        if start_dt > end_dt:
            print("错误: 起始日期不能晚于终止日期")
            return

        print(f"开始清除 {start_date} 到 {end_date} 的本地文件缓存...")

        # 检查是否启用了时间层次结构
        if not get_config("client.add_time_hierarchy", False):
            print("警告: 未启用时间层次结构 (client.add_time_hierarchy=False)")
            print("文件存储结构为: hash[:2]/hash[2:]，无法按日期清除")
            print("建议启用时间层次结构后重新上传文件")
            return

        # 收集所有需要删除的目录
        target_paths = []
        total_file_count = 0

        # 遍历日期范围
        current_date = start_dt
        while current_date <= end_dt:
            year = str(current_date.year)
            month = str(current_date.month)
            day = str(current_date.day)

            # 构建目标路径: year/month/day/
            target_path = os.path.join(year, month, day)
            full_path = localstorage.mount(target_path)

            if os.path.exists(full_path):
                # 统计文件数量
                file_count = 0
                for root, dirs, files in os.walk(full_path):
                    file_count += len(files)

                if file_count > 0:
                    target_paths.append((current_date.strftime("%Y-%m-%d"), full_path, file_count))
                    total_file_count += file_count
                    print(f"  {current_date.strftime('%Y-%m-%d')}: {file_count} 个文件")

            current_date += timedelta(days=1)

        if not target_paths:
            print("未找到任何需要删除的文件")
            return

        print(f"\n总计: {len(target_paths)} 个日期目录，{total_file_count} 个文件")

        # 确认删除
        confirm = input(f"确认删除 {start_date} 到 {end_date} 的所有缓存文件? (y/N): ")
        if confirm.lower() in ['y', 'yes']:
            deleted_count = 0
            for date_str, full_path, file_count in target_paths:
                try:
                    shutil.rmtree(full_path)
                    deleted_count += file_count
                    print(f"✓ 删除 {date_str}: {file_count} 个文件")
                except Exception as e:
                    print(f"✗ 删除 {date_str} 失败: {e}")

            print(f"\n✓ 成功删除 {deleted_count} 个缓存文件")
        else:
            print("取消删除操作")

    except ValueError as e:
        print(f"错误: 日期格式不正确，请使用 YYYY-MM-DD 格式")
        print(f"示例: invoke clean-files --start-date=2025-08-01 --end-date=2025-08-07")
        print(f"详细错误: {e}")
    except Exception as e:
        print(f"清除缓存时发生错误: {e}")
        import traceback
        traceback.print_exc()
