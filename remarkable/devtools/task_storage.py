import os
import shutil
from datetime import datetime, timedelta

from invoke import task

from remarkable.common.storage import localstorage
from remarkable.common.util import loop_wrapper
from remarkable.config import get_config
from remarkable.db import db, db_binder, pw_db
from remarkable.devtools import InvokeWrapper
from remarkable.models.new_file import NewFile


@loop_wrapper
@db_binder(with_tx=True)
async def collect_filenames() -> set:
    filenames = set()
    attrs = ["hash", "pdf", "docx", "pdfinsight"]
    sql = "select id, hash, pdf, docx, pdfinsight from file;"
    async for row in db.iterate(db.text(sql)):
        fields = [getattr(row, attr) for attr in attrs]
        for field in fields:
            if field is None:
                continue
            filenames.add(field)

    return filenames


@task
def clear_nonexistent_files(_):
    filenames_used = collect_filenames()
    localstorage.clear_orphan_files(filenames_used, clear_cache=True)


@task(klass=InvokeWrapper)
async def migration_files(ctx, dry_run=False, batch_size=100):
    """
    迁移文件从旧的存储结构到新的时间层次结构

    将文件从 hash[:2]/hash[2:] 结构迁移到 year/month/day/hash[:2]/hash[2:] 结构

    Args:
        ctx: invoke 上下文
        dry_run: 是否为试运行模式，只显示将要执行的操作而不实际移动文件
        batch_size: 批处理大小，每次处理的文件数量

    Usage:
        invoke migration-files                    # 执行迁移
        invoke migration-files --dry-run          # 试运行，查看将要执行的操作
        invoke migration-files --batch-size=50    # 指定批处理大小
    """

    print("开始文件迁移任务...")

    # 检查是否启用了时间层次结构
    if not get_config("client.add_time_hierarchy", False):
        return

    # 获取所有文件记录
    print("正在查询数据库中的文件记录...")
    try:
        files: list[NewFile] = list(await pw_db.execute(NewFile.select()))
    except Exception as e:
        print(f"查询数据库失败: {e}")
        return

    total_files = len(files)

    if total_files == 0:
        print("数据库中没有文件记录，无需迁移")
        return

    print(f"找到 {total_files} 个文件记录")

    # 统计需要迁移的文件
    migration_stats = {"total": 0, "exists": 0, "missing": 0, "already_migrated": 0, "success": 0, "failed": 0}

    # 分析文件状态
    print("正在分析文件状态...")
    files_to_migrate = []

    for i, file in enumerate(files):
        try:
            # 旧路径
            old_path = os.path.join(get_config("web.data_dir"), file.hash[:2], file.hash[2:])
            # 新路径
            new_path = localstorage.mount(file.path())
        except Exception as e:
            print(f"警告: 构建文件路径失败 {file.hash}: {e}")
            continue

        migration_stats["total"] += 1

        if os.path.exists(new_path):
            migration_stats["already_migrated"] += 1
            continue

        if not os.path.exists(old_path):
            migration_stats["missing"] += 1
            if not dry_run:
                print(f"警告: 文件不存在 {old_path}")
            continue

        migration_stats["exists"] += 1
        files_to_migrate.append((file, old_path, new_path))
    if migration_stats["exists"] == 0:
        print("没有需要迁移的文件")
        return

    # 显示统计信息
    print(f"\n文件状态统计:")
    print(f"  总文件数: {migration_stats['total']}")
    print(f"  需要迁移: {migration_stats['exists']}")
    print(f"  已经迁移: {migration_stats['already_migrated']}")
    print(f"  文件缺失: {migration_stats['missing']}")

    if dry_run:
        print(f"\n试运行模式 - 将要迁移的文件示例 (前10个):")
        for i, (file, old_path, new_path) in enumerate(files_to_migrate[:10]):
            print(f"  {old_path} -> {new_path}")
        if len(files_to_migrate) > 10:
            print(f"  ... 还有 {len(files_to_migrate) - 10} 个文件")
        return

    # 执行迁移
    print(f"\n开始迁移文件...")

    for i, (file, old_path, new_path) in enumerate(files_to_migrate):
        try:
            # 创建目标目录
            os.makedirs(os.path.dirname(new_path), exist_ok=True)

            # 移动文件
            shutil.move(old_path, new_path)
            migration_stats["success"] += 1

            # 显示进度
            if (i + 1) % batch_size == 0 or (i + 1) == len(files_to_migrate):
                print(
                    f"  迁移进度: {i + 1}/{len(files_to_migrate)} "
                    f"(成功: {migration_stats['success']}, 失败: {migration_stats['failed']})"
                )

        except Exception as e:
            migration_stats["failed"] += 1
            print(f"  迁移失败: {old_path} -> {new_path}, 错误: {e}")

    # 显示最终结果
    print(f"\n迁移完成!")
    print(f"  成功迁移: {migration_stats['success']} 个文件")
    print(f"  迁移失败: {migration_stats['failed']} 个文件")

    if migration_stats["failed"] > 0:
        print(f"  建议检查失败的文件并重新运行迁移")


@task
def clean_files(ctx, start_date, end_date):
    """
    清除指定日期范围的本地文件缓存

    Args:
        ctx: invoke 上下文
        start_date: 起始日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-01)
        end_date: 终止日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-07)

    Usage:
        invoke clean-files --start-date=2025-08-01 --end-date=2025-08-07
    """
    try:
        # 解析日期
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # 验证日期范围
        if start_dt > end_dt:
            print("错误: 起始日期不能晚于终止日期")
            return

        print(f"开始清除 {start_date} 到 {end_date} 的本地文件缓存...")

        # 收集所有需要删除的目录
        target_paths = []
        total_file_count = 0

        # 遍历日期范围
        current_date = start_dt
        while current_date <= end_dt:
            year = str(current_date.year)
            month = str(current_date.month)
            day = str(current_date.day)

            # 构建目标路径: year/month/day/
            target_path = os.path.join(year, month, day)
            full_path = localstorage.mount(target_path)

            if os.path.exists(full_path):
                # 统计文件数量
                file_count = 0
                for root, dirs, files in os.walk(full_path):
                    file_count += len(files)

                if file_count > 0:
                    target_paths.append((current_date.strftime("%Y-%m-%d"), full_path, file_count))
                    total_file_count += file_count
                    print(f"  {current_date.strftime('%Y-%m-%d')}: {file_count} 个文件")

            current_date += timedelta(days=1)

        if not target_paths:
            print("未找到任何需要删除的文件")
            return

        print(f"\n总计: {len(target_paths)} 个日期目录，{total_file_count} 个文件")

        # 确认删除
        confirm = input(f"确认删除 {start_date} 到 {end_date} 的所有缓存文件? (y/n): ")
        if confirm.lower() in ["y", "yes"]:
            deleted_count = 0
            for date_str, full_path, file_count in target_paths:
                try:
                    shutil.rmtree(full_path)
                    deleted_count += file_count
                    print(f"✓ 删除 {date_str}: {file_count} 个文件")
                except Exception as e:
                    print(f"✗ 删除 {date_str} 失败: {e}")

            print(f"\n✓ 成功删除 {deleted_count} 个缓存文件")
        else:
            print("取消删除操作")

    except ValueError as e:
        print(f"错误: 日期格式不正确，请使用 YYYY-MM-DD 格式")
        print(f"示例: invoke clean-files --start-date=2025-08-01 --end-date=2025-08-07")
        print(f"详细错误: {e}")
    except Exception as e:
        print(f"清除缓存时发生错误: {e}")
        import traceback

        traceback.print_exc()
