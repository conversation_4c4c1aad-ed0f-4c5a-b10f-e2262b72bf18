import os

from invoke import task

from remarkable.common.storage import localstorage
from remarkable.common.util import loop_wrapper
from remarkable.config import get_config
from remarkable.db import db, db_binder, pw_db
from remarkable.models.new_file import NewFile


@loop_wrapper
@db_binder(with_tx=True)
async def collect_filenames() -> set:
    filenames = set()
    attrs = ["hash", "pdf", "docx", "pdfinsight"]
    sql = "select id, hash, pdf, docx, pdfinsight from file;"
    async for row in db.iterate(db.text(sql)):
        fields = [getattr(row, attr) for attr in attrs]
        for field in fields:
            if field is None:
                continue
            filenames.add(field)

    return filenames


@task
def clear_nonexistent_files(_):
    filenames_used = collect_filenames()
    localstorage.clear_orphan_files(filenames_used, clear_cache=True)

@task
async def migration_files(_):
    files: list[NewFile] = list(await pw_db.execute(NewFile.select()))
    for file in files:
        os.rename(os.path.join(get_config("web.data_dir"), file.hash[:2], file.hash[2:]), localstorage.mount(file.path()))

@task
def clean_files(ctx, date):
    """
    清除指定日期的本地文件缓存

    Args:
        ctx: invoke 上下文
        date: 日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-07)

    Usage:
        invoke clean-files --date=2025-08-07
    """
    import os
    import shutil
    from datetime import datetime
    from remarkable.common.storage import localstorage
    from remarkable.common.config import get_config

    try:
        # 解析日期
        target_date = datetime.strptime(date, "%Y-%m-%d")
        year = str(target_date.year)
        month = str(target_date.month)
        day = str(target_date.day)

        print(f"开始清除 {date} 的本地文件缓存...")

        # 检查是否启用了时间层次结构
        if get_config("client.add_time_hierarchy", False):
            # 构建目标路径: year/month/day/
            target_path = os.path.join(year, month, day)
            full_path = localstorage.mount(target_path)

            if os.path.exists(full_path):
                print(f"找到目标目录: {full_path}")

                # 统计文件数量
                file_count = 0
                for root, dirs, files in os.walk(full_path):
                    file_count += len(files)

                if file_count > 0:
                    print(f"将删除 {file_count} 个文件")

                    # 确认删除
                    confirm = input(f"确认删除 {date} 的所有缓存文件? (y/N): ")
                    if confirm.lower() in ['y', 'yes']:
                        shutil.rmtree(full_path)
                        print(f"✓ 成功删除 {date} 的缓存文件")
                    else:
                        print("取消删除操作")
                else:
                    print(f"目录 {full_path} 为空，无需删除")
            else:
                print(f"目录 {full_path} 不存在，无需删除")
        else:
            print("警告: 未启用时间层次结构 (client.add_time_hierarchy=False)")
            print("文件存储结构为: hash[:2]/hash[2:]，无法按日期清除")
            print("建议启用时间层次结构后重新上传文件")

    except ValueError as e:
        print(f"错误: 日期格式不正确，请使用 YYYY-MM-DD 格式 (例如: 2025-08-07)")
        print(f"详细错误: {e}")
    except Exception as e:
        print(f"清除缓存时发生错误: {e}")
        import traceback
        traceback.print_exc()
