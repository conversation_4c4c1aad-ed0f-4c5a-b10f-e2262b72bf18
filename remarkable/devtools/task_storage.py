import os
import shutil
from datetime import datetime, timed<PERSON>ta

from invoke import task

from remarkable.common.storage import localstorage
from remarkable.config import get_config
from remarkable.db import pw_db
from remarkable.devtools import InvokeWrapper
from remarkable.models.new_file import NewFile


async def collect_filenames() -> set:
    filenames = set()
    attrs = ["hash", "pdf", "docx", "pdfinsight"]

    files = await pw_db.execute(NewFile.select(NewFile.id, NewFile.hash, NewFile.pdf, NewFile.docx, NewFile.pdfinsight))

    for file in files:
        fields = [getattr(file, attr) for attr in attrs]
        for field in fields:
            if field is None:
                continue
            filenames.add(field)

    return filenames


@task(klass=InvokeWrapper)
def clear_nonexistent_files(_):
    filenames_used = collect_filenames()
    localstorage.clear_orphan_files(filenames_used, clear_cache=True)


@task(klass=InvokeWrapper)
async def migration_files(ctx):
    """
    迁移文件从旧的存储结构到新的时间层次结构

    将文件从 hash[:2]/hash[2:] 结构迁移到 year/month/day/hash[:2]/hash[2:] 结构
    """

    # 检查是否启用了时间层次结构
    if not get_config("client.add_time_hierarchy", False):
        return

    # 获取所有文件记录
    files: list[NewFile] = list(await pw_db.execute(NewFile.select()))

    if not files:
        print("数据库中没有文件记录，无需迁移")
        return

    # 统计需要迁移的文件
    migration_stats = {"total": 0, "exists": 0, "missing": 0, "already_migrated": 0, "success": 0, "failed": 0}

    # 分析文件状态
    print("开始文件迁移任务...")
    files_to_migrate = []

    # 需要迁移的字段
    file_fields = ["hash", "pdf", "pdfinsight", "revise_docx", "revise_pdf", "docx"]

    # 需要检查的源路径（都在 web.data_dir 下）
    base_data_dir = get_config("web.data_dir")
    source_paths = [
        ("web.data_dir", base_data_dir),
        ("cache_root", os.path.join(base_data_dir, localstorage.cache_root)),
        ("label_cache_dir", os.path.join(base_data_dir, localstorage.label_cache_dir)),
    ]

    for i, file in enumerate(files):
        if i % 1000 == 0:
            print(f"  分析进度: {i}/{len(files)}")

        # 处理每个文件的多个字段
        for field_name in file_fields:
            try:
                field_value = getattr(file, field_name, None)

                # 判空：跳过空值
                if not field_value:
                    continue

                # 新路径
                new_path = localstorage.mount(file.path(field_name))

                # 检查所有可能的源路径
                found_source = False
                for source_name, source_dir in source_paths:
                    if not source_dir:  # 跳过空的源路径
                        continue

                    # 旧路径
                    old_path = os.path.join(source_dir, field_value[:2], field_value[2:])

                    if os.path.exists(old_path):
                        found_source = True
                        migration_stats["total"] += 1

                        if os.path.exists(new_path):
                            migration_stats["already_migrated"] += 1
                            break

                        migration_stats["exists"] += 1
                        files_to_migrate.append((file, field_name, field_value, old_path, new_path, source_name))
                        break

                # 如果在所有源路径中都没找到文件
                if not found_source:
                    migration_stats["total"] += 1
                    if os.path.exists(new_path):
                        migration_stats["already_migrated"] += 1
                    else:
                        migration_stats["missing"] += 1
                        print(f"警告: 文件在所有源路径中都不存在 {field_value} (字段: {field_name})")

            except Exception as e:
                print(f"警告: 构建文件路径失败 {file.id}.{field_name}={field_value}: {e}")
                continue
    if migration_stats["exists"] == 0:
        print("没有需要迁移的文件")
        return

    # 显示统计信息
    print("\n文件状态统计:")
    print(f"  总文件数: {migration_stats['total']}")
    print(f"  需要迁移: {migration_stats['exists']}")
    print(f"  已经迁移: {migration_stats['already_migrated']}")
    print(f"  文件缺失: {migration_stats['missing']}")

    # 执行迁移
    print("\n开始迁移文件...")

    for i, (file, field_name, field_value, old_path, new_path, source_name) in enumerate(files_to_migrate):
        try:
            # 创建目标目录
            os.makedirs(os.path.dirname(new_path), exist_ok=True)

            # 移动文件
            shutil.move(old_path, new_path)
            migration_stats["success"] += 1

            # 显示进度
            if (i + 1) % 100 == 0 or (i + 1) == len(files_to_migrate):
                print(
                    f"  迁移进度: {i + 1}/{len(files_to_migrate)} "
                    f"(成功: {migration_stats['success']}, 失败: {migration_stats['failed']})"
                )

        except Exception as e:
            migration_stats["failed"] += 1
            print(f"  迁移失败: {old_path} -> {new_path} (文件ID: {file.id}, 字段: {field_name}, 源: {source_name}), 错误: {e}")

    # 显示最终结果
    print("\n迁移完成!")
    print(f"  成功迁移: {migration_stats['success']} 个文件")
    print(f"  迁移失败: {migration_stats['failed']} 个文件")

    if migration_stats["failed"] > 0:
        print("  建议检查失败的文件并重新运行迁移")


@task
def clean_files(ctx, start_date, end_date):
    """
    清除指定日期范围的本地文件缓存

    Args:
        ctx: invoke 上下文
        start_date: 起始日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-01)
        end_date: 终止日期字符串，格式为 YYYY-MM-DD (例如: 2025-08-07)

    Usage:
        invoke clean-files --start-date=2025-08-01 --end-date=2025-08-07
    """
    # 检查是否启用了时间层次结构
    if not get_config("client.add_time_hierarchy", False):
        return

    try:
        # 解析日期
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # 验证日期范围
        if start_dt > end_dt:
            print("错误: 起始日期不能晚于终止日期")
            return

        print(f"开始清除 {start_date} 到 {end_date} 的本地文件缓存...")

        # 收集所有需要删除的目录
        target_paths = []
        total_file_count = 0

        # 遍历日期范围
        current_date = start_dt
        while current_date <= end_dt:
            year = str(current_date.year)
            month = str(current_date.month)
            day = str(current_date.day)

            # 构建目标路径: year/month/day/
            target_path = os.path.join(year, month, day)
            full_path = localstorage.mount(target_path)

            if os.path.exists(full_path):
                # 统计文件数量
                file_count = 0
                for _, _, files in os.walk(full_path):
                    file_count += len(files)

                if file_count > 0:
                    target_paths.append((current_date.strftime("%Y-%m-%d"), full_path, file_count))
                    total_file_count += file_count
                    print(f"  {current_date.strftime('%Y-%m-%d')}: {file_count} 个文件")

            current_date += timedelta(days=1)

        if not target_paths:
            print("未找到任何需要删除的文件")
            return

        print(f"\n总计: {len(target_paths)} 个日期目录，{total_file_count} 个文件")

        # 确认删除
        confirm = input(f"确认删除 {start_date} 到 {end_date} 的所有缓存文件? (y/n): ")
        if confirm.lower() in ["y", "yes"]:
            deleted_count = 0
            for date_str, full_path, file_count in target_paths:
                try:
                    shutil.rmtree(full_path)
                    deleted_count += file_count
                    print(f"✓ 删除 {date_str}: {file_count} 个文件")
                except Exception as e:
                    print(f"✗ 删除 {date_str} 失败: {e}")

            print(f"\n✓ 成功删除 {deleted_count} 个缓存文件")
        else:
            print("取消删除操作")

    except ValueError as e:
        print("错误: 日期格式不正确，请使用 YYYY-MM-DD 格式")
        print("示例: invoke clean-files --start-date=2025-08-01 --end-date=2025-08-07")
        print(f"详细错误: {e}")
    except Exception as e:
        print(f"清除缓存时发生错误: {e}")
        import traceback

        traceback.print_exc()
