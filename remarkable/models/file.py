from __future__ import annotations

import logging
import os
from datetime import datetime
from functools import cached_property
from typing import TYPE_CHECKING, Optional

from gino.dialects.asyncpg import JSONB
from sqlalchemy import and_, desc

from remarkable.common.constants import PDFFlag, PDFParseStatus, TagType
from remarkable.common.enums import TaskType
from remarkable.common.storage import localstorage
from remarkable.common.util import generate_timestamp, simple_match_ext
from remarkable.config import get_config
from remarkable.db import db
from remarkable.models import ModelBase, TimeStampMixin
from remarkable.models.china_stock import ChinaStockAnswer
from remarkable.models.query_helper import QueryHelper
from remarkable.models.user import User

if TYPE_CHECKING:
    from remarkable.file_flow.uploaded_file import UploadedFile

CACHE_DIR = "pdf_cache"


class File(TimeStampMixin, ModelBase):
    __tablename__ = "file"

    name = db.Column(db.String)
    hash = db.Column(db.String)
    pdf = db.Column(db.String)
    pdf_flag = db.Column(db.Integer)
    size = db.Column(db.Integer)
    page = db.Column(db.Integer)
    pdf_parse_status = db.Column(db.Integer)
    pdfinsight = db.Column(db.String)
    docx = db.Column(db.String)
    revise_docx = db.Column(db.String)
    revise_pdf = db.Column(db.String)
    annotation_path = db.Column(db.String)
    farm_id = db.Column(db.Integer)
    link = db.Column(db.String, default="")
    meta_info = db.Column(JSONB)

    tree_id = db.Column(db.Integer, db.ForeignKey("file_tree.id"))
    pid = db.Column(db.Integer, db.ForeignKey("file_project.id"))
    molds = db.Column(db.ARRAY(db.Integer))
    uid = db.Column(db.Integer, db.ForeignKey("admin_user.id"), default=1)
    task_type = db.Column(db.String)
    sysfrom = db.Column(db.String)
    source = db.Column(db.String)
    origin_tree_id = db.Column(db.Integer)
    rank = db.Column(db.Integer, default=0)
    priority = db.Column(db.Integer, default=9)

    @property
    def pdf_name(self):
        return f"{os.path.splitext(self.name)[0]}.pdf"

    @property
    def ext(self):
        return os.path.splitext(self.name)[-1].lower()

    @cached_property
    def is_pdf(self):
        return simple_match_ext(self.ext, self.path(abs_path=True), ".pdf")

    @cached_property
    def is_image(self):
        return simple_match_ext(self.ext, self.path(abs_path=True), ".jpg", ".jpeg", ".png", ".tif")

    @cached_property
    def is_word(self):
        return simple_match_ext(self.ext, self.path(abs_path=True), ".doc", ".docx")

    @cached_property
    def is_excel(self):
        return simple_match_ext(self.ext, self.path(abs_path=True), ".xls", ".xlsx")

    @cached_property
    def is_ppt(self):
        return simple_match_ext(self.ext, self.path(abs_path=True), ".ppt", ".pptx")

    @cached_property
    def is_txt(self):
        return simple_match_ext(self.ext, self.path(abs_path=True), ".txt")

    @property
    def label_cache_dir(self):
        """PDFinsight相关数据缓存
        TODO: 迁移文档svg/搜索缓存到这里
        """
        if not self.pdfinsight_path():
            raise FileNotFoundError(f"file: {self.id} missing pdfinsight data")
        cache_dir = localstorage.mount(os.path.join(localstorage.label_cache_dir, self.pdfinsight_path()))
        localstorage.create_dir(cache_dir)
        return cache_dir

    @classmethod
    def loader(cls):
        from .file_meta import FileMeta

        return cls.load(
            file_meta=FileMeta.on(FileMeta.file_id == cls.id),
            project=FileProject.on(FileProject.id == cls.pid),
            user=User.on(User.id == cls.uid),
        )

    async def delete_(self):
        from remarkable.plugins.diff.common import CCXI_CACHE_PATH

        from .ccxi import CCXIContract
        from .file_meta import FileMeta
        from .question import Question
        from .rule_result import RuleResult

        await super().delete_()

        for question in await Question.find_by_fid(self.id):
            await question.delete_()

        file_meta = await FileMeta.find_by_kwargs(file_id=self.id)
        if file_meta:
            await file_meta.delete_()

        await RuleResult.delete.where(RuleResult.fid == self.id).gino.status()

        await CCXIContract.delete.where(CCXIContract.fid == self.id).gino.status()
        await ChinaStockAnswer.delete.where(ChinaStockAnswer.fid == self.id).gino.status()

        if self.meta_info and self.task_type == TaskType.CLEAN_FILE.value:
            from remarkable.value_obj import CGSFileMeta

            CGSFileMeta(**self.meta_info).clean_file.delete()

        same_file = await File.find_by_kwargs(hash=self.hash)
        if same_file:
            return None
        if self.pdf_path():
            localstorage.delete_file(self.pdf_path())
        if self.pdfinsight_path():
            localstorage.delete_file(self.pdfinsight_path())
            # romove ccxi pdfinsight cache
            ccxi_path = os.path.join(CCXI_CACHE_PATH, self.pdfinsight[:2], self.pdfinsight[2:])
            localstorage.delete_file(ccxi_path)
        if self.pdf_cache_path():
            localstorage.delete_dir(self.pdf_cache_path())
        # 如果没有相同文件, 则删除db关联的PDFinsight hash记录
        await self.update_(pdfinsight=None)

    def path(self, col="hash", *, parent="", abs_path=False) -> str | None:
        if col not in self.table_columns():
            logging.error(f"{col} not in table: {self.__tablename__}")
            return None
        col_val = getattr(self, col)
        if col_val is None or str(col_val) == "" or str(col_val) == "null":
            return None

        # relative_path = os.path.join(parent, col_val[:2], col_val[2:])
        if get_config("client.add_time_hierarchy", False):
            if not hasattr(self, "created_utc"):
                logging.warning(f"{self.table_name()} has no column created_utc")
                return None
            created_utc = self.created_utc
            if created_utc is None or str(created_utc) == "" or str(created_utc) == "null":
                return None
            upload_date = datetime.fromtimestamp(created_utc)
            relative_path = os.path.join(
                parent, str(upload_date.year), str(upload_date.month), str(upload_date.day), col_val[:2], col_val[2:]
            )
        else:
            relative_path = os.path.join(parent, col_val[:2], col_val[2:])
        return localstorage.mount(relative_path) if abs_path else relative_path

    def docx_path(self):
        return self.path(col="docx")

    def revise_docx_path(self):
        return self.path(col="revise_docx")

    def raw_pdf_path(self):
        # convert_to_pdf 处理图片转pdf的流早前存在问题（我们会将图片转换成pdf后进行处理，然后再将pdf传送到pdfinsight解析,
        # 但pdfinsight需要的是原始的pdf，不然会产生其他不可预知的问题）
        # 现在修正流程，保存了原始的pdf发送给pdfinsight,并且如果是图片的情况下应该也将pdf的path置为原始的文件路径
        if self.is_image and self.meta_info:
            if raw_pdf := self.meta_info.get("raw_pdf"):
                return os.path.join(raw_pdf[:2], raw_pdf[2:])
        return None

    def pdf_path(self):
        return self.path(col="pdf")

    def pdfinsight_path(self):
        return self.path(col="pdfinsight")

    def pdf_cache_path(self):
        return os.path.join(CACHE_DIR, self.pdf[:2], self.pdf[2:]) if self.pdf else None

    def annotation_pdf_path(self):
        return self.path(col="annotation_path")

    @staticmethod
    def get_path(col_val):
        return os.path.join(col_val[:2], col_val[2:]) if col_val else None

    @classmethod
    async def file_query(
        cls,
        filters,
        questions_filters,
        uid,
        params,
        page=None,
        size=None,
        group_by=None,
        order_by=None,
        join_tables="",
        columns=None,
    ):
        # TODO: 待重构
        if "uid" not in params:
            params["uid"] = uid
        params["default_health"] = get_config("web.default_question_health", 2)

        if not group_by:
            group_by = " group by (file.id, admin_user.name)"

        if not order_by:
            order_by = "order by file.id desc"

        columns = columns or []
        all_columns = (
            ["file.{}".format(col) for col in cls.table_columns()]
            + ["admin_user.name as user_name"]
            + columns
            + [
                f"""array_to_json(array(select row_to_json(t) from (
                   select question.id, question.mold, question.ai_status, question.health, question.updated_utc, question.fill_in_user, question.data_updated_utc, question.fill_in_status,
                   question.progress, question.status, question.health, question.ai_status, question.name, question.num, question.mark_uids, question.mark_users,
                   COALESCE(question.origin_health, :default_health) as origin_health, mold.name AS mold_name
                from question
                INNER JOIN mold on question.mold = mold.id
                where {questions_filters}) as t
                )) as questions""",
            ]
            + ["array_remove(array_agg(DISTINCT file_tag.id), NULL) tags"]
        )
        sql = """
            with file_tag as (
                select t.id, tr.relational_id file_id
                from tag_relation tr
                left join tag t on t.id = tr.tag_id
                where t.tag_type = :tag_type
            )
            select {{}}
                from file
                left join question on file.id = question.fid
                left join file_project on file.pid = file_project.id
                left join admin_user on file.uid = admin_user.id
                left join file_tag on file.id = file_tag.file_id
                {join_tables}
                where {filters}
        """.format(filters=filters, join_tables=join_tables)
        params["tag_type"] = TagType.FILE
        data = await QueryHelper.pagedata(
            sql, all_columns, group_by=group_by, order_by=order_by, page=page, size=size, params=params
        )
        if page is not None and size is not None:
            return data["total"], data["items"]
        return None, data["items"]

    @classmethod
    async def list_all_by_page(
        cls,
        prj_id,
        mold_id,
        uid,
        page,
        size,
        answered=False,
        fileid=None,
        qstatus=None,
        filename=None,
        is_manager=True,
        cond=None,
        join_tables="",
        columns=None,
        group_by=None,
    ):
        filters = [" file.deleted_utc = 0"]
        questions_filters = [" question.fid=file.id and question.deleted_utc=0"]
        if answered:
            filters.append(f"AND {uid}=ANY(question.mark_uids)")
            questions_filters.append(f" AND {uid}=ANY(question.mark_uids)")
        if qstatus is not None:
            filters.append(" and question.status=:qstatus")
            qstatus = int(qstatus)

        if mold_id != -1:
            filters.append(" and mold=:mold")
        if prj_id != -1:
            filters.append(" and file.pid=:pid")
        else:
            filters.append(" and file_project.visible")

        if fileid:
            filters.append("AND file.id = :fileid")
        if filename:
            filters.append("AND file.name ILIKE :filename ESCAPE '='")
        if not is_manager:
            filters.append(f" AND (file_project.uid = {uid} or file_project.public)")
        if cond:
            filters.append(cond)

        total, data = await cls.file_query(
            filters=" ".join(filters),
            questions_filters=" ".join(questions_filters),
            uid=uid,
            params={
                "pid": prj_id,
                "mold": mold_id,
                "uid": uid,
                "qstatus": qstatus,
                "filename": filename,
                "fileid": fileid,
            },
            page=page,
            size=size,
            group_by=group_by,
            join_tables=join_tables,
            columns=columns,
        )
        return {"total": total, "items": data}


class FileTree(TimeStampMixin, ModelBase):
    __tablename__ = "file_tree"

    ptree_id = db.Column(db.Integer, db.ForeignKey("file_tree.id"), nullable=False)
    pid = db.Column(db.Integer, db.ForeignKey("file_project.id"), nullable=False)
    name = db.Column(db.String, nullable=False)
    default_molds = db.Column(db.ARRAY(db.Integer))
    uid = db.Column(db.Integer, default=-1)
    meta = db.Column(JSONB, default={})
    origin_ptree_id = db.Column(db.Integer)

    async def delete_(self):
        deleted_utc = generate_timestamp()
        await self.update_(deleted_utc=deleted_utc, updated_utc=deleted_utc)
        files = await File.find_by_kwargs("all", tree_id=self.id)
        for file in files:
            await file.delete_()

        trees = await self.list_by_tree(self.id)
        for tree in trees:
            await tree.delete_()

    @classmethod
    def user_loader(cls):
        return cls.load(user=User.on(User.id == cls.uid))

    @classmethod
    async def list_by_tree(cls, _id):
        return (
            await cls.user_loader()
            .query.where(and_(cls.ptree_id == _id, cls.deleted_utc == 0))
            .order_by(desc(cls.id))
            .gino.all()
        )

    @classmethod
    async def find_default_molds(cls, tree_id):
        tree = await cls.find_by_id(tree_id)
        if tree.default_molds or not tree.ptree_id:
            return tree.default_molds
        return await FileTree.find_default_molds(tree.ptree_id)

    @classmethod
    async def find_all_sub_tree(cls, tree_id, include_self=False):
        async def recursive_find(_tree_id):
            trees = await FileTree.find_by_kwargs(ptree_id=_tree_id, delegate="all")
            if trees:
                for tree in trees:
                    tree_ids.append(tree.id)
                    await recursive_find(tree.id)

        tree_ids = []
        if include_self:
            tree_ids.append(tree_id)
        await recursive_find(tree_id)
        return tree_ids


class FileProject(TimeStampMixin, ModelBase):
    __tablename__ = "file_project"

    name = db.Column(db.String, nullable=False)
    rtree_id = db.Column(db.Integer, db.ForeignKey("file_tree.id"), nullable=False)
    preset_answer_model = db.Column(db.String)  # 已经废弃的字段
    default_molds = db.Column(db.ARRAY(db.Integer))
    uid = db.Column(db.Integer, default=-1)
    public = db.Column(db.Boolean, default=True)
    visible = db.Column(db.Boolean, default=True)
    meta = db.Column(JSONB, default={})
    status = db.Column(db.String)

    async def delete_(self):
        await self.update_(deleted_utc=generate_timestamp())
        root = await FileTree.find_by_id(self.rtree_id)
        if root:
            await root.delete_()

    async def create_root_tree(self):
        if self.rtree_id:
            raise Exception("the project already have root filetree")
        tree = await FileTree.create_(
            **{
                "name": self.name,
                "ptree_id": 0,
                "default_molds": self.default_molds,
                "uid": self.uid,
                "pid": self.id,
                "meta": self.meta,
            },
        )
        self.rtree_id = tree.id
        await self.update_(rtree_id=tree.id)
        return tree

    @classmethod
    async def create_(cls, **columns):
        if "rtree_id" not in columns:
            columns["rtree_id"] = 0
        project = await super(FileProject, cls).create_(**columns)
        return project

    @classmethod
    def user_loader(cls):
        return cls.load(user=User.on(User.id == cls.uid))

    def build_file_data(
        self, upload_file: UploadedFile, tree_id: int, uid: Optional[int] = None, data: Optional[dict] = None
    ):
        pdf_hash = upload_file.md5 if upload_file.is_pdf else None
        file_data = {
            "tree_id": tree_id,
            "pid": self.id,
            "name": upload_file.filename,
            "hash": upload_file.md5,
            "size": upload_file.length,
            "page": None,
            "molds": [],
            "pdf": pdf_hash,
            "docx": upload_file.md5 if upload_file.is_docx else None,
            "uid": uid if uid else self.uid,
            "pdfinsight": None,
            "pdf_flag": PDFFlag.CONVERTED.value if pdf_hash else PDFFlag.NEED_CONVERT.value,
            "pdf_parse_status": PDFParseStatus.PENDING.value,
            "meta_info": None,
            "link": None,
            "task_type": TaskType.EXTRACT.value,
            "sysfrom": None,
            "source": None,
            "rank": 0,
            "priority": 9,
        }
        if data:
            return file_data | data

        return file_data

    def get_constrained_molds(self, molds_limit):
        return molds_limit.get(self.meta.get("prj_type"))


if __name__ == "__main__":
    import asyncio

    asyncio.run(File.list_files_labeled(17, tree_l=[231]))
