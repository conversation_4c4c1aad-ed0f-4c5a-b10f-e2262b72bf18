import json
from copy import deepcopy
from typing import List, <PERSON>

from typing_extensions import Protocol

from remarkable.answer.common import gen_key_md5
from remarkable.answer.node import AnswerItem
from remarkable.answer.reader import AnswerReader
from remarkable.common.constants import HistoryAction
from remarkable.common.util import generate_timestamp
from remarkable.db import peewee_transaction_wrapper, pw_db
from remarkable.models.new_user import AD<PERSON><PERSON>, NewAdminUser
from remarkable.plugins.fileapi.common import LabelEncoder
from remarkable.plugins.fileapi.predict import answer_convert
from remarkable.pw_models.answer_data import NewAnswerData
from remarkable.pw_models.model import NewAnswer, NewMold, NewSpecialAnswer
from remarkable.pw_models.question import NewQuestion
from remarkable.schema.answer import UserAnswer
from remarkable.service.new_mold import NewMoldService
from remarkable.utils.answer_util import AnswerUtil
from remarkable.worker.tasks.common_tasks import save_event_log


@peewee_transaction_wrapper
async def set_convert_answer(qid):
    """
    合并答案转换为导出答案
    """
    question = await NewQuestion.find_by_id(qid)
    if not question or not question.answer or not question.answer["userAnswer"]["items"]:
        return
    converted_answer = await answer_convert(question)
    if converted_answer is None:
        return
    existed_answers = await NewSpecialAnswer.get_answers(qid, NewSpecialAnswer.ANSWER_TYPE_EXPORT, top=1)
    if existed_answers:
        mold = await NewMold.find_by_id(question.mold)
        export_answer = existed_answers[0]
        merged_answer = AnswerUtil.merge_answers(
            [
                UserAnswer._make([ADMIN.id, ADMIN.name, export_answer.data]),
                UserAnswer._make([ADMIN.id, ADMIN.name, json.loads(json.dumps(converted_answer, cls=LabelEncoder))]),
            ],
            schema_data=mold.data,
        )
        update_info = {"data": json.dumps(merged_answer, cls=LabelEncoder), "updated_utc": generate_timestamp()}
        await export_answer.update_(**update_info)
    else:
        await NewSpecialAnswer.create(
            **{
                "qid": qid,
                "answer_type": NewSpecialAnswer.ANSWER_TYPE_EXPORT,
                "data": json.dumps(converted_answer, cls=LabelEncoder),
            },
        )


async def delete_custom_field(user: NewAdminUser, qid: int, md5_list: List[str]):
    for answer in await NewAnswer.get_answers_by_qid(qid):
        if not NewQuestion.answer_items(answer.data, "custom_field"):
            continue
        answer.data["custom_field"]["items"] = [
            i for i in answer.data["custom_field"]["items"] if gen_key_md5(i) not in md5_list
        ]
        await answer.update_(data=answer.data)
    question = await NewQuestion.find_by_id(qid)
    items = []
    deleted_item = None
    for item in question.answer["custom_field"]["items"]:
        if gen_key_md5(item) in md5_list:
            deleted_item = item
        else:
            items.append(item)
    question.answer["custom_field"]["items"] = items
    save_event_log.delay(user.id, user.name, HistoryAction.DELETE_CUSTOM_FIELD, qid, deleted_item)
    await question.update_(answer=question.answer)


class SimpleQuestion(Protocol):
    id: int
    fid: int
    mold: int


async def get_master_question_answer(question: SimpleQuestion):
    """
    将answer_data表中的数据组装成普通answer格式
    {
        "schema": {},
        "userAnswer: {}
    }
    :return:
    """
    molds = await NewMoldService.get_related_molds(question.fid, question.mold)
    mold, _ = NewMoldService.master_mold_with_merged_schemas(molds)
    cond = NewAnswerData.qid == question.id
    data = await pw_db.execute(NewAnswerData.select().where(cond).order_by(NewAnswerData.id))
    user_map = await NewAdminUser.get_user_name_map()

    p_molds_name = NewMoldService.get_p_molds_name(molds)

    mold_dict = deepcopy(mold.to_dict())
    schema = mold_dict["data"]
    schema["version"] = mold_dict["checksum"]

    answer = {
        "userAnswer": {
            "items": [x.to_dict(master_mold=molds[0], p_molds_name=p_molds_name, user_map=user_map) for x in data]
        },
        "schema": schema,
    }

    return answer, mold


async def get_master_preset_answer(question: Union[NewQuestion]):
    molds = await NewMoldService.get_related_molds(question.fid, question.mold)
    mold, fixed_molds = NewMoldService.master_mold_with_merged_schemas(molds)
    questions = await NewQuestion.find_by_fid_mids(question.fid, [x.id for x in molds])
    answer_datas = []
    if not all(x.preset_answer for x in questions):
        return answer_datas, mold

    mold_reserved_fields = {}
    for fixed_mold in fixed_molds:
        mold_reserved_fields[fixed_mold.name] = fixed_mold.data["schemas"][0]["orders"]

    for question in questions:
        answer_reader = AnswerReader(question.preset_answer)
        for item in answer_reader.items:
            answer_item = AnswerItem(**item)
            if answer_item.first_level_field not in mold_reserved_fields.get(answer_reader.mold_name, []):
                continue

            if not answer_item.value:
                value = []
            elif isinstance(answer_item.value, str):
                value = [answer_item.value]
            elif isinstance(answer_item.value, (list, tuple)):
                value = answer_item.value
            else:
                raise ValueError(f"Invalid {answer_item.value}")

            answer_data = {
                "qid": question.id,
                "uid": answer_item.marker["id"] if answer_item.marker else None,
                "key": answer_item.key,
                "data": answer_item.data,
                "schema": answer_item.schema,
                "value": value,
                "text": answer_item.text,
                "score": answer_item.score,
                "record": None,
                "revise_suggestion": None,
            }
            answer_datas.append(answer_data)

    mold_dict = deepcopy(mold.to_dict())
    schema = mold_dict["data"]
    schema["version"] = mold_dict["checksum"]

    answer = {
        "userAnswer": {"items": answer_datas},
        "schema": schema,
    }

    return answer, mold


async def get_preset_answer_by_mid_qid(mold: NewMold, question: NewQuestion):
    questions = await NewQuestion.find_by_fid_mids(question.fid, [mold.id])
    answer_datas = []
    if not all(x.preset_answer for x in questions):
        return answer_datas, mold

    mold_reserved_fields = {mold.name: mold.data["schemas"][0]["orders"]}

    for question in questions:
        answer_reader = AnswerReader(question.preset_answer)
        for item in answer_reader.items:
            answer_item = AnswerItem(**item)
            if answer_item.first_level_field not in mold_reserved_fields.get(answer_reader.mold_name, []):
                continue

            if not answer_item.value:
                value = []
            elif isinstance(answer_item.value, str):
                value = [answer_item.value]
            elif isinstance(answer_item.value, (list, tuple)):
                value = answer_item.value
            else:
                raise ValueError(f"Invalid {answer_item.value}")

            answer_data = {
                "qid": question.id,
                "uid": answer_item.marker["id"] if answer_item.marker else None,
                "key": answer_item.key,
                "data": answer_item.data,
                "schema": answer_item.schema,
                "value": value,
                "text": answer_item.text,
                "score": answer_item.score,
                "record": None,
                "revise_suggestion": None,
            }
            answer_datas.append(answer_data)

    mold_dict = deepcopy(mold.to_dict())
    schema = mold_dict["data"]
    schema["version"] = mold_dict["checksum"]

    answer = {
        "userAnswer": {"items": answer_datas},
        "schema": schema,
    }

    return answer


async def get_question_answer_by_mid_qid(mold: NewMold, question: NewQuestion):
    if not mold or not question or mold.id != question.mold:
        return {
            "userAnswer": {"items": []},
            "schema": {},
        }
    data = await pw_db.execute(
        NewAnswerData.select().where(NewAnswerData.qid == question.id).order_by(NewAnswerData.id)
    )
    user_map = await NewAdminUser.get_user_name_map()
    mold_dict = deepcopy(mold.to_dict())
    schema = mold_dict["data"]
    schema["version"] = mold_dict["checksum"]

    return {
        "userAnswer": {"items": [x.to_dict(user_map=user_map) for x in data]},
        "schema": schema,
    }
