import json
import logging
import os
from collections import defaultdict, namedtuple
from copy import deepcopy
from typing import Optional

from attr import define, field
from glom import assign, glom
from speedy.peewee_plus.orm import and_ as peewee_and
from speedy.peewee_plus.orm import fn

from remarkable.answer.common import create_empty_answer, get_first_level_field, parse_path
from remarkable.answer.node import AnswerItem
from remarkable.answer.reader import AnswerReader
from remarkable.common.callback import http_post_callback
from remarkable.common.constants import AIStatus, AnswerStatus, QuestionStatus
from remarkable.common.enums import ClientName, QuestionAnswerType
from remarkable.common.multiprocess import run_in_multiprocess
from remarkable.common.schema import Schema
from remarkable.common.storage import localstorage
from remarkable.common.util import compact_dumps
from remarkable.config import get_config
from remarkable.converter.utils import generate_cache_for_diff, generate_customer_answer, push_answer_to_remote
from remarkable.db import IS_MYSQL, pw_db
from remarkable.models.new_file import NewFile
from remarkable.models.new_user import NewAdmin<PERSON>ser
from remarkable.optools.export_answers_with_format import format_answer_to_json_tree, get_annotation_answer
from remarkable.predictor.mold_schema import MoldSchema, SchemaItem
from remarkable.pw_models.answer_data import DEFAULT_FILE_ANSWER_MERGE_STRATEGY, NewAnswerData, NewAnswerDataStat
from remarkable.pw_models.model import MoldWithFK, NewAnswer, NewMold
from remarkable.pw_models.question import NewQuestion, QuestionWithFK
from remarkable.schema.answer import AnswerGroup
from remarkable.security import authtoken
from remarkable.service.cmfchina.util import sync_answer_data_stat
from remarkable.service.mold_field import MoldFieldService
from remarkable.service.new_mold import NewMoldService
from remarkable.service.statistics import save_stat_result

logger = logging.getLogger(__name__)


class NewQuestionService:
    @staticmethod
    def question_query_without_answer(
        file_ids: list[int] = None,
        uid: int = None,
        is_answered: bool = None,
        status: int = None,
    ):
        default_health = get_config("web.default_question_health") or 1

        cond = []
        if file_ids:
            cond.append(NewQuestion.fid.in_(file_ids))
        if is_answered:
            cond.append(NewQuestion.mark_uids.contains(uid))
        if status:
            cond.append(NewQuestion.status == status)

        question_fields = [
            NewQuestion.id,
            NewQuestion.fid,
            NewQuestion.name,
            NewQuestion.mold,
            NewQuestion.ai_status,
            NewQuestion.health,
            NewQuestion.updated_utc,
            NewQuestion.fill_in_user,
            NewQuestion.data_updated_utc,
            NewQuestion.fill_in_status,
            NewQuestion.progress,
            NewQuestion.status,
            NewQuestion.num,
            NewQuestion.mark_uids,
            NewQuestion.mark_users,
            fn.COALESCE(NewQuestion.origin_health, default_health).alias("origin_health"),
        ]

        query = (
            NewQuestion.select(*question_fields, NewMold.name.alias("mold_name"))
            .join(NewMold, on=(NewMold.id == NewQuestion.mold))
            .where(*cond)
        )

        return query

    @staticmethod
    async def update_markers(question: NewQuestion):
        mark_uids = []
        mark_users = []
        users = await pw_db.execute(NewAdminUser.select().where(NewAdminUser.deleted_utc == 0))
        users_map = {u.id: u.name for u in users}
        answers = await pw_db.execute(
            NewAnswer.select()
            .where(NewAnswer.qid == question.id, NewAnswer.status.in_((AnswerStatus.VALID, AnswerStatus.UNFINISHED)))
            .order_by(NewAnswer.updated_utc)
        )

        for answer in answers:
            mark_uids.append(answer.uid)
            mark_users.append(users_map.get(answer.uid, f"DELETED_{answer.uid}"))

        await question.update_(**{"mark_uids": mark_uids, "mark_users": mark_users})

    @staticmethod
    async def get_mark_summary(prj_id, tree_ids: list[int], mold_ids: list[int] | None = None):
        user_data = {}
        for user in await pw_db.execute(NewAdminUser.select()):
            user_data[user.id] = {"name": user.name, "login_count": user.login_count}

        cond = (NewFile.pid == prj_id) & (NewQuestion.status != QuestionStatus.TODO.value)

        if tree_ids is not None:
            cond &= NewFile.tree_id.in_(tree_ids)

        if mold_ids is not None:
            cond &= NewQuestion.mold.in_(mold_ids)

        records = await pw_db.execute(
            NewAnswer.select(NewAdminUser.id, fn.COUNT(NewAdminUser.id))
            .join(NewAdminUser, on=(NewAdminUser.id == NewAnswer.uid))
            .join(NewQuestion, on=(NewQuestion.id == NewAnswer.qid))
            .join(NewFile, on=(NewFile.id == NewQuestion.fid))
            .where(cond)
            .group_by(NewAdminUser.id)
            .tuples()
        )
        mark_summary = []
        for uid, markcount in records:
            name = ""
            login_count = -1
            if uid in user_data:
                name = user_data[uid]["name"]
                login_count = user_data[uid]["login_count"]
            mark_summary.append({"uid": uid, "markcount": markcount, "name": name, "login_count": login_count})
        return mark_summary

    @staticmethod
    async def get_question_ai_status(fid):
        questions: list[NewQuestion] = await NewQuestion.find_by_fid(fid)
        for status in (
            AIStatus.DISABLE,
            AIStatus.SKIP_PREDICT,
            AIStatus.TODO,
            AIStatus.DOING,
            AIStatus.FAILED,
            AIStatus.FINISH,
        ):
            for question in questions:
                if question.ai_status == status:
                    return status
        return AIStatus.FAILED

    @staticmethod
    def build_empty_answer(schema: SchemaItem, index: int = 0):
        empty_answer = create_empty_answer(schema, index)
        empty_answer["meta"] = {"placeholder": True}
        return empty_answer

    @classmethod
    def fill_group_with_fixed_length(cls, answer: dict, mold: NewMold, amount: int = 1, ignore_basic_type: bool = True):
        group_answers = {}
        items = glom(answer, "userAnswer.items")
        keys = set()
        basic_type_keys = set()
        for item in items:
            paths = parse_path(item["key"])
            if len(paths) == 2:
                basic_type_keys.add(f"{paths[1][0]}")
            elif len(paths) == 3:
                keys.add(item["key"])
                assign(group_answers, f"{paths[1][0]}.{paths[1][1]}.{paths[2][0]}", item, dict)
            else:
                raise Exception(f"Unsupported key: {item['key']}")

        # 如果分组数量等于0, 则生成一个空答案
        schema = MoldSchema(mold.data)
        for child in schema.root_schema.children:
            if not child.children:
                if child.name not in basic_type_keys and not ignore_basic_type:
                    items.append(cls.build_empty_answer(child, 0))
                continue

            group_answers.setdefault(
                child.name,
                {"0": {c.name: cls.build_empty_answer(c, 0) for c in child.children}},
            )
            for i in range(amount):
                if str(i) not in group_answers[child.name]:
                    # 如果分组数量少于amount, 则补全
                    group = group_answers[child.name][str(i)] = {
                        c.name: cls.build_empty_answer(c, i) for c in child.children
                    }
                else:
                    group = group_answers[child.name][str(i)]
                for c in child.children:
                    if c.name in group:
                        continue
                    group_answers[child.name][str(i)][c.name] = cls.build_empty_answer(c, i)
        items.extend(
            item
            for groups in group_answers.values()
            for group in groups.values()
            for item in group.values()
            if item["key"] not in keys
        )
        assign(answer, "userAnswer.items", items)

    @classmethod
    async def post_pipe(
        cls, qid, fid, file_meta_info, skip_hook: bool = False, triggered_by_predict: bool = False, **kwargs
    ):
        """
        Question.set_answer 执行完之后做的一些后续操作
        :return:
        """
        from remarkable.hooks import PredictFinishHook

        if not get_config("data_flow.post_pipe_after_preset", True):
            logging.info(f"Skip post_pipe after preset for question: {qid}")
            return

        logger.info(f"post_pipe after preset for question: {qid}")

        if get_config("data_flow.file_answer.generate"):
            logging.info(f"generate_file_answer for file: {fid}, question: {qid}")
            await cls.update_answer_data(
                fid=fid,
                qid=qid,
                merge_groups=ClientName.cmfchina != get_config("client.name"),
                merge_strategy=kwargs.get("file_answer_merge_strategy"),
            )

        push_predict = get_config("web.push_preset_answer") or False
        customer_answer = get_config("web.customer_answer") or False
        gen_diff_cache = get_config("web.gen_diff_cache") or False

        if push_predict:
            logging.info(f"push_preset_answer for file: {fid}, question: {qid}")
            await push_answer_to_remote(qid)

        if customer_answer:
            logging.info(f"generate_customer_answer for file: {fid}, question: {qid}")
            await generate_customer_answer(qid)
        question = await QuestionWithFK.get_by_id(qid, prefetch_queries=[MoldWithFK.select(), NewFile.select()])
        if not skip_hook:
            await PredictFinishHook(question).__call__()
        from remarkable.service.rule import (
            do_inspect_rule_pipe,
        )

        # 调用update_answer_data()后, file_answer已更新,审核结果也要更新
        await do_inspect_rule_pipe(
            fid,
            schema_id=question.mold.id,
            audit_preset_answer=triggered_by_predict
            and get_config("client.name") == ClientName.cmfchina
            and "cmfchina_checker" == get_config("inspector.package_name"),
        )

        if (file_meta_info or {}).get("annotation_callback"):
            logging.info(f"annotation_callback for file: {fid}, question: {qid}")
            await cls.annotation_post_processing(qid)

        if gen_diff_cache:
            logging.info(f"generate_cache_for_diff for file: {fid}, question: {qid}")
            await generate_cache_for_diff(qid)

    @staticmethod
    async def annotation_post_processing(qid):
        """标注完成后续处理：
        1. 重新预测
        2. 重新检查合规
        3. 标注结果推送
        """

        file = await NewFile.find_by_qid(qid)
        file_meta = file.meta_info or {}
        callback_url = file_meta.get("annotation_callback")
        answer_from = file_meta.get("answer_from", "user")
        answer_format = file_meta.get("answer_format", "json_tree")
        if not callback_url:
            logging.error(f"No callback url for file: {file.id}")
            return

        encode_url_for = file_meta.get("encode_url_for")
        if encode_url_for and get_config(f"app.auth.{encode_url_for}"):
            app_id = get_config(f"app.auth.{encode_url_for}.app_id")
            secret_key = get_config(f"app.auth.{encode_url_for}.secret_key")
            if app_id and secret_key:
                callback_url = authtoken.encode_url(callback_url, app_id, secret_key)
            else:
                logging.warning(f"Missing auth section for {encode_url_for}, please check the config file")

        answer_data = await get_annotation_answer(qid, answer_from)
        mold = await pw_db.scalar(NewQuestion.select(NewQuestion.mold).where(NewQuestion.id == qid))
        if answer_format == "json_tree":
            answer_data = format_answer_to_json_tree(answer_data, localstorage.mount(file.pdfinsight_path()))
        answer_data["checksum"] = file.hash
        answer_data["question_id"] = qid
        answer_data["schema_id"] = mold
        await http_post_callback(callback_url, json=answer_data)

    @classmethod
    async def update_answer_data(cls, fid, qid, merge_groups=True, merge_strategy=None):
        if merge_groups:
            if not merge_strategy:
                merge_strategy = DEFAULT_FILE_ANSWER_MERGE_STRATEGY
            logging.info(f"{merge_strategy=}")
            master_question = await NewQuestion.get_master_question(fid)
            if not master_question:
                logging.info(f"No master_question, no answer_data, fid: {fid}")
                return

            old_groups = await get_old_groups(master_question)
            new_groups = await get_new_groups(master_question)
            answer_datas = NewAnswerData.merge_groups(old_groups, new_groups, merge_strategy)
            for data in answer_datas:
                if isinstance(data.get("score"), (int, float)):
                    data["score"] = str(data["score"])
        else:
            answer_datas, master_question = await update_answer_data_by_mold_question(qid, fid)
            if answer_datas is None or master_question is None:
                return

        async with pw_db.atomic():
            await pw_db.execute(NewAnswerData.delete().where(NewAnswerData.qid == master_question.id))
            await pw_db.execute(NewAnswerDataStat.delete().where(NewAnswerDataStat.qid == master_question.id))
            if IS_MYSQL:
                await NewAnswerData.bulk_insert(answer_datas)
                ids = await pw_db.scalars(
                    NewAnswerData.select(NewAnswerData.id).where(NewAnswerData.qid == master_question.id)
                )
            else:
                ids = list(await NewAnswerData.bulk_insert(answer_datas, iter_ids=True))
            if ids:
                await sync_answer_data_stat(ids)


async def get_old_groups(master_question: NewQuestion):
    answer_data_groups = defaultdict(AnswerGroup)
    items = await NewAnswerData.find_by_kwargs(qid=master_question.id, delegate="all")
    for item in items:
        item = item.to_dict(exclude=["id", "created_utc", "updated_utc"])
        first_level_field = get_first_level_field(item["key"])
        group = answer_data_groups[first_level_field]
        group.items.append(item)
        group.manual = group.manual or bool(item["record"])

    return answer_data_groups


async def get_new_groups(master_question: NewQuestion):
    molds = await NewMoldService.get_related_molds(master_question.fid, master_question.mold)
    answer_data_groups = defaultdict(AnswerGroup)
    questions = await NewQuestion.find_by_fid_mids(master_question.fid, [x.id for x in molds])
    if not all(x.answer for x in questions):
        logging.info(f"Not all questions have answer, no answer_data, {master_question.fid=}")
        return answer_data_groups

    _, fixed_molds = NewMoldService.master_mold_with_merged_schemas(molds)
    mold_reserved_fields = {}
    for mold in fixed_molds:
        mold_reserved_fields[mold.name] = mold.data["schemas"][0]["orders"]

    for question in questions:
        answer_reader = AnswerReader(question.answer)
        for item in answer_reader.items:
            answer_item = AnswerItem(**item)
            if answer_item.first_level_field not in mold_reserved_fields.get(answer_reader.mold_name, []):
                continue

            if not answer_item.value:
                value = []
            elif isinstance(answer_item.value, str):
                value = [answer_item.value]
            elif isinstance(answer_item.value, (list, tuple)):
                value = answer_item.value
            else:
                raise ValueError(f"Invalid {answer_item.value}")

            answer_data = {
                "qid": master_question.id,
                "uid": answer_item.marker["id"] if answer_item.marker else None,
                "key": answer_item.key,
                "data": answer_item.data,
                "schema": answer_item.schema,
                "value": value,
                # "text": answer_item.text,
                "score": answer_item.score,
                "record": None,
                "revise_suggestion": None,
            }
            answer_data_groups[answer_item.first_level_field].items.append(answer_data)
    return answer_data_groups


async def update_answer_data_by_mold_question(qid, fid):
    # 多schema情况下，合并answer_data，会修改pid，造成根据模型统计时数据丢失, 根据各对应的qid存储各自的answer_data
    question = await NewQuestion.get_by_id(qid)
    if not question:
        logging.info(f"No question, no answer_data, fid: {fid}")
        return None, None
    mold = await pw_db.first(
        NewMold.select().join(NewQuestion, on=peewee_and(NewMold.id == NewQuestion.mold, NewQuestion.id == qid))
    )
    if not mold:
        logging.info(f"No mold, mold:{mold.id}")
        return None, None
    mold_reserved_fields = {mold.name: mold.data["schemas"][0]["orders"]}
    mold_field_mapping = await MoldFieldService.get_mold_field_uuid_path(mold.id)
    result = {compact_dumps(row.path): row.id for row in mold_field_mapping}
    answer_datas = []
    answer_reader = AnswerReader(question.answer)
    for item in answer_reader.items:
        answer_item = AnswerItem(**item)
        if answer_item.first_level_field not in mold_reserved_fields.get(answer_reader.mold_name, []):
            continue

        if not answer_item.value:
            value = []
        elif isinstance(answer_item.value, str):
            value = [answer_item.value]
        elif isinstance(answer_item.value, (list, tuple)):
            value = answer_item.value
        else:
            raise ValueError(f"Invalid {answer_item.value}")
        answer_data = {
            "qid": question.id,
            "uid": answer_item.marker["id"] if answer_item.marker else None,
            "key": answer_item.key,
            "data": answer_item.data,
            "schema": answer_item.schema,
            "value": value,
            # "text": answer_item.text,
            "score": answer_item.score,
            "record": None,
            "revise_suggestion": None,
            "mold_field_id": result.get(answer_item.md5),
            "indexes": answer_item.key_indices,
            "answer_type": QuestionAnswerType.preset_answer,
        }
        answer_datas.append(answer_data)
    return answer_datas, question


async def batch_preset(
    start,
    end,
    project=None,
    mold=None,
    overwrite=False,
    save=None,
    workers=0,
    headnum=10,
    vid=0,
    preset_path=None,
    tree_s=None,
    acid=None,
    ctx_method="fork",
    special_rules=None,
    files_ids=None,
):
    questions = await NewQuestion.list_by_range(
        start=start,
        end=end,
        project=project,
        mold=mold,
        have_preset_answer=None if overwrite else False,
        special_cols=["id", "fid"],
        files_ids=files_ids,
    )
    tasks = [
        (
            q.fid,
            q.id,
            vid,
            preset_path,
            special_rules,
        )
        for q in questions
    ]

    async with pw_db.atomic():
        run_in_multiprocess(run_preset_answer, tasks, workers=workers, ctx_method=ctx_method)
        if save:
            await save_stat_result(preset_path, headnum, mold, save, vid, tree_s, acid)


async def run_preset_answer(args):
    from remarkable.plugins.fileapi.predict import predict_answer

    fid, qid, vid, preset_path, special_rules = args
    question = await NewQuestion.find_by_id(qid)
    file = await NewFile.find_by_id(fid)
    logging.info(f"preset answer for file: {fid}")
    async with pw_db.atomic():
        answer = await predict_answer(question, vid, special_rules)
        await question.set_answer()
        await NewQuestionService.post_pipe(qid, fid, file.meta_info, triggered_by_predict=True)
        if preset_path and os.path.exists(preset_path):
            with open(os.path.join(preset_path, "%s.json" % fid), "w") as file_obj:
                json.dump(answer, file_obj)


def replace_answer_item(origin_answer, new_answer, special_rules):
    items = origin_answer["userAnswer"]["items"]
    items = [item for item in items if json.loads(item["key"])[1].split(":")[0] not in special_rules]
    origin_answer["userAnswer"]["items"] = items + new_answer["userAnswer"]["items"]
    return origin_answer


@define
class MixinSchema:
    mold: NewMold = field()
    schema: Schema = field(init=False)
    mold_schema: MoldSchema = field(init=False)

    def __attrs_post_init__(self):
        self.schema = Schema(self.mold.data)
        self.mold_schema = MoldSchema(self.mold.data)

    @classmethod
    async def initialize(cls, mid: int) -> "MixinSchema":
        mold = await NewMold.find_by_id(mid)
        assert mold, f"No mold found: {mid}"
        return cls(mold)


async def migrate_answers(
    question: NewQuestion,
    *,
    mixin_schema: Optional[MixinSchema] = None,
    overwrite=False,
    safe_mode=False,
    update_timestamp=True,
):
    assert question.mold, f"Question: {question.id}, fid: {question.fid} has no schema yet"
    MixinAnswer = namedtuple("Answer", ["id", "table", "col", "data"])
    answers = await NewAnswer.get_answers_by_qid(question.id)
    all_answers = [MixinAnswer._make([answer.id, "answer", "data", answer.data]) for answer in answers]
    if question.preset_answer:
        all_answers.append(MixinAnswer._make([question.id, "question", "preset_answer", question.preset_answer]))
    if question.answer:
        all_answers.append(MixinAnswer._make([question.id, "question", "answer", question.answer]))

    if not mixin_schema:
        mixin_schema = await MixinSchema.initialize(question.mold)

    for answer in all_answers:
        if answer.data["schema"]["version"] == mixin_schema.mold.checksum and not overwrite:
            logging.info("%s %s has same schema version", answer.table, answer.id)
            continue

        items_to_delete = []
        items_to_remain = []
        for item in answer.data.get("userAnswer", {}).get("items", []):
            need_remain, key_str = mixin_schema.schema.contains_path(item["key"], skip_root=True)
            if need_remain:
                item["key"] = key_str
                # 更新可能的类型变化
                item["schema"] = mixin_schema.mold_schema.find_schema_by_path(key_str).to_answer_data()
                items_to_remain.append(item)
            else:
                items_to_delete.append(item)
        logging.info(
            "%s %s wants to remove items:%s",
            answer.table,
            answer.id,
            "\n".join([item["key"] for item in items_to_delete]),
        )
        logging.info(f"{len(items_to_remain)} items remain")
        if safe_mode:
            logging.warning("running in safe mode, pass this migration")
            continue
        answer.data["userAnswer"]["items"] = items_to_remain
        logging.info(f"{len(items_to_delete)} items deleted")
        answer.data["schema"] = deepcopy(mixin_schema.mold.data)
        answer.data["schema"]["version"] = mixin_schema.mold.checksum
        if answer.table == "answer":
            await NewAnswer.update_by_pk(answer.id, update_timestamp=update_timestamp, data=answer.data)
        if answer.table == "question":
            await NewQuestion.update_by_pk(answer.id, update_timestamp=update_timestamp, **{answer.col: answer.data})
        logging.info("update %s %s schema to version %s", answer.table, answer.id, mixin_schema.mold.checksum)
