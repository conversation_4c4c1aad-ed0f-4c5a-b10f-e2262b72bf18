@startuml cmfchina

!theme plain
top to bottom direction
skinparam linetype ortho

entity "admin_user" as admin_user {
    | 字段名 | 类型 | 长度 | 注释 |
    | id | integer | - | 用户ID |
    | name | text | - | 用户名 |
    | password | text | - | 用户密码 |
    | salt | text | - | 密码盐 |
    | permission | json | - | 用户权限 |
    | login_utc | integer | - | 最后登录时间（UTC） |
    | login_count | integer | - | 登录次数 |
    | created_utc | integer | - | 创建时间（UTC） |
    | updated_utc | integer | - | 更新时间（UTC） |
    | deleted_utc | integer | - | 删除时间（UTC） |
}

entity "file" as file {
    | 字段名 | 类型 | 长度 | 注释 |
    | id | integer | - | 文件ID |
    | name | text | - | 文件名 |
    | hash | text | - | 文件哈希值 |
    | size | integer | - | 文件大小 |
    | page | integer | - | 页数 |
    | pdf_parse_status | integer | - | PDF解析状态 |
    | uid | integer | - | 用户ID |
    | task_id | integer | - | 任务ID |
    | meta_info | json | - | 元信息 |
    | priority | integer | - | 优先级 |
    | created_utc | integer | - | 创建时间（UTC） |
    | updated_utc | integer | - | 更新时间（UTC） |
    | deleted_utc | integer | - | 删除时间（UTC） |
}

entity "mold" as mold {
    | 字段名 | 类型 | 长度 | 注释 |
    | id | integer | - | 场景ID |
    | name | text | - | 场景名称 |
    | data | json | - | 场景数据 |
    | created_utc | integer | - | 创建时间（UTC） |
    | updated_utc | integer | - | 更新时间（UTC） |
    | deleted_utc | integer | - | 删除时间（UTC） |
    | mold_type | integer | - | 场景类型 |
    | uid | integer | - | 用户ID |
    | public | bool | - | 是否公开 |
    | meta | json | - | 元数据 |
    | master | integer | - | 是否是主场景 |
}

entity "question" as question {
    | 字段名 | 类型 | 长度 | 注释 |
    | id | integer | - | 问题ID |
    | created_utc | integer | - | 创建时间（UTC） |
    | updated_utc | integer | - | 更新时间（UTC） |
    | deleted_utc | integer | - | 删除时间（UTC） |
    | ai_status | integer | - | 预测状态 |
    | answer | json | - | 预测答案 |
    | mold | integer | - | 模型ID |
    | fid | integer | - | 文件ID |
}



entity "file_answer" as file_answer {
   | 字段名 | 类型 | 长度 | 注释 |
   | fid | integer | - | 文件ID |
   | status | integer | - | 比对状态 |
   | schema | jsonb | - | 比对模式 |
   | answer | jsonb | - | 比对答案 |
   | created_utc | integer | - | 创建时间（UTC） |
   | updated_utc | integer | - | 更新时间（UTC） |
   | id | integer | - | 主键ID |
}

entity "audit_result" as audit_result {
   | 字段名 | 类型 | 长度 | 注释 |
   | fid | integer | - | 文件ID |
   | is_compliance | bool | - | 是否合规 |
   | reasons | jsonb | - | 原因 |
   | suggestion | jsonb | - | 建议 |
   | created_utc | integer | - | 创建时间（UTC） |
   | updated_utc | integer | - | 更新时间（UTC） |
   | id | integer | - | 主键ID |
}


entity "model" as model {
   | 字段名 | 类型 | 长度 | 注释 |
   | name | text | - | 名称 |
   | address | text | - | 模型地址 |
   | intro | text | - | 简介 |
   | usage | text | - | 使用方式 |
   | created_utc | integer | - | 创建时间（UTC） |
   | updated_utc | integer | - | 更新时间（UTC） |
   | id | integer | - | 主键ID |
}

entity "mold_model_ref" as mold_model_ref {
   | 字段名 | 类型 | 长度 | 注释 |
   | mold_id | integer | - | 场景 |
   | model_id | integer | - | 模型 |
   | enable | bool | - | 是否启用 |
   | created_utc | integer | - | 创建时间（UTC） |
   | id | integer | - | 主键ID |
}


question }o--|| file : "fid"
audit_result }o--|| file : "fid"
file }o--|| admin_user : "uid"
question }o--|| mold : "mold"
mold }o--|| admin_user : "uid"
file_answer }o--|| file : "fid"
model }o--|| mold_model_ref : "model_id"
mold }o--|| mold_model_ref : "mold_id"

@enduml