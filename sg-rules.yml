# ast-grep rule file

---
id: mysql-string-column
language: Python
message: "sa.String in Column should specify length parameter for MySQL compatibility"
severity: error
rule:
  any:
    - pattern: sa.Column($NAME, sa.String())
    - pattern: sa.Column($NAME, sa.String)
    - pattern: sa.Column($NAME, sa.String, $$$OPTIONS)
    - pattern: sa.Column($NAME, sa.String(), $$$OPTIONS)
# Examples:
# sa.Column("name", sa.String())
# sa.Column("name", sa.String)
# sa.Column("name", sa.String, nullable=False)
# sa.Column("name", sa.String(), nullable=False)

---
id: mysql-string-assignment
language: Python
message: "sa.String assignment should specify length parameter for MySQL compatibility"
severity: error
rule:
  any:
    - pattern: $VAR = sa.Column(sa.String)
    - pattern: $VAR = sa.Column(sa.String())
    - pattern: $VAR = sa.Column(sa.String, $$$OPTIONS)
    - pattern: $VAR = sa.Column(sa.String(), $$$OPTIONS)
# Examples:
# name = sa.Column(sa.String)
# name = sa.Column(sa.String())
# name = sa.Column(sa.String, nullable=False)
# name = sa.Column(sa.String(), nullable=False)

---
id: mysql-string-add-column
language: Python
message: "sa.String in add_column should specify length parameter for MySQL compatibility"
severity: error
rule:
  any:
    - pattern: op.add_column($TABLE, sa.Column($NAME, sa.String))
    - pattern: op.add_column($TABLE, sa.Column($NAME, sa.String()))
    - pattern: op.add_column($TABLE, sa.Column($NAME, sa.String, $$$OPTIONS))
    - pattern: op.add_column($TABLE, sa.Column($NAME, sa.String(), $$$OPTIONS))
# Examples:
# op.add_column("users", sa.Column("name", sa.String))
# op.add_column("users", sa.Column("name", sa.String()))
# op.add_column("users", sa.Column("name", sa.String, nullable=False))
# op.add_column("users", sa.Column("name", sa.String(), nullable=False))

---
id: mysql-string-array
language: Python
message: "create_array_field should be user MySQL ARRAY compatibility"
severity: error
note: |
  For MySQL compatibility, sa.ARRAY should using create_array_field()

  from remarkable.common.migrate_util which automatically
  handles MySQL compatibility by converting sa.ARRAY to sa.JSON in MySQL environments:
  create_array_field("field_name", sa.ARRAY(sa.String), nullable=True)
rule:
  all:
    - pattern: sa.ARRAY($$$)
    - not:
        inside:
          pattern: create_array_field($$$)
          stopBy: end
# This rule detects sa.ARRAY(sa.String) usage without length parameter for MySQL compatibility
#
# Examples that WILL trigger this rule:
# - sa.ARRAY(sa.*)
# - sa.Column("field", sa.ARRAY(sa.String))
# - sa.ARRAY(sa.String(255))
# - op.add_column("table", sa.Column("field", sa.ARRAY(sa.String)))
#
# Examples that will NOT trigger this rule (correctly excluded):
# - create_array_field("field_name", sa.ARRAY(sa.String), nullable=True)
# - create_array_field("field_name", sa.ARRAY(sa.String()))
#
# Note: create_array_field() automatically handles MySQL compatibility by converting
# sa.ARRAY to sa.JSON in MySQL environments, so these cases are safely excluded.
