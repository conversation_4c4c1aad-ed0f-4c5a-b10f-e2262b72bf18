version = 1
revision = 3
requires-python = "==3.12.*"
resolution-markers = [
    "platform_machine == 'aarch64' and sys_platform == 'linux'",
    "platform_machine != 'aarch64' and sys_platform == 'linux'",
    "sys_platform == 'darwin'",
    "sys_platform != 'darwin' and sys_platform != 'linux'",
]

[[package]]
name = "a2wsgi"
version = "1.10.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ce/b0/93505ed5a34527c9800a87aae468e8ce9a3c5d0de11acb54bf43c2642dba/a2wsgi-1.10.0.tar.gz", hash = "sha256:c80eea2b752eda41216c645b82a43a62f6006c6336ef31a7db1403399edf7c16", size = 17811, upload-time = "2023-12-28T02:41:06.803Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f3/79/f11e78444458d7c3734619481e62c49397e237bdc2faa2d405999b45b49a/a2wsgi-1.10.0-py3-none-any.whl", hash = "sha256:f55c7322dccdb73f8198da898c2622c1afc2af0cffaa105a710c87e3457fedfd", size = 16577, upload-time = "2023-12-28T02:41:04.974Z" },
]

[[package]]
name = "aenum"
version = "3.1.16"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e3/52/6ad8f63ec8da1bf40f96996d25d5b650fdd38f5975f8c813732c47388f18/aenum-3.1.16-py3-none-any.whl", hash = "sha256:9035092855a98e41b66e3d0998bd7b96280e85ceb3a04cc035636138a1943eaf", size = 165627, upload-time = "2025-04-25T03:17:58.89Z" },
]

[[package]]
name = "aiofile"
version = "3.9.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "caio" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/67/e2/d7cb819de8df6b5c1968a2756c3cb4122d4fa2b8fc768b53b7c9e5edb646/aiofile-3.9.0.tar.gz", hash = "sha256:e5ad718bb148b265b6df1b3752c4d1d83024b93da9bd599df74b9d9ffcf7919b", size = 17943, upload-time = "2024-10-08T10:39:35.846Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/25/da1f0b4dd970e52bf5a36c204c107e11a0c6d3ed195eba0bfbc664c312b2/aiofile-3.9.0-py3-none-any.whl", hash = "sha256:ce2f6c1571538cbdfa0143b04e16b208ecb0e9cb4148e528af8a640ed51cc8aa", size = 19539, upload-time = "2024-10-08T10:39:32.955Z" },
]

[[package]]
name = "aiofiles"
version = "24.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/03/a88171e277e8caa88a4c77808c20ebb04ba74cc4681bf1e9416c862de237/aiofiles-24.1.0.tar.gz", hash = "sha256:22a075c9e5a3810f0c2e48f3008c94d68c65d763b9b03857924c99e57355166c", size = 30247, upload-time = "2024-06-24T11:02:03.584Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl", hash = "sha256:b4ec55f4195e3eb5d7abd1bf7e061763e864dd4954231fb8539a0ef8bb8260e5", size = 15896, upload-time = "2024-06-24T11:02:01.529Z" },
]

[[package]]
name = "aiomysql"
version = "0.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pymysql" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/67/76/2c5b55e4406a1957ffdfd933a94c2517455291c97d2b81cec6813754791a/aiomysql-0.2.0.tar.gz", hash = "sha256:558b9c26d580d08b8c5fd1be23c5231ce3aeff2dadad989540fee740253deb67", size = 114706, upload-time = "2023-06-11T19:57:53.608Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/42/87/c982ee8b333c85b8ae16306387d703a1fcdfc81a2f3f15a24820ab1a512d/aiomysql-0.2.0-py3-none-any.whl", hash = "sha256:b7c26da0daf23a5ec5e0b133c03d20657276e4eae9b73e040b72787f6f6ade0a", size = 44215, upload-time = "2023-06-11T19:57:51.09Z" },
]

[[package]]
name = "aiopg"
version = "1.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "async-timeout" },
    { name = "psycopg2-binary" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b0/0a/aba75a9ffcb1704b98c39986344230eaa70c40ac28e5ca635df231db912f/aiopg-1.4.0.tar.gz", hash = "sha256:116253bef86b4d954116716d181e9a0294037f266718b2e1c9766af995639d71", size = 35593, upload-time = "2022-10-26T09:31:49.478Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9b/2f/ab8690bf995171b9a8b60b98a2ca91d4108a42422abf10bf622397437d26/aiopg-1.4.0-py3-none-any.whl", hash = "sha256:aea46e8aff30b039cfa818e6db4752c97656e893fc75e5a5dc57355a9e9dedbd", size = 34770, upload-time = "2022-10-26T09:31:48.019Z" },
]

[[package]]
name = "aipod"
version = "1.3.4"
source = { registry = "http://**********:3141/cheftin/pypi" }
dependencies = [
    { name = "filelock" },
    { name = "filetype" },
    { name = "grpcio-health-checking" },
    { name = "grpcio-reflection" },
    { name = "grpcio-tools" },
    { name = "psutil" },
    { name = "zstandard" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/768/943fa3f3d6b9f/aipod-1.3.4-py3-none-any.whl", hash = "sha256:768943fa3f3d6b9fed294d44b72358bd6bf483e448d034ed7d3b194ccb9fe8fd" },
]

[[package]]
name = "alembic"
version = "1.8.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "mako" },
    { name = "sqlalchemy" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/37/ab/80e6d86ca81235ea1a7104089dddf74de4b45f8af0a05d4b265be44d6ff9/alembic-1.8.1.tar.gz", hash = "sha256:cd0b5e45b14b706426b833f06369b9a6d5ee03f826ec3238723ce8caaf6e5ffa", size = 1255927, upload-time = "2022-07-13T14:18:50.766Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b3/c8/69600a8138a56794713ecdb8b75b14fbe32a410bc444683f27dbab93c0ca/alembic-1.8.1-py3-none-any.whl", hash = "sha256:0a024d7f2de88d738d7395ff866997314c837be6104e90c5724350313dee4da4", size = 209845, upload-time = "2022-07-13T14:18:53.415Z" },
]

[[package]]
name = "amqp"
version = "5.3.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "vine" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/79/fc/ec94a357dfc6683d8c86f8b4cfa5416a4c36b28052ec8260c77aca96a443/amqp-5.3.1.tar.gz", hash = "sha256:cddc00c725449522023bad949f70fff7b48f0b1ade74d170a6f10ab044739432", size = 129013, upload-time = "2024-11-12T19:55:44.051Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/99/fc813cd978842c26c82534010ea849eee9ab3a13ea2b74e95cb9c99e747b/amqp-5.3.1-py3-none-any.whl", hash = "sha256:43b3319e1b4e7d1251833a93d672b4af1e40f3d632d479b98661a95f117880a2", size = 50944, upload-time = "2024-11-12T19:55:41.782Z" },
]

[[package]]
name = "annotated-types"
version = "0.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ee/67/531ea369ba64dcff5ec9c3402f9f51bf748cec26dde048a2f973a4eea7f5/annotated_types-0.7.0.tar.gz", hash = "sha256:aff07c09a53a08bc8cfccb9c85b05f1aa9a2a6f23728d790723543408344ce89", size = 16081, upload-time = "2024-05-20T21:33:25.928Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl", hash = "sha256:1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53", size = 13643, upload-time = "2024-05-20T21:33:24.1Z" },
]

[[package]]
name = "anyio"
version = "4.10.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "idna" },
    { name = "sniffio" },
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f1/b4/636b3b65173d3ce9a38ef5f0522789614e590dab6a8d505340a4efe4c567/anyio-4.10.0.tar.gz", hash = "sha256:3f3fae35c96039744587aa5b8371e7e8e603c0702999535961dd336026973ba6", size = 213252, upload-time = "2025-08-04T08:54:26.451Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6f/12/e5e0282d673bb9746bacfb6e2dba8719989d3660cdb2ea79aee9a9651afb/anyio-4.10.0-py3-none-any.whl", hash = "sha256:60e474ac86736bbfd6f210f7a61218939c318f43f9972497381f1c5e930ed3d1", size = 107213, upload-time = "2025-08-04T08:54:24.882Z" },
]

[[package]]
name = "apispec"
version = "6.8.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "packaging" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c0/21/2b86a14a01c800308226c018fa489991cc40ffc227954d4f59d8a4090477/apispec-6.8.2.tar.gz", hash = "sha256:ce5b69b9fcf0250cb56ba0c1a52a75ff22c2f7c586654e57884399018c519f26", size = 77148, upload-time = "2025-05-12T14:57:52.649Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f7/86/a2e7e0f6e9a82023108f52014eab389b184a06da486139fc5436d50b08d0/apispec-6.8.2-py3-none-any.whl", hash = "sha256:43c52ab6aa7d4056c1dfc6c81310c659b29f4db5858b3b4351819b77d3a1afff", size = 30508, upload-time = "2025-05-12T14:57:49.546Z" },
]

[[package]]
name = "astroid"
version = "3.3.11"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/74/dfb75f9ccd592bbedb175d4a32fc643cf569d7c218508bfbd6ea7ef9c091/astroid-3.3.11.tar.gz", hash = "sha256:1e5a5011af2920c7c67a53f65d536d65bfa7116feeaf2354d8b94f29573bb0ce", size = 400439, upload-time = "2025-07-13T18:04:23.177Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/af/0f/3b8fdc946b4d9cc8cc1e8af42c4e409468c84441b933d037e101b3d72d86/astroid-3.3.11-py3-none-any.whl", hash = "sha256:54c760ae8322ece1abd213057c4b5bba7c49818853fc901ef09719a60dbf9dec", size = 275612, upload-time = "2025-07-13T18:04:21.07Z" },
]

[[package]]
name = "async-timeout"
version = "4.0.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/87/d6/21b30a550dafea84b1b8eee21b5e23fa16d010ae006011221f33dcd8d7f8/async-timeout-4.0.3.tar.gz", hash = "sha256:4640d96be84d82d02ed59ea2b7105a0f7b33abe8703703cd0ab0bf87c427522f", size = 8345, upload-time = "2023-08-10T16:35:56.907Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/fa/e01228c2938de91d47b307831c62ab9e4001e747789d0b05baf779a6488c/async_timeout-4.0.3-py3-none-any.whl", hash = "sha256:7405140ff1230c310e51dc27b3145b9092d659ce68ff733fb0cefe3ee42be028", size = 5721, upload-time = "2023-08-10T16:35:55.203Z" },
]

[[package]]
name = "asyncio-pool"
version = "0.6.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/99/11/65f0a225cb01ddd3782dcc0581085f10c9b3215bb911e6f66ff23053bc80/asyncio_pool-0.6.0.tar.gz", hash = "sha256:d7ba5e299ba58d4fb0cebbc722989d1f880df4c4b19e37055075b3dabc062c5b", size = 10206, upload-time = "2022-05-21T10:34:26.356Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/13/b7/b22e41f2f3044da160a664ab74c337da876009ea8809318623ef10120904/asyncio_pool-0.6.0-py3-none-any.whl", hash = "sha256:bf4417be93c2776262d93decabbbd633579f7610947fb73d80857823689e1455", size = 8524, upload-time = "2022-05-21T10:34:24.569Z" },
]

[[package]]
name = "asyncpg"
version = "0.29.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c1/11/7a6000244eaeb6b8ed2238bf33477c486515d6133f2c295913aca3ba4a00/asyncpg-0.29.0.tar.gz", hash = "sha256:d1c49e1f44fffafd9a55e1a9b101590859d881d639ea2922516f5d9c512d354e", size = 820455, upload-time = "2023-11-05T05:59:10.879Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/b7/38b7c195f66a5598413c538da499b3f8119ba5764ded6fff620f7eb84c65/asyncpg-0.29.0-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:6011b0dc29886ab424dc042bf9eeb507670a3b40aece3439944006aafe023178", size = 636282, upload-time = "2023-11-05T05:58:18.594Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/eb/0b/d128b57f7e994a6d71253d0a6a8c949fc50c969785010d46b87d8491be24/asyncpg-0.29.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:b544ffc66b039d5ec5a7454667f855f7fec08e0dfaf5a5490dfafbb7abbd2cfb", size = 618024, upload-time = "2023-11-05T05:58:20.55Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/49/ac/0396e559e1e7ab23787f790ae96b22affe2d66acebb084d6fc42293d12b8/asyncpg-0.29.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d84156d5fb530b06c493f9e7635aa18f518fa1d1395ef240d211cb563c4e2364", size = 3196465, upload-time = "2023-11-05T05:58:22.559Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/99/38/0bfb00e9b828513bd759174860fd2b1c5e36d0b33985c90ff4ed6f96814c/asyncpg-0.29.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:54858bc25b49d1114178d65a88e48ad50cb2b6f3e475caa0f0c092d5f527c106", size = 3275564, upload-time = "2023-11-05T05:58:24.888Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/1b/bb42784e9895832bf460ee6643f818bd53e4d6a6308cca5984c581a51845/asyncpg-0.29.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:bde17a1861cf10d5afce80a36fca736a86769ab3579532c03e45f83ba8a09c59", size = 3164724, upload-time = "2023-11-05T05:58:27.368Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d5/d1/7ed5169e30e80573c942f5a6f29b2f87d5b8379bdd9bd916f0ed136c874e/asyncpg-0.29.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:37a2ec1b9ff88d8773d3eb6d3784dc7e3fee7756a5317b67f923172a4748a175", size = 3252834, upload-time = "2023-11-05T05:58:30.068Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/91/2e/20e024608c57c2099531ba492c761b12fdd80891a67e58c92de44d05d57e/asyncpg-0.29.0-cp312-cp312-win32.whl", hash = "sha256:bb1292d9fad43112a85e98ecdc2e051602bce97c199920586be83254d9dafc02", size = 487254, upload-time = "2023-11-05T05:58:32.517Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/71/86/7a18e1a457afb73991e5e5586e2341af09a31c91d8f65cc003f0b4553252/asyncpg-0.29.0-cp312-cp312-win_amd64.whl", hash = "sha256:2245be8ec5047a605e0b454c894e54bf2ec787ac04b1cb7e0d3c67aa1e32f0fe", size = 530253, upload-time = "2023-11-05T05:58:34.273Z" },
]

[[package]]
name = "attrs"
version = "21.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d7/77/ebb15fc26d0f815839ecd897b919ed6d85c050feeb83e100e020df9153d2/attrs-21.4.0.tar.gz", hash = "sha256:626ba8234211db98e869df76230a137c4c40a12d72445c45d5f5b716f076e2fd", size = 201839, upload-time = "2021-12-29T13:15:09.056Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/be/be/7abce643bfdf8ca01c48afa2ddf8308c2308b0c3b239a44e57d020afa0ef/attrs-21.4.0-py2.py3-none-any.whl", hash = "sha256:2d27e3784d7a565d36ab851fe94887c5eccd6a463168875832a1be79c82828b4", size = 60567, upload-time = "2021-12-29T13:15:06.703Z" },
]

[[package]]
name = "babel"
version = "2.9.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pytz" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/17/e6/ec9aa6ac3d00c383a5731cc97ed7c619d3996232c977bb8326bcbb6c687e/Babel-2.9.1.tar.gz", hash = "sha256:bc0c176f9f6a994582230df350aa6e05ba2ebe4b3ac317eab29d9be5d2768da0", size = 8683505, upload-time = "2021-04-28T19:31:41.723Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/aa/96/4ba93c5f40459dc850d25f9ba93f869a623e77aaecc7a9344e19c01942cf/Babel-2.9.1-py2.py3-none-any.whl", hash = "sha256:ab49e12b91d937cd11f0b67cb259a57ab4ad2b59ac7a3b41d6c06c0ac5b0def9", size = 8832555, upload-time = "2021-04-28T19:31:38.181Z" },
]

[[package]]
name = "beautifulsoup4"
version = "4.13.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "soupsieve" },
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/e4/0c4c39e18fd76d6a628d4dd8da40543d136ce2d1752bd6eeeab0791f4d6b/beautifulsoup4-4.13.4.tar.gz", hash = "sha256:dbb3c4e1ceae6aefebdaf2423247260cd062430a410e38c66f2baa50a8437195", size = 621067, upload-time = "2025-04-15T17:05:13.836Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/cd/30110dc0ffcf3b131156077b90e9f60ed75711223f306da4db08eff8403b/beautifulsoup4-4.13.4-py3-none-any.whl", hash = "sha256:9bbbb14bfde9d79f38b8cd5f8c7c85f4b8f2523190ebed90e950a8dea4cb1c4b", size = 187285, upload-time = "2025-04-15T17:05:12.221Z" },
]

[[package]]
name = "billiard"
version = "4.2.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7c/58/1546c970afcd2a2428b1bfafecf2371d8951cc34b46701bea73f4280989e/billiard-4.2.1.tar.gz", hash = "sha256:12b641b0c539073fc8d3f5b8b7be998956665c4233c7c1fcd66a7e677c4fb36f", size = 155031, upload-time = "2024-09-21T13:40:22.491Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/30/da/43b15f28fe5f9e027b41c539abc5469052e9d48fd75f8ff094ba2a0ae767/billiard-4.2.1-py3-none-any.whl", hash = "sha256:40b59a4ac8806ba2c2369ea98d876bc6108b051c227baffd928c644d15d8f3cb", size = 86766, upload-time = "2024-09-21T13:40:20.188Z" },
]

[[package]]
name = "boltons"
version = "25.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/63/54/71a94d8e02da9a865587fb3fff100cb0fc7aa9f4d5ed9ed3a591216ddcc7/boltons-25.0.0.tar.gz", hash = "sha256:e110fbdc30b7b9868cb604e3f71d4722dd8f4dcb4a5ddd06028ba8f1ab0b5ace", size = 246294, upload-time = "2025-02-03T05:57:59.129Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/45/7f/0e961cf3908bc4c1c3e027de2794f867c6c89fb4916fc7dba295a0e80a2d/boltons-25.0.0-py3-none-any.whl", hash = "sha256:dc9fb38bf28985715497d1b54d00b62ea866eca3938938ea9043e254a3a6ca62", size = 194210, upload-time = "2025-02-03T05:57:56.705Z" },
]

[[package]]
name = "brotli"
version = "1.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2f/c2/f9e977608bdf958650638c3f1e28f85a1b075f075ebbe77db8555463787b/Brotli-1.1.0.tar.gz", hash = "sha256:81de08ac11bcb85841e440c13611c00b67d3bf82698314928d0b676362546724", size = 7372270, upload-time = "2023-09-07T14:05:41.643Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5c/d0/5373ae13b93fe00095a58efcbce837fd470ca39f703a235d2a999baadfbc/Brotli-1.1.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:32d95b80260d79926f5fab3c41701dbb818fde1c9da590e77e571eefd14abe28", size = 815693, upload-time = "2024-10-18T12:32:23.824Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8e/48/f6e1cdf86751300c288c1459724bfa6917a80e30dbfc326f92cea5d3683a/Brotli-1.1.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:b760c65308ff1e462f65d69c12e4ae085cff3b332d894637f6273a12a482d09f", size = 422489, upload-time = "2024-10-18T12:32:25.641Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/06/88/564958cedce636d0f1bed313381dfc4b4e3d3f6015a63dae6146e1b8c65c/Brotli-1.1.0-cp312-cp312-macosx_10_9_universal2.whl", hash = "sha256:316cc9b17edf613ac76b1f1f305d2a748f1b976b033b049a6ecdfd5612c70409", size = 873081, upload-time = "2023-09-07T14:03:57.967Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/58/79/b7026a8bb65da9a6bb7d14329fd2bd48d2b7f86d7329d5cc8ddc6a90526f/Brotli-1.1.0-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:caf9ee9a5775f3111642d33b86237b05808dafcd6268faa492250e9b78046eb2", size = 446244, upload-time = "2023-09-07T14:03:59.319Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e5/18/c18c32ecea41b6c0004e15606e274006366fe19436b6adccc1ae7b2e50c2/Brotli-1.1.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:70051525001750221daa10907c77830bc889cb6d865cc0b813d9db7fefc21451", size = 2906505, upload-time = "2023-09-07T14:04:01.327Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/08/c8/69ec0496b1ada7569b62d85893d928e865df29b90736558d6c98c2031208/Brotli-1.1.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:7f4bf76817c14aa98cc6697ac02f3972cb8c3da93e9ef16b9c66573a68014f91", size = 2944152, upload-time = "2023-09-07T14:04:03.033Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ab/fb/0517cea182219d6768113a38167ef6d4eb157a033178cc938033a552ed6d/Brotli-1.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d0c5516f0aed654134a2fc936325cc2e642f8a0e096d075209672eb321cff408", size = 2919252, upload-time = "2023-09-07T14:04:04.675Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c7/53/73a3431662e33ae61a5c80b1b9d2d18f58dfa910ae8dd696e57d39f1a2f5/Brotli-1.1.0-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:6c3020404e0b5eefd7c9485ccf8393cfb75ec38ce75586e046573c9dc29967a0", size = 2845955, upload-time = "2023-09-07T14:04:06.585Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/55/ac/bd280708d9c5ebdbf9de01459e625a3e3803cce0784f47d633562cf40e83/Brotli-1.1.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:4ed11165dd45ce798d99a136808a794a748d5dc38511303239d4e2363c0695dc", size = 2914304, upload-time = "2023-09-07T14:04:08.668Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/76/58/5c391b41ecfc4527d2cc3350719b02e87cb424ef8ba2023fb662f9bf743c/Brotli-1.1.0-cp312-cp312-musllinux_1_1_i686.whl", hash = "sha256:4093c631e96fdd49e0377a9c167bfd75b6d0bad2ace734c6eb20b348bc3ea180", size = 2814452, upload-time = "2023-09-07T14:04:10.736Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c7/4e/91b8256dfe99c407f174924b65a01f5305e303f486cc7a2e8a5d43c8bec3/Brotli-1.1.0-cp312-cp312-musllinux_1_1_ppc64le.whl", hash = "sha256:7e4c4629ddad63006efa0ef968c8e4751c5868ff0b1c5c40f76524e894c50248", size = 2938751, upload-time = "2023-09-07T14:04:12.875Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5a/a6/e2a39a5d3b412938362bbbeba5af904092bf3f95b867b4a3eb856104074e/Brotli-1.1.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:861bf317735688269936f755fa136a99d1ed526883859f86e41a5d43c61d8966", size = 2933757, upload-time = "2023-09-07T14:04:14.551Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/13/f0/358354786280a509482e0e77c1a5459e439766597d280f28cb097642fc26/Brotli-1.1.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:87a3044c3a35055527ac75e419dfa9f4f3667a1e887ee80360589eb8c90aabb9", size = 2936146, upload-time = "2024-10-18T12:32:27.257Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/80/f7/daf538c1060d3a88266b80ecc1d1c98b79553b3f117a485653f17070ea2a/Brotli-1.1.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:c5529b34c1c9d937168297f2c1fde7ebe9ebdd5e121297ff9c043bdb2ae3d6fb", size = 2848055, upload-time = "2024-10-18T12:32:29.376Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ad/cf/0eaa0585c4077d3c2d1edf322d8e97aabf317941d3a72d7b3ad8bce004b0/Brotli-1.1.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:ca63e1890ede90b2e4454f9a65135a4d387a4585ff8282bb72964fab893f2111", size = 3035102, upload-time = "2024-10-18T12:32:31.371Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/63/1c1585b2aa554fe6dbce30f0c18bdbc877fa9a1bf5ff17677d9cca0ac122/Brotli-1.1.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e79e6520141d792237c70bcd7a3b122d00f2613769ae0cb61c52e89fd3443839", size = 2930029, upload-time = "2024-10-18T12:32:33.293Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5f/3b/4e3fd1893eb3bbfef8e5a80d4508bec17a57bb92d586c85c12d28666bb13/Brotli-1.1.0-cp312-cp312-win32.whl", hash = "sha256:5f4d5ea15c9382135076d2fb28dde923352fe02951e66935a9efaac8f10e81b0", size = 333276, upload-time = "2023-09-07T14:04:16.49Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3d/d5/942051b45a9e883b5b6e98c041698b1eb2012d25e5948c58d6bf85b1bb43/Brotli-1.1.0-cp312-cp312-win_amd64.whl", hash = "sha256:906bc3a79de8c4ae5b86d3d75a8b77e44404b0f4261714306e3ad248d8ab0951", size = 357255, upload-time = "2023-09-07T14:04:17.83Z" },
]

[[package]]
name = "brotlicffi"
version = "1.1.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/95/9d/70caa61192f570fcf0352766331b735afa931b4c6bc9a348a0925cc13288/brotlicffi-1.1.0.0.tar.gz", hash = "sha256:b77827a689905143f87915310b93b273ab17888fd43ef350d4832c4a71083c13" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/11/7b96009d3dcc2c931e828ce1e157f03824a69fb728d06bfd7b2fc6f93718/brotlicffi-1.1.0.0-cp37-abi3-macosx_10_9_x86_64.whl", hash = "sha256:9b7ae6bd1a3f0df532b6d67ff674099a96d22bc0948955cb338488c31bfb8851" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d6/e6/a8f46f4a4ee7856fbd6ac0c6fb0dc65ed181ba46cd77875b8d9bbe494d9e/brotlicffi-1.1.0.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:19ffc919fa4fc6ace69286e0a23b3789b4219058313cf9b45625016bf7ff996b" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/be/20/201559dff14e83ba345a5ec03335607e47467b6633c210607e693aefac40/brotlicffi-1.1.0.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9feb210d932ffe7798ee62e6145d3a757eb6233aa9a4e7db78dd3690d7755814" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cd/15/695b1409264143be3c933f708a3f81d53c4a1e1ebbc06f46331decbf6563/brotlicffi-1.1.0.0-cp37-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:84763dbdef5dd5c24b75597a77e1b30c66604725707565188ba54bab4f114820" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b4/40/b961a702463b6005baf952794c2e9e0099bde657d0d7e007f923883b907f/brotlicffi-1.1.0.0-cp37-abi3-win32.whl", hash = "sha256:1b12b50e07c3911e1efa3a8971543e7648100713d4e0971b13631cce22c587eb" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1c/fa/5408a03c041114ceab628ce21766a4ea882aa6f6f0a800e04ee3a30ec6b9/brotlicffi-1.1.0.0-cp37-abi3-win_amd64.whl", hash = "sha256:994a4f0681bb6c6c3b0925530a1926b7a189d878e6e5e38fae8efa47c5d9c613" },
]

[[package]]
name = "caio"
version = "0.9.24"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/04/ec9b6864135032fd454f6cd1d9444e0bb01040196ad0cd776c061fc92c6b/caio-0.9.24.tar.gz", hash = "sha256:5bcdecaea02a9aa8e3acf0364eff8ad9903d57d70cdb274a42270126290a77f1", size = 27174, upload-time = "2025-04-23T16:31:19.191Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5f/76/b33a89dc2516aae045ef509cf2febe7ffb2a36c4eebb8f301a7ef2093385/caio-0.9.24-cp312-cp312-macosx_10_9_universal2.whl", hash = "sha256:7ae3566228383175265a7583107f21a7cb044a752ea29ba84fce7c1a49a05903", size = 42212, upload-time = "2025-04-23T16:31:08.457Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a9/8c/cb62483e69309bbad503c2ace29c4ac3466558a20e9aed840d313e1dcacd/caio-0.9.24-cp312-cp312-manylinux_2_34_aarch64.whl", hash = "sha256:a306b0dda91cb4ca3170f066c114597f8ea41b3da578574a9d2b54f86963de68", size = 81517, upload-time = "2025-04-23T16:31:09.686Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/64/80/8a8cdfd4b47e06d1e9de6d5431c2603e0741282fa06f757f10c04e619d8f/caio-0.9.24-cp312-cp312-manylinux_2_34_x86_64.whl", hash = "sha256:8ee158e56128d865fb7d57a9c9c22fca4e8aa8d8664859c977a36fff3ccb3609", size = 80216, upload-time = "2025-04-23T16:31:10.98Z" },
]

[[package]]
name = "calliper-diff"
version = "1.3.23"
source = { registry = "http://**********:3141/cheftin/pypi" }
dependencies = [
    { name = "attrs" },
    { name = "more-itertools" },
    { name = "numpy" },
    { name = "opencc", version = "1.1.7", source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }, marker = "sys_platform != 'darwin'" },
    { name = "opencc", version = "1.1.7", source = { url = "https://fm.paodingai.com/api/public/dl/jqeZI7gv/Soft/pypi/opencc-1.1.7-cp312-cp312-macosx_11_0_arm64.whl" }, marker = "sys_platform == 'darwin'" },
    { name = "python-levenshtein" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/e07/52c5c83e5dbf0/calliper_diff-1.3.23-cp312-cp312-macosx_10_9_universal2.whl", hash = "sha256:e0752c5c83e5dbf09ff4d17c5cf5afa31e691364fb63547430b41b75db1bbadf" },
    { url = "http://**********:3141/cheftin/pypi/+f/c7f/96a13f8fac05f/calliper_diff-1.3.23-cp312-cp312-manylinux_2_27_x86_64.whl", hash = "sha256:c7f96a13f8fac05f77331ad360483726def4b0ae025e9481dfeff2ba00552842" },
]

[[package]]
name = "celery"
version = "5.5.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "billiard" },
    { name = "click" },
    { name = "click-didyoumean" },
    { name = "click-plugins" },
    { name = "click-repl" },
    { name = "kombu" },
    { name = "python-dateutil" },
    { name = "vine" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/7d/6c289f407d219ba36d8b384b42489ebdd0c84ce9c413875a8aae0c85f35b/celery-5.5.3.tar.gz", hash = "sha256:6c972ae7968c2b5281227f01c3a3f984037d21c5129d07bf3550cc2afc6b10a5", size = 1667144, upload-time = "2025-06-01T11:08:12.563Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c9/af/0dcccc7fdcdf170f9a1585e5e96b6fb0ba1749ef6be8c89a6202284759bd/celery-5.5.3-py3-none-any.whl", hash = "sha256:0b5761a07057acee94694464ca482416b959568904c9dfa41ce8413a7d65d525", size = 438775, upload-time = "2025-06-01T11:08:09.94Z" },
]

[package.optional-dependencies]
redis = [
    { name = "kombu", extra = ["redis"] },
]

[[package]]
name = "certifi"
version = "2025.8.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/dc/67/960ebe6bf230a96cda2e0abcf73af550ec4f090005363542f0765df162e0/certifi-2025.8.3.tar.gz", hash = "sha256:e564105f78ded564e3ae7c923924435e1daa7463faeab5bb932bc53ffae63407", size = 162386, upload-time = "2025-08-03T03:07:47.08Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e5/48/1549795ba7742c948d2ad169c1c8cdbae65bc450d6cd753d124b17c8cd32/certifi-2025.8.3-py3-none-any.whl", hash = "sha256:f6c12493cfb1b06ba2ff328595af9350c65d6644968e5d3a2ffd78699af217a5", size = 161216, upload-time = "2025-08-03T03:07:45.777Z" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fc/97/c783634659c2920c3fc70419e3af40972dbaf758daa229a7d6ea6135c90d/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824", size = 516621, upload-time = "2024-09-04T20:45:21.852Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5a/84/e94227139ee5fb4d600a7a4927f322e1d4aea6fdc50bd3fca8493caba23f/cffi-1.17.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:805b4371bf7197c329fcb3ead37e710d1bca9da5d583f5073b799d5c5bd1eee4", size = 183178, upload-time = "2024-09-04T20:44:12.232Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/da/ee/fb72c2b48656111c4ef27f0f91da355e130a923473bf5ee75c5643d00cca/cffi-1.17.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:733e99bc2df47476e3848417c5a4540522f234dfd4ef3ab7fafdf555b082ec0c", size = 178840, upload-time = "2024-09-04T20:44:13.739Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cc/b6/db007700f67d151abadf508cbfd6a1884f57eab90b1bb985c4c8c02b0f28/cffi-1.17.1-cp312-cp312-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1257bdabf294dceb59f5e70c64a3e2f462c30c7ad68092d01bbbfb1c16b1ba36", size = 454803, upload-time = "2024-09-04T20:44:15.231Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1a/df/f8d151540d8c200eb1c6fba8cd0dfd40904f1b0682ea705c36e6c2e97ab3/cffi-1.17.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:da95af8214998d77a98cc14e3a3bd00aa191526343078b530ceb0bd710fb48a5", size = 478850, upload-time = "2024-09-04T20:44:17.188Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/28/c0/b31116332a547fd2677ae5b78a2ef662dfc8023d67f41b2a83f7c2aa78b1/cffi-1.17.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d63afe322132c194cf832bfec0dc69a99fb9bb6bbd550f161a49e9e855cc78ff", size = 485729, upload-time = "2024-09-04T20:44:18.688Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/91/2b/9a1ddfa5c7f13cab007a2c9cc295b70fbbda7cb10a286aa6810338e60ea1/cffi-1.17.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f79fc4fc25f1c8698ff97788206bb3c2598949bfe0fef03d299eb1b5356ada99", size = 471256, upload-time = "2024-09-04T20:44:20.248Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b2/d5/da47df7004cb17e4955df6a43d14b3b4ae77737dff8bf7f8f333196717bf/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b62ce867176a75d03a665bad002af8e6d54644fad99a3c70905c543130e39d93", size = 479424, upload-time = "2024-09-04T20:44:21.673Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/ac/2a28bcf513e93a219c8a4e8e125534f4f6db03e3179ba1c45e949b76212c/cffi-1.17.1-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:386c8bf53c502fff58903061338ce4f4950cbdcb23e2902d86c0f722b786bbe3", size = 484568, upload-time = "2024-09-04T20:44:23.245Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d4/38/ca8a4f639065f14ae0f1d9751e70447a261f1a30fa7547a828ae08142465/cffi-1.17.1-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:4ceb10419a9adf4460ea14cfd6bc43d08701f0835e979bf821052f1805850fe8", size = 488736, upload-time = "2024-09-04T20:44:24.757Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/86/c5/28b2d6f799ec0bdecf44dced2ec5ed43e0eb63097b0f58c293583b406582/cffi-1.17.1-cp312-cp312-win32.whl", hash = "sha256:a08d7e755f8ed21095a310a693525137cfe756ce62d066e53f502a83dc550f65", size = 172448, upload-time = "2024-09-04T20:44:26.208Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/b9/db34c4755a7bd1cb2d1603ac3863f22bcecbd1ba29e5ee841a4bc510b294/cffi-1.17.1-cp312-cp312-win_amd64.whl", hash = "sha256:51392eae71afec0d0c8fb1a53b204dbb3bcabcb3c9b807eedf3e1e6ccf2de903", size = 181976, upload-time = "2024-09-04T20:44:27.578Z" },
]

[[package]]
name = "cfgv"
version = "3.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/11/74/539e56497d9bd1d484fd863dd69cbbfa653cd2aa27abfe35653494d85e94/cfgv-3.4.0.tar.gz", hash = "sha256:e52591d4c5f5dead8e0f673fb16db7949d2cfb3f7da4582893288f0ded8fe560" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c5/55/51844dd50c4fc7a33b653bfaba4c2456f06955289ca770a5dbd5fd267374/cfgv-3.4.0-py2.py3-none-any.whl", hash = "sha256:b7265b1f29fd3316bfcd2b330d63d024f2bfd8bcb8b0272f8e19a504856c48f9" },
]

[[package]]
name = "chardet"
version = "5.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f3/0d/f7b6ab21ec75897ed80c17d79b15951a719226b9fababf1e40ea74d69079/chardet-5.2.0.tar.gz", hash = "sha256:1b3b6ff479a8c414bc3fa2c0852995695c4a026dcd6d0633b2dd092ca39c1cf7" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl", hash = "sha256:e1cf59446890a00105fe7b7912492ea04b6e6f06d4b742b2c788469e34c82970" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e4/33/89c2ced2b67d1c2a61c19c6751aa8902d46ce3dacb23600a283619f5a12d/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63", size = 126367, upload-time = "2025-05-02T08:34:42.01Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d7/a4/37f4d6035c89cac7930395a35cc0f1b872e652eaafb76a6075943754f095/charset_normalizer-3.4.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:0c29de6a1a95f24b9a1aa7aefd27d2487263f00dfd55a77719b530788f75cff7", size = 199936, upload-time = "2025-05-02T08:32:33.712Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ee/8a/1a5e33b73e0d9287274f899d967907cd0bf9c343e651755d9307e0dbf2b3/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:cddf7bd982eaa998934a91f69d182aec997c6c468898efe6679af88283b498d3", size = 143790, upload-time = "2025-05-02T08:32:35.768Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/66/52/59521f1d8e6ab1482164fa21409c5ef44da3e9f653c13ba71becdd98dec3/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:fcbe676a55d7445b22c10967bceaaf0ee69407fbe0ece4d032b6eb8d4565982a", size = 153924, upload-time = "2025-05-02T08:32:37.284Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/86/2d/fb55fdf41964ec782febbf33cb64be480a6b8f16ded2dbe8db27a405c09f/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:d41c4d287cfc69060fa91cae9683eacffad989f1a10811995fa309df656ec214", size = 146626, upload-time = "2025-05-02T08:32:38.803Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8c/73/6ede2ec59bce19b3edf4209d70004253ec5f4e319f9a2e3f2f15601ed5f7/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4e594135de17ab3866138f496755f302b72157d115086d100c3f19370839dd3a", size = 148567, upload-time = "2025-05-02T08:32:40.251Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/09/14/957d03c6dc343c04904530b6bef4e5efae5ec7d7990a7cbb868e4595ee30/charset_normalizer-3.4.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:cf713fe9a71ef6fd5adf7a79670135081cd4431c2943864757f0fa3a65b1fafd", size = 150957, upload-time = "2025-05-02T08:32:41.705Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0d/c8/8174d0e5c10ccebdcb1b53cc959591c4c722a3ad92461a273e86b9f5a302/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:a370b3e078e418187da8c3674eddb9d983ec09445c99a3a263c2011993522981", size = 145408, upload-time = "2025-05-02T08:32:43.709Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/58/aa/8904b84bc8084ac19dc52feb4f5952c6df03ffb460a887b42615ee1382e8/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:a955b438e62efdf7e0b7b52a64dc5c3396e2634baa62471768a64bc2adb73d5c", size = 153399, upload-time = "2025-05-02T08:32:46.197Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c2/26/89ee1f0e264d201cb65cf054aca6038c03b1a0c6b4ae998070392a3ce605/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:7222ffd5e4de8e57e03ce2cef95a4c43c98fcb72ad86909abdfc2c17d227fc1b", size = 156815, upload-time = "2025-05-02T08:32:48.105Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fd/07/68e95b4b345bad3dbbd3a8681737b4338ff2c9df29856a6d6d23ac4c73cb/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:bee093bf902e1d8fc0ac143c88902c3dfc8941f7ea1d6a8dd2bcb786d33db03d", size = 154537, upload-time = "2025-05-02T08:32:49.719Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/77/1a/5eefc0ce04affb98af07bc05f3bac9094513c0e23b0562d64af46a06aae4/charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:dedb8adb91d11846ee08bec4c8236c8549ac721c245678282dcb06b221aab59f", size = 149565, upload-time = "2025-05-02T08:32:51.404Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/37/a0/2410e5e6032a174c95e0806b1a6585eb21e12f445ebe239fac441995226a/charset_normalizer-3.4.2-cp312-cp312-win32.whl", hash = "sha256:db4c7bf0e07fc3b7d89ac2a5880a6a8062056801b83ff56d8464b70f65482b6c", size = 98357, upload-time = "2025-05-02T08:32:53.079Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6c/4f/c02d5c493967af3eda9c771ad4d2bbc8df6f99ddbeb37ceea6e8716a32bc/charset_normalizer-3.4.2-cp312-cp312-win_amd64.whl", hash = "sha256:5a9979887252a82fefd3d3ed2a8e3b937a7a809f65dcb1e068b090e165bbe99e", size = 105776, upload-time = "2025-05-02T08:32:54.573Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/20/94/c5790835a017658cbfabd07f3bfb549140c3ac458cfc196323996b10095a/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0", size = 52626, upload-time = "2025-05-02T08:34:40.053Z" },
]

[[package]]
name = "check-requirements-txt"
version = "1.2.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cd/99/336f2083b6f3951ec7a9af6843f24c09cb12fc6fc810253a397e77d43c45/check_requirements_txt-1.2.3-py3-none-any.whl", hash = "sha256:06a36a3db84f7c67c4758b4513f2189a24c952c0904a31a62b7fb911464756b3", size = 6184, upload-time = "2024-07-26T04:33:37.012Z" },
]

[[package]]
name = "click"
version = "8.2.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/60/6c/8ca2efa64cf75a977a0d7fac081354553ebe483345c734fb6b6515d96bbc/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202", size = 286342, upload-time = "2025-05-20T23:19:49.832Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b", size = 102215, upload-time = "2025-05-20T23:19:47.796Z" },
]

[[package]]
name = "click-didyoumean"
version = "0.3.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "click" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/30/ce/217289b77c590ea1e7c24242d9ddd6e249e52c795ff10fac2c50062c48cb/click_didyoumean-0.3.1.tar.gz", hash = "sha256:4f82fdff0dbe64ef8ab2279bd6aa3f6a99c3b28c05aa09cbfc07c9d7fbb5a463", size = 3089, upload-time = "2024-03-24T08:22:07.499Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1b/5b/974430b5ffdb7a4f1941d13d83c64a0395114503cc357c6b9ae4ce5047ed/click_didyoumean-0.3.1-py3-none-any.whl", hash = "sha256:5c4bb6007cfea5f2fd6583a2fb6701a22a41eb98957e63d0fac41c10e7c3117c", size = 3631, upload-time = "2024-03-24T08:22:06.356Z" },
]

[[package]]
name = "click-plugins"
version = "1.1.1.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "click" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c3/a4/34847b59150da33690a36da3681d6bbc2ec14ee9a846bc30a6746e5984e4/click_plugins-1.1.1.2.tar.gz", hash = "sha256:d7af3984a99d243c131aa1a828331e7630f4a88a9741fd05c927b204bcf92261", size = 8343, upload-time = "2025-06-25T00:47:37.555Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3d/9a/2abecb28ae875e39c8cad711eb1186d8d14eab564705325e77e4e6ab9ae5/click_plugins-1.1.1.2-py2.py3-none-any.whl", hash = "sha256:008d65743833ffc1f5417bf0e78e8d2c23aab04d9745ba817bd3e71b0feb6aa6", size = 11051, upload-time = "2025-06-25T00:47:36.731Z" },
]

[[package]]
name = "click-repl"
version = "0.3.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "click" },
    { name = "prompt-toolkit" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/a2/57f4ac79838cfae6912f997b4d1a64a858fb0c86d7fcaae6f7b58d267fca/click-repl-0.3.0.tar.gz", hash = "sha256:17849c23dba3d667247dc4defe1757fff98694e90fe37474f3feebb69ced26a9" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/52/40/9d857001228658f0d59e97ebd4c346fe73e138c6de1bce61dc568a57c7f8/click_repl-0.3.0-py3-none-any.whl", hash = "sha256:fb7e06deb8da8de86180a33a9da97ac316751c094c6899382da7feeeeb51b812" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697, upload-time = "2022-10-25T02:36:22.414Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335, upload-time = "2022-10-25T02:36:20.889Z" },
]

[[package]]
name = "coloredlogs"
version = "15.0.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "humanfriendly" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cc/c7/eed8f27100517e8c0e6b923d5f0845d0cb99763da6fdee00478f91db7325/coloredlogs-15.0.1.tar.gz", hash = "sha256:7c991aa71a4577af2f82600d8f8f3a89f936baeaf9b50a9c197da014e5bf16b0", size = 278520, upload-time = "2021-06-11T10:22:45.202Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/06/3d6badcf13db419e25b07041d9c7b4a2c331d3f4e7134445ec5df57714cd/coloredlogs-15.0.1-py2.py3-none-any.whl", hash = "sha256:612ee75c546f53e92e70049c9dbfcc18c935a2b9a53b66085ce9ef6a6e5c0934", size = 46018, upload-time = "2021-06-11T10:22:42.561Z" },
]

[[package]]
name = "configparser"
version = "7.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/ac/ea19242153b5e8be412a726a70e82c7b5c1537c83f61b20995b2eda3dcd7/configparser-7.2.0.tar.gz", hash = "sha256:b629cc8ae916e3afbd36d1b3d093f34193d851e11998920fdcfc4552218b7b70", size = 51273, upload-time = "2025-03-08T16:04:09.339Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/09/fe/f61e7129e9e689d9e40bbf8a36fb90f04eceb477f4617c02c6a18463e81f/configparser-7.2.0-py3-none-any.whl", hash = "sha256:fee5e1f3db4156dcd0ed95bc4edfa3580475537711f67a819c966b389d09ce62", size = 17232, upload-time = "2025-03-08T16:04:07.743Z" },
]

[[package]]
name = "cryptography"
version = "39.0.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fa/f3/f4b8c175ea9a1de650b0085858059050b7953a93d66c97ed89b93b232996/cryptography-39.0.2.tar.gz", hash = "sha256:bc5b871e977c8ee5a1bbc42fa8d19bcc08baf0c51cbf1586b0e87a2694dde42f", size = 604277, upload-time = "2023-03-02T21:08:49.517Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c5/8a/6dcd53c995506d4ff0de3a7da2202715654493fd12d7875f2a43b3a44150/cryptography-39.0.2-cp36-abi3-macosx_10_12_universal2.whl", hash = "sha256:2725672bb53bb92dc7b4150d233cd4b8c59615cd8288d495eaa86db00d4e5c06", size = 5444868, upload-time = "2023-03-02T21:06:34.782Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3c/0c/ac188ca210fbc02102d34ad8dba6956fe16fc566e5c5110a7f7bdbd30138/cryptography-39.0.2-cp36-abi3-macosx_10_12_x86_64.whl", hash = "sha256:23df8ca3f24699167daf3e23e51f7ba7334d504af63a94af468f468b975b7dd7", size = 2870908, upload-time = "2023-03-02T21:06:41.657Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3c/5a/6c180b745336f989e9b298e1790af0ef5b37640edb861fc536b5663726e3/cryptography-39.0.2-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_24_aarch64.whl", hash = "sha256:eb40fe69cfc6f5cdab9a5ebd022131ba21453cf7b8a7fd3631f45bbf52bed612", size = 3679686, upload-time = "2023-03-02T21:07:01.028Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d6/99/************************************************************/cryptography-39.0.2-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:bc0521cce2c1d541634b19f3ac661d7a64f9555135e9d8af3980965be717fd4a", size = 4001717, upload-time = "2023-03-02T21:07:45.741Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/d2/85480f4e754375c6d8e4a18cc8d2f28ef1984cf2843395c4d1ea396331d3/cryptography-39.0.2-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ffd394c7896ed7821a6d13b24657c6a34b6e2650bd84ae063cf11ccffa4f1a97", size = 4184331, upload-time = "2023-03-02T21:07:52.622Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f7/c0/daaeedc40e3385f01bb1af8c001ac214dcea6716b61efebabf9066b6f619/cryptography-39.0.2-cp36-abi3-manylinux_2_24_x86_64.whl", hash = "sha256:e8a0772016feeb106efd28d4a328e77dc2edae84dfbac06061319fdb669ff828", size = 4087820, upload-time = "2023-03-02T21:07:11.953Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9c/30/e787edf59f35192799d340a0a36976870ce487ba32948f086c29dc5d54ab/cryptography-39.0.2-cp36-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:8f35c17bd4faed2bc7797d2a66cbb4f986242ce2e30340ab832e5d99ae60e011", size = 3996069, upload-time = "2023-03-02T21:07:22.471Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f4/6d/1afb19efbe093f0b1af7a788bb8a693e495dc6c1d2139316b05b40f5e1dd/cryptography-39.0.2-cp36-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:b49a88ff802e1993b7f749b1eeb31134f03c8d5c956e3c125c75558955cda536", size = 4199202, upload-time = "2023-03-02T21:07:34.865Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d3/26/da69282ae3b350ee869536994e6816ac77057a7b5970068fabe56c644624/cryptography-39.0.2-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:5f8c682e736513db7d04349b4f6693690170f95aac449c56f97415c6980edef5", size = 4097677, upload-time = "2023-03-02T21:07:58.495Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e8/5c/9e47aac90fb5923d09c413909af6bf6ad4af2bfeeff707a2485c3f2af8be/cryptography-39.0.2-cp36-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:d7d84a512a59f4412ca8549b01f94be4161c94efc598bf09d027d67826beddc0", size = 4264946, upload-time = "2023-03-02T21:08:03.191Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/20/f406c0d2edb7b3974c39099816795d629bbd8716cd41cda8c3c4703de095/cryptography-39.0.2-cp36-abi3-win32.whl", hash = "sha256:c43ac224aabcbf83a947eeb8b17eaf1547bce3767ee2d70093b461f31729a480", size = 2086555, upload-time = "2023-03-02T21:08:06.829Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4f/0e/55b8cff87b572da828e9c6b7e7c5ecb9dc955b551ab863c28464a15f6451/cryptography-39.0.2-cp36-abi3-win_amd64.whl", hash = "sha256:788b3921d763ee35dfdb04248d0e3de11e3ca8eb22e2e48fef880c42e1f3c8f9", size = 2454252, upload-time = "2023-03-02T21:08:11.324Z" },
]

[[package]]
name = "cssselect2"
version = "0.8.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "tinycss2" },
    { name = "webencodings" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9f/86/fd7f58fc498b3166f3a7e8e0cddb6e620fe1da35b02248b1bd59e95dbaaa/cssselect2-0.8.0.tar.gz", hash = "sha256:7674ffb954a3b46162392aee2a3a0aedb2e14ecf99fcc28644900f4e6e3e9d3a", size = 35716, upload-time = "2025-03-05T14:46:07.988Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0f/e7/aa315e6a749d9b96c2504a1ba0ba031ba2d0517e972ce22682e3fccecb09/cssselect2-0.8.0-py3-none-any.whl", hash = "sha256:46fc70ebc41ced7a32cd42d58b1884d72ade23d21e5a4eaaf022401c13f0e76e", size = 15454, upload-time = "2025-03-05T14:46:06.463Z" },
]

[[package]]
name = "cv-algorithms"
version = "1.0.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cffi", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/80/02/3477bda4fdc4560a8295274c8d527085696828b94cbd91cdb80700765fff/cv_algorithms-1.0.4.tar.gz", hash = "sha256:6367f9060c6e9d97ede50bc3da177110cf6b5e2ae31d2bfabf2e06bb96ba38f2", size = 19613, upload-time = "2022-01-23T21:14:26.335Z" }

[[package]]
name = "cx-oracle"
version = "8.3.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e8/16/13c265afc984796fe38ee928733569b599cfd657245ddd1afad238b66656/cx_Oracle-8.3.0.tar.gz", hash = "sha256:3b2d215af4441463c97ea469b9cc307460739f89fdfa8ea222ea3518f1a424d9", size = 363886, upload-time = "2021-11-04T22:08:34.141Z" }

[[package]]
name = "cython"
version = "3.1.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/40/7b17cd866158238db704965da1b5849af261dbad393ea3ac966f934b2d39/cython-3.1.2.tar.gz", hash = "sha256:6bbf7a953fa6762dfecdec015e3b054ba51c0121a45ad851fa130f63f5331381", size = 3184825, upload-time = "2025-06-09T07:08:48.465Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/22/86/9393ab7204d5bb65f415dd271b658c18f57b9345d06002cae069376a5a7a/cython-3.1.2-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:9c2c4b6f9a941c857b40168b3f3c81d514e509d985c2dcd12e1a4fea9734192e", size = 3015898, upload-time = "2025-06-09T07:09:50.79Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f9/b8/3d10ac37ab7b7ee60bc6bfb48f6682ebee7fddaccf56e1e135f0d46ca79f/cython-3.1.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:bdbc115bbe1b8c1dcbcd1b03748ea87fa967eb8dfc3a1a9bb243d4a382efcff4", size = 2846204, upload-time = "2025-06-09T07:09:52.832Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f8/34/637771d8e10ebabc34a34cdd0d63fe797b66c334e150189955bf6442d710/cython-3.1.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c05111f89db1ca98edc0675cfaa62be47b3ff519a29876eb095532a9f9e052b8", size = 3080671, upload-time = "2025-06-09T07:09:54.924Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6b/c8/383ad1851fb272920a152c5a30bb6f08c3471b5438079d9488fc3074a170/cython-3.1.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f6e7188df8709be32cfdfadc7c3782e361c929df9132f95e1bbc90a340dca3c7", size = 3199022, upload-time = "2025-06-09T07:09:56.978Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e6/11/20adc8f2db37a29f245e8fd4b8b8a8245fce4bbbd128185cc9a7b1065e4c/cython-3.1.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1c0ecc71e60a051732c2607b8eb8f2a03a5dac09b28e52b8af323c329db9987b", size = 3241337, upload-time = "2025-06-09T07:09:59.156Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6f/0b/491f1fd3e177cccb6bb6d52f9609f78d395edde83ac47ebb06d21717ca29/cython-3.1.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:f27143cf88835c8bcc9bf3304953f23f377d1d991e8942982fe7be344c7cfce3", size = 3131808, upload-time = "2025-06-09T07:10:01.31Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/db/d2/5e7053a3214c9baa7ad72940555eb87cf4750e597f10b2bb43db62c3f39f/cython-3.1.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:d8c43566701133f53bf13485839d8f3f309095fe0d3b9d0cd5873073394d2edc", size = 3340319, upload-time = "2025-06-09T07:10:03.485Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/95/42/4842f8ddac9b36c94ae08b23c7fcde3f930c1dd49ac8992bb5320a4d96b5/cython-3.1.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:a3bb893e85f027a929c1764bb14db4c31cbdf8a96f59a78f608f2ba7cfbbce95", size = 3287370, upload-time = "2025-06-09T07:10:05.637Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/03/0d/417745ed75d414176e50310087b43299a3e611e75c379ff998f60f2ca1a8/cython-3.1.2-cp312-cp312-win32.whl", hash = "sha256:12c5902f105e43ca9af7874cdf87a23627f98c15d5a4f6d38bc9d334845145c0", size = 2487734, upload-time = "2025-06-09T07:10:07.591Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8e/82/df61d09ab81979ba171a8252af8fb8a3b26a0f19d1330c2679c11fe41667/cython-3.1.2-cp312-cp312-win_amd64.whl", hash = "sha256:06789eb7bd2e55b38b9dd349e9309f794aee0fed99c26ea5c9562d463877763f", size = 2695542, upload-time = "2025-06-09T07:10:09.545Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/25/d6/ef8557d5e75cc57d55df579af4976935ee111a85bbee4a5b72354e257066/cython-3.1.2-py3-none-any.whl", hash = "sha256:d23fd7ffd7457205f08571a42b108a3cf993e83a59fe4d72b42e6fc592cf2639", size = 1224753, upload-time = "2025-06-09T07:08:44.849Z" },
]

[[package]]
name = "dateparser"
version = "1.1.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "regex" },
    { name = "tzlocal" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/54/e6/5c1a1fb2ddc1fd1fcc37040df00dbbb2ca53278f232c069658252142d2e2/dateparser-1.1.1.tar.gz", hash = "sha256:038196b1f12c7397e38aad3d61588833257f6f552baa63a1499e6987fa8d42d9", size = 284013, upload-time = "2022-03-17T15:43:03.536Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e9/ee/91ad58de5cbd071ecab632a3a5ebea2b39937cf0691b7e1b4afff4a5eb7d/dateparser-1.1.1-py2.py3-none-any.whl", hash = "sha256:9600874312ff28a41f96ec7ccdc73be1d1c44435719da47fea3339d55ff5a628", size = 288862, upload-time = "2022-03-17T15:43:01.032Z" },
]

[[package]]
name = "deprecated"
version = "1.2.18"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "wrapt", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/97/06afe62762c9a8a86af0cfb7bfdab22a43ad17138b07af5b1a58442690a2/deprecated-1.2.18.tar.gz", hash = "sha256:422b6f6d859da6f2ef57857761bfb392480502a64c3028ca9bbe86085d72115d", size = 2928744, upload-time = "2025-01-27T10:46:25.7Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6e/c6/ac0b6c1e2d138f1002bcf799d330bd6d85084fece321e662a14223794041/Deprecated-1.2.18-py2.py3-none-any.whl", hash = "sha256:bd5011788200372a32418f888e326a09ff80d0214bd961147cfed01b5c018eec", size = 9998, upload-time = "2025-01-27T10:46:09.186Z" },
]

[[package]]
name = "dill"
version = "0.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/12/80/630b4b88364e9a8c8c5797f4602d0f76ef820909ee32f0bacb9f90654042/dill-0.4.0.tar.gz", hash = "sha256:0633f1d2df477324f53a895b02c901fb961bdbf65a17122586ea7019292cbcf0", size = 186976, upload-time = "2025-04-16T00:41:48.867Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/3d/9373ad9c56321fdab5b41197068e1d8c25883b3fea29dd361f9b55116869/dill-0.4.0-py3-none-any.whl", hash = "sha256:44f54bf6412c2c8464c14e8243eb163690a9800dbe2c367330883b19c7561049", size = 119668, upload-time = "2025-04-16T00:41:47.671Z" },
]

[[package]]
name = "distlib"
version = "0.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/96/8e/709914eb2b5749865801041647dc7f4e6d00b549cfe88b65ca192995f07c/distlib-0.4.0.tar.gz", hash = "sha256:feec40075be03a04501a973d81f633735b4b69f98b05450592310c0f401a4e0d", size = 614605, upload-time = "2025-07-17T16:52:00.465Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/33/6b/e0547afaf41bf2c42e52430072fa5658766e3d65bd4b03a563d1b6336f57/distlib-0.4.0-py2.py3-none-any.whl", hash = "sha256:9659f7d87e46584a30b5780e43ac7a2143098441670ff0a49d5f9034c54a6c16", size = 469047, upload-time = "2025-07-17T16:51:58.613Z" },
]

[[package]]
name = "distro"
version = "1.9.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fc/f8/98eea607f65de6527f8a2e8885fc8015d3e6f5775df186e443e0964a11c3/distro-1.9.0.tar.gz", hash = "sha256:2fa77c6fd8940f116ee1d6b94a2f90b13b5ea8d019b98bc8bafdcabcdd9bdbed", size = 60722, upload-time = "2023-12-24T09:54:32.31Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl", hash = "sha256:7bffd925d65168f85027d8da9af6bddab658135b840670a223589bc0c8ef02b2", size = 20277, upload-time = "2023-12-24T09:54:30.421Z" },
]

[[package]]
name = "dnspython"
version = "2.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b5/4a/263763cb2ba3816dd94b08ad3a33d5fdae34ecb856678773cc40a3605829/dnspython-2.7.0.tar.gz", hash = "sha256:ce9c432eda0dc91cf618a5cedf1a4e142651196bbcd2c80e89ed5a907e5cfaf1", size = 345197, upload-time = "2024-10-05T20:14:59.362Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/68/1b/e0a87d256e40e8c888847551b20a017a6b98139178505dc7ffb96f04e954/dnspython-2.7.0-py3-none-any.whl", hash = "sha256:b4c34b7d10b51bcc3a5071e7b8dee77939f1e878477eeecc965e9835f63c6c86", size = 313632, upload-time = "2024-10-05T20:14:57.687Z" },
]

[[package]]
name = "docopt"
version = "0.6.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/55/8f8cab2afd404cf578136ef2cc5dfb50baa1761b68c9da1fb1e4eed343c9/docopt-0.6.2.tar.gz", hash = "sha256:49b3a825280bd66b3aa83585ef59c4a8c82f2c8a522dbe754a8bc8d08c85c491", size = 25901, upload-time = "2014-06-16T11:18:57.406Z" }

[[package]]
name = "editdistpy"
version = "0.1.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0f/e3/be4d6836250feb6347799f4aa683927b7aa5db8e849906c6e54e10db2152/editdistpy-0.1.6.tar.gz", hash = "sha256:33cef3a82c6eb007edc02af65d8c99d67b75ce8e9c980105da4bd8256bcb4b25", size = 117947, upload-time = "2025-06-07T12:00:49.932Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/14/c7/e3b2b47e74c4afffb422fc7c44ca602d620982f84335f879cdb24f47ea2f/editdistpy-0.1.6-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:fa0bae603d1dd8e74618ac6f81e420fd19a8a7e44afdb8dedb1c871debda3e88", size = 159960, upload-time = "2025-06-07T12:00:22.18Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/34/5a/c77ac24f215400d1cf2ec6e413d76e6560abad608ca0f868bb167808f6d6/editdistpy-0.1.6-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:a3104f9807d5c59f70c1d4b144eb31775525d005ae160ccbe5593f956fe09bde", size = 159789, upload-time = "2025-06-07T12:00:23.584Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bd/77/3f5bb75ba8ff713b60572f9bab947914ccbc57ab782035fd3eeb82c2f03c/editdistpy-0.1.6-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4e9105e1a770989cee59aa049b972d63cdc3b04822247f548bf7af136f83fd20", size = 159589, upload-time = "2025-06-07T12:00:25.001Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9f/7f/3c0fb0aae0d9962ec85bf07114f4d01ac680dcfe76245dd9e793c4fa8a49/editdistpy-0.1.6-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:63bc15e924120ecbc45697314bd9c416c67e23aa1dae218ef74d32d859353a89", size = 1150312, upload-time = "2025-06-07T12:00:27.002Z" },
]

[[package]]
name = "email-validator"
version = "2.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "dnspython" },
    { name = "idna" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/48/ce/13508a1ec3f8bb981ae4ca79ea40384becc868bfae97fd1c942bb3a001b1/email_validator-2.2.0.tar.gz", hash = "sha256:cb690f344c617a714f22e66ae771445a1ceb46821152df8e165c5f9a364582b7", size = 48967, upload-time = "2024-06-20T11:30:30.034Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d7/ee/bf0adb559ad3c786f12bcbc9296b3f5675f529199bef03e2df281fa1fadb/email_validator-2.2.0-py3-none-any.whl", hash = "sha256:561977c2d73ce3611850a06fa56b414621e0c8faa9d66f2611407d87465da631", size = 33521, upload-time = "2024-06-20T11:30:28.248Z" },
]

[[package]]
name = "et-xmlfile"
version = "2.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d3/38/af70d7ab1ae9d4da450eeec1fa3918940a5fafb9055e934af8d6eb0c2313/et_xmlfile-2.0.0.tar.gz", hash = "sha256:dab3f4764309081ce75662649be815c4c9081e88f0837825f90fd28317d4da54", size = 17234, upload-time = "2024-10-25T17:25:40.039Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c1/8b/5fe2cc11fee489817272089c4203e679c63b570a5aaeb18d852ae3cbba6a/et_xmlfile-2.0.0-py3-none-any.whl", hash = "sha256:7a91720bc756843502c3b7504c77b8fe44217c85c537d85037f0f536151b2caa", size = 18059, upload-time = "2024-10-25T17:25:39.051Z" },
]

[[package]]
name = "execnet"
version = "2.1.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/ff/b4c0dc78fbe20c3e59c0c7334de0c27eb4001a2b2017999af398bf730817/execnet-2.1.1.tar.gz", hash = "sha256:5189b52c6121c24feae288166ab41b32549c7e2348652736540b9e6e7d4e72e3", size = 166524, upload-time = "2024-04-08T09:04:19.245Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/43/09/2aea36ff60d16dd8879bdb2f5b3ee0ba8d08cbbdcdfe870e695ce3784385/execnet-2.1.1-py3-none-any.whl", hash = "sha256:26dee51f1b80cebd6d0ca8e74dd8745419761d3bef34163928cbebbdc4749fdc", size = 40612, upload-time = "2024-04-08T09:04:17.414Z" },
]

[[package]]
name = "face"
version = "24.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "boltons" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ac/79/2484075a8549cd64beae697a8f664dee69a5ccf3a7439ee40c8f93c1978a/face-24.0.0.tar.gz", hash = "sha256:611e29a01ac5970f0077f9c577e746d48c082588b411b33a0dd55c4d872949f6", size = 62732, upload-time = "2024-11-02T05:24:26.095Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e9/47/21867c2e5fd006c8d36a560df9e32cb4f1f566b20c5dd41f5f8a2124f7de/face-24.0.0-py3-none-any.whl", hash = "sha256:0e2c17b426fa4639a4e77d1de9580f74a98f4869ba4c7c8c175b810611622cd3", size = 54742, upload-time = "2024-11-02T05:24:24.939Z" },
]

[[package]]
name = "faker"
version = "37.5.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "tzdata", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ce/5d/7797a74e8e31fa227f0303239802c5f09b6722bdb6638359e7b6c8f30004/faker-37.5.3.tar.gz", hash = "sha256:8315d8ff4d6f4f588bd42ffe63abd599886c785073e26a44707e10eeba5713dc", size = 1907147, upload-time = "2025-07-30T15:52:19.528Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4b/bf/d06dd96e7afa72069dbdd26ed0853b5e8bd7941e2c0819a9b21d6e6fc052/faker-37.5.3-py3-none-any.whl", hash = "sha256:386fe9d5e6132a915984bf887fcebcc72d6366a25dd5952905b31b141a17016d", size = 1949261, upload-time = "2025-07-30T15:52:17.729Z" },
]

[[package]]
name = "farm"
version = "0.4"
source = { registry = "http://**********:3141/cheftin/pypi" }
dependencies = [
    { name = "psutil" },
    { name = "requests" },
]
sdist = { url = "http://**********:3141/cheftin/pypi/+f/9d9/acdc066176c0a/farm-0.4.tar.gz", hash = "sha256:9d9acdc066176c0aea90e59c77ffaf70c16d365a178b44cecc6fc147b8715674" }

[[package]]
name = "fastapi"
version = "0.115.14"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pydantic" },
    { name = "starlette" },
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ca/53/8c38a874844a8b0fa10dd8adf3836ac154082cf88d3f22b544e9ceea0a15/fastapi-0.115.14.tar.gz", hash = "sha256:b1de15cdc1c499a4da47914db35d0e4ef8f1ce62b624e94e0e5824421df99739", size = 296263, upload-time = "2025-06-26T15:29:08.21Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/53/50/b1222562c6d270fea83e9c9075b8e8600b8479150a18e4516a6138b980d1/fastapi-0.115.14-py3-none-any.whl", hash = "sha256:6c0c8bf9420bd58f565e585036d971872472b4f7d3f6c73b698e10cffdefb3ca", size = 95514, upload-time = "2025-06-26T15:29:06.49Z" },
]

[[package]]
name = "fastapi-permissions"
version = "0.2.7"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "fastapi" },
    { name = "python-multipart" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3e/bf/2696be442f9c15f78e41d2932965d968e5c2b9cafad6f0e71fc5fba3e511/fastapi_permissions-0.2.7.tar.gz", hash = "sha256:9a344b2f67d983959f8922a02aa308f1c6382efeab12cdc9f013482c49df9b76", size = 16435, upload-time = "2020-10-05T14:18:54.421Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b9/b2/7d783f799b6428b470d33426e09e2297eb5cb2bbd06c8194384b914a1b96/fastapi_permissions-0.2.7-py3-none-any.whl", hash = "sha256:b9e5c05b4621b00db3d3ffb9cac55be1295c83648ced6c253fe94cd8cd3bc75f", size = 11589, upload-time = "2020-10-05T14:18:40.148Z" },
]

[[package]]
name = "filelock"
version = "3.18.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0a/10/c23352565a6544bdc5353e0b15fc1c563352101f30e24bf500207a54df9a/filelock-3.18.0.tar.gz", hash = "sha256:adbc88eabb99d2fec8c9c1b229b171f18afa655400173ddc653d5d01501fb9f2", size = 18075, upload-time = "2025-03-14T07:11:40.47Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl", hash = "sha256:c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de", size = 16215, upload-time = "2025-03-14T07:11:39.145Z" },
]

[[package]]
name = "filetype"
version = "1.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/29/745f7d30d47fe0f251d3ad3dc2978a23141917661998763bebb6da007eb1/filetype-1.2.0.tar.gz", hash = "sha256:66b56cd6474bf41d8c54660347d37afcc3f7d1970648de365c102ef77548aadb", size = 998020, upload-time = "2022-11-02T17:34:04.141Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/79/1b8fa1bb3568781e84c9200f951c735f3f157429f44be0495da55894d620/filetype-1.2.0-py2.py3-none-any.whl", hash = "sha256:7ce71b6880181241cf7ac8697a2f1eb6a8bd9b429f7ad6d27b8db9ba5f1c2d25", size = 19970, upload-time = "2022-11-02T17:34:01.425Z" },
]

[[package]]
name = "fire"
version = "0.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "termcolor" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6b/b6/82c7e601d6d3c3278c40b7bd35e17e82aa227f050aa9f66cb7b7fce29471/fire-0.7.0.tar.gz", hash = "sha256:961550f07936eaf65ad1dc8360f2b2bf8408fad46abbfa4d2a3794f8d2a95cdf", size = 87189, upload-time = "2024-10-01T14:29:31.585Z" }

[[package]]
name = "flashtext"
version = "2.7"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/81/d8/2cd0656eae456d615c2f1efbcae8dfca2cb871a31f34ba8925aba47d5e09/flashtext-2.7.tar.gz", hash = "sha256:a1be2b93e09d4f0deee4aad72b91a7127b61fb8b8034ca9a9c78ea745d8b05cf", size = 14536, upload-time = "2018-02-16T05:24:17.232Z" }

[[package]]
name = "flatbuffers"
version = "25.2.10"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e4/30/eb5dce7994fc71a2f685d98ec33cc660c0a5887db5610137e60d8cbc4489/flatbuffers-25.2.10.tar.gz", hash = "sha256:97e451377a41262f8d9bd4295cc836133415cc03d8cb966410a4af92eb00d26e", size = 22170, upload-time = "2025-02-11T04:26:46.257Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b8/25/155f9f080d5e4bc0082edfda032ea2bc2b8fab3f4d25d46c1e9dd22a1a89/flatbuffers-25.2.10-py2.py3-none-any.whl", hash = "sha256:ebba5f4d5ea615af3f7fd70fc310636fbb2bbd1f566ac0a23d98dd412de50051", size = 30953, upload-time = "2025-02-11T04:26:44.484Z" },
]

[[package]]
name = "fonttools"
version = "4.59.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8a/27/ec3c723bfdf86f34c5c82bf6305df3e0f0d8ea798d2d3a7cb0c0a866d286/fonttools-4.59.0.tar.gz", hash = "sha256:be392ec3529e2f57faa28709d60723a763904f71a2b63aabe14fee6648fe3b14", size = 3532521, upload-time = "2025-07-16T12:04:54.613Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e2/77/b1c8af22f4265e951cd2e5535dbef8859efcef4fb8dee742d368c967cddb/fonttools-4.59.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f9b3a78f69dcbd803cf2fb3f972779875b244c1115481dfbdd567b2c22b31f6b", size = 2767562, upload-time = "2025-07-16T12:04:06.895Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ff/5a/aeb975699588176bb357e8b398dfd27e5d3a2230d92b81ab8cbb6187358d/fonttools-4.59.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:57bb7e26928573ee7c6504f54c05860d867fd35e675769f3ce01b52af38d48e2", size = 2335168, upload-time = "2025-07-16T12:04:08.695Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/54/97/c6101a7e60ae138c4ef75b22434373a0da50a707dad523dd19a4889315bf/fonttools-4.59.0-cp312-cp312-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl", hash = "sha256:4536f2695fe5c1ffb528d84a35a7d3967e5558d2af58b4775e7ab1449d65767b", size = 4909850, upload-time = "2025-07-16T12:04:10.761Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bd/6c/fa4d18d641054f7bff878cbea14aa9433f292b9057cb1700d8e91a4d5f4f/fonttools-4.59.0-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:885bde7d26e5b40e15c47bd5def48b38cbd50830a65f98122a8fb90962af7cd1", size = 4955131, upload-time = "2025-07-16T12:04:12.846Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/20/5c/331947fc1377deb928a69bde49f9003364f5115e5cbe351eea99e39412a2/fonttools-4.59.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:6801aeddb6acb2c42eafa45bc1cb98ba236871ae6f33f31e984670b749a8e58e", size = 4899667, upload-time = "2025-07-16T12:04:14.558Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8a/46/b66469dfa26b8ff0baa7654b2cc7851206c6d57fe3abdabbaab22079a119/fonttools-4.59.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:31003b6a10f70742a63126b80863ab48175fb8272a18ca0846c0482968f0588e", size = 5051349, upload-time = "2025-07-16T12:04:16.388Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2e/05/ebfb6b1f3a4328ab69787d106a7d92ccde77ce66e98659df0f9e3f28d93d/fonttools-4.59.0-cp312-cp312-win32.whl", hash = "sha256:fbce6dae41b692a5973d0f2158f782b9ad05babc2c2019a970a1094a23909b1b", size = 2201315, upload-time = "2025-07-16T12:04:18.557Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/09/45/d2bdc9ea20bbadec1016fd0db45696d573d7a26d95ab5174ffcb6d74340b/fonttools-4.59.0-cp312-cp312-win_amd64.whl", hash = "sha256:332bfe685d1ac58ca8d62b8d6c71c2e52a6c64bc218dc8f7825c9ea51385aa01", size = 2249408, upload-time = "2025-07-16T12:04:20.489Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d0/9c/df0ef2c51845a13043e5088f7bb988ca6cd5bb82d5d4203d6a158aa58cf2/fonttools-4.59.0-py3-none-any.whl", hash = "sha256:241313683afd3baacb32a6bd124d0bce7404bc5280e12e291bae1b9bba28711d", size = 1128050, upload-time = "2025-07-16T12:04:52.687Z" },
]

[package.optional-dependencies]
woff = [
    { name = "brotli", marker = "platform_python_implementation == 'CPython'" },
    { name = "brotlicffi", marker = "platform_python_implementation != 'CPython'" },
    { name = "zopfli" },
]

[[package]]
name = "gino"
version = "1.1.0rc1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "sqlalchemy" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/60/32/179bbe4f49a0e7afc2d43c888fb8070faebecdd9ffeac5b15a8fe37f32f0/gino-1.1.0rc1.tar.gz", hash = "sha256:61a6f91aa9c7a0d430bc0809818914a516e177b05bc2bd2406c51aee96bc9d78", size = 52913, upload-time = "2022-02-12T20:37:36.836Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/59/a7/4f7442a6bc2f2a132b2456e65e5510fa6775e0243241beda068af075436d/gino-1.1.0rc1-py3-none-any.whl", hash = "sha256:184de44b0eccc7a9233c43eb7a21a72fbf56a101350e07b12701673441a05a8d", size = 59458, upload-time = "2022-02-12T20:37:35.218Z" },
]

[[package]]
name = "glazer-docx-convert"
version = "0.3.49"
source = { registry = "http://**********:3141/cheftin/pypi" }
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/37b/bad18e7019c57/glazer_docx_convert-0.3.49-py3-none-macosx_10_9_universal2.whl", hash = "sha256:37bbad18e7019c57f6921b7b06530630adb0c64fe47b21a8063270f55f91ba2b" },
    { url = "http://**********:3141/cheftin/pypi/+f/60d/1063e4b8454a0/glazer_docx_convert-0.3.49-py3-none-manylinux2014_aarch64.whl", hash = "sha256:60d1063e4b8454a03998ba9c164aedc697d8fcd8400ed591c9a821bab21cdefc" },
    { url = "http://**********:3141/cheftin/pypi/+f/83b/7c70762ce7c9b/glazer_docx_convert-0.3.49-py3-none-manylinux2014_x86_64.whl", hash = "sha256:83b7c70762ce7c9b9bb449a9bd63a59b0d83d7479ba97c5cc42d985fdf8af98e" },
]

[[package]]
name = "glom"
version = "24.11.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "attrs" },
    { name = "boltons" },
    { name = "face" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/05/89/b57cfbc448189426f2e01b244fbe9226b059ef5423a9d49c1d335a1f1026/glom-24.11.0.tar.gz", hash = "sha256:4325f96759a912044af7b6c6bd0dba44ad8c1eb6038aab057329661d2021bb27", size = 195120, upload-time = "2024-11-02T23:17:50.405Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9c/a2/75fd80784ec33da8d39cf885e8811a4fbc045a90db5e336b8e345e66dbb2/glom-24.11.0-py3-none-any.whl", hash = "sha256:991db7fcb4bfa9687010aa519b7b541bbe21111e70e58fdd2d7e34bbaa2c1fbd", size = 102690, upload-time = "2024-11-02T23:17:46.468Z" },
]

[[package]]
name = "gmssl-python"
version = "2.2.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/0c/4cfe1456608d1523a4db51e1204d73b7ec63f3c4c6530b6c153cccb5d2f4/gmssl_python-2.2.2.tar.gz", hash = "sha256:882537fbb70505236ae781d28f444e6dc35458d7b757c863e45080cc023f751c" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/ce/68605e510b6c1812de5e7184685ca1148eb261ceebae9767ff8c939a4fe9/gmssl_python-2.2.2-py3-none-any.whl", hash = "sha256:b3e019c52dbb0551e0c1a9b2329488c3c6d706d8f26daff4eea25c3fa3a61972" },
]

[[package]]
name = "greenlet"
version = "3.0.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/17/14/3bddb1298b9a6786539ac609ba4b7c9c0842e12aa73aaa4d8d73ec8f8185/greenlet-3.0.3.tar.gz", hash = "sha256:43374442353259554ce33599da8b692d5aa96f8976d567d4badf263371fbe491", size = 182013, upload-time = "2023-12-21T22:02:54.659Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/2f/461615adc53ba81e99471303b15ac6b2a6daa8d2a0f7f77fd15605e16d5b/greenlet-3.0.3-cp312-cp312-macosx_11_0_universal2.whl", hash = "sha256:70fb482fdf2c707765ab5f0b6655e9cfcf3780d8d87355a063547b41177599be", size = 273085, upload-time = "2023-12-21T22:03:01.176Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e9/55/2c3cfa3cdbb940cf7321fbcf544f0e9c74898eed43bf678abf416812d132/greenlet-3.0.3-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d4d1ac74f5c0c0524e4a24335350edad7e5f03b9532da7ea4d3c54d527784f2e", size = 660514, upload-time = "2023-12-21T22:29:28.62Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/38/77/efb21ab402651896c74f24a172eb4d7479f9f53898bd5e56b9e20bb24ffd/greenlet-3.0.3-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:149e94a2dd82d19838fe4b2259f1b6b9957d5ba1b25640d2380bea9c5df37676", size = 674295, upload-time = "2023-12-21T22:26:24.101Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/74/3a/92f188ace0190f0066dca3636cf1b09481d0854c46e92ec5e29c7cefe5b1/greenlet-3.0.3-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:15d79dd26056573940fcb8c7413d84118086f2ec1a8acdfa854631084393efcc", size = 669395, upload-time = "2023-12-21T22:31:35.992Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/63/0f/847ed02cdfce10f0e6e3425cd054296bddb11a17ef1b34681fa01a055187/greenlet-3.0.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:881b7db1ebff4ba09aaaeae6aa491daeb226c8150fc20e836ad00041bcb11230", size = 670455, upload-time = "2023-12-21T22:03:16.291Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bd/37/56b0da468a85e7704f3b2bc045015301bdf4be2184a44868c71f6dca6fe2/greenlet-3.0.3-cp312-cp312-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:fcd2469d6a2cf298f198f0487e0a5b1a47a42ca0fa4dfd1b6862c999f018ebbf", size = 625692, upload-time = "2023-12-21T22:03:06.294Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7c/68/b5f4084c0a252d7e9c0d95fc1cfc845d08622037adb74e05be3a49831186/greenlet-3.0.3-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:1f672519db1796ca0d8753f9e78ec02355e862d0998193038c7073045899f305", size = 1152597, upload-time = "2023-12-21T22:31:00.412Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a4/fa/31e22345518adcd69d1d6ab5087a12c178aa7f3c51103f6d5d702199d243/greenlet-3.0.3-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:2516a9957eed41dd8f1ec0c604f1cdc86758b587d964668b5b196a9db5bfcde6", size = 1181043, upload-time = "2023-12-21T22:04:20.032Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/53/80/3d94d5999b4179d91bcc93745d1b0815b073d61be79dd546b840d17adb18/greenlet-3.0.3-cp312-cp312-win_amd64.whl", hash = "sha256:bba5387a6975598857d86de9eac14210a49d554a77eb8261cc68b7d082f78ce2", size = 293635, upload-time = "2023-12-21T22:26:01.555Z" },
]

[[package]]
name = "grpcio"
version = "1.59.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/38/98/ca2cb5a81e0e2f3e3a00ebf91338fd3278ea0673e156a7ff2de30fe17113/grpcio-1.59.0.tar.gz", hash = "sha256:acf70a63cf09dd494000007b798aff88a436e1c03b394995ce450be437b8e54f", size = 24842974, upload-time = "2023-09-29T17:36:31.58Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f9/e9/596d8227be1091759504eeb85b5354ff4742b82afb62b0d0da1b01d740cf/grpcio-1.59.0-cp312-cp312-linux_armv7l.whl", hash = "sha256:f1feb034321ae2f718172d86b8276c03599846dc7bb1792ae370af02718f91c5", size = 97924642, upload-time = "2023-09-29T17:32:09.169Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/64/d8/609c556ee19756b036d4673da440a493b352bce0207d53cb03d3ecb5c33d/grpcio-1.59.0-cp312-cp312-macosx_10_10_universal2.whl", hash = "sha256:d09bd2a4e9f5a44d36bb8684f284835c14d30c22d8ec92ce796655af12163588", size = 9491596, upload-time = "2023-09-29T17:32:14.319Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/67/c9/8f4c9f345a90ebf460b6e2e082171ca020a9549f5c4fd0bfd40a30e0a1eb/grpcio-1.59.0-cp312-cp312-manylinux_2_17_aarch64.whl", hash = "sha256:2f120d27051e4c59db2f267b71b833796770d3ea36ca712befa8c5fff5da6ebd", size = 4996549, upload-time = "2023-09-29T17:32:16.914Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/93/90/16dffdd19745d9f1d8e22aa836dd52d1dadaae2159be69f79ac1b85f7a12/grpcio-1.59.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ba0ca727a173ee093f49ead932c051af463258b4b493b956a2c099696f38aa66", size = 5487168, upload-time = "2023-09-29T17:32:19.461Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/37/d3/feaaa48b29debef9438a0cd63e29b18864fbae6a4a45734ab3ff41828d27/grpcio-1.59.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:5711c51e204dc52065f4a3327dca46e69636a0b76d3e98c2c28c4ccef9b04c52", size = 5232498, upload-time = "2023-09-29T17:32:21.522Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3d/fe/49999d9156884e460a4eb6027c65b21ddc038b061c5daeb0fc41c944b073/grpcio-1.59.0-cp312-cp312-musllinux_1_1_i686.whl", hash = "sha256:d74f7d2d7c242a6af9d4d069552ec3669965b74fed6b92946e0e13b4168374f9", size = 5777692, upload-time = "2023-09-29T17:32:24.314Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f1/e0/4bacb18aea65326d2bd93b25b6b328db178f8cf0cb4415b0d25048175609/grpcio-1.59.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:3859917de234a0a2a52132489c4425a73669de9c458b01c9a83687f1f31b5b10", size = 5495449, upload-time = "2023-09-29T17:32:27.232Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e2/03/1c214689e3fd40bf2dd9a2748387c6a36be1d4ddbadceb06190fc0127654/grpcio-1.59.0-cp312-cp312-win32.whl", hash = "sha256:de2599985b7c1b4ce7526e15c969d66b93687571aa008ca749d6235d056b7205", size = 3075384, upload-time = "2023-09-29T17:32:29.162Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3b/4f/77fd12258e2b81ab648f6b6c678834fcd8f06876a1169b45615549532895/grpcio-1.59.0-cp312-cp312-win_amd64.whl", hash = "sha256:598f3530231cf10ae03f4ab92d48c3be1fee0c52213a1d5958df1a90957e6a88", size = 3646429, upload-time = "2023-09-29T17:32:30.971Z" },
]

[[package]]
name = "grpcio-health-checking"
version = "1.59.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "grpcio" },
    { name = "protobuf" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/c0/d69af94d157039192b36e38c28dbd85b5f88e293c4c78fa63a10394e58d1/grpcio-health-checking-1.59.0.tar.gz", hash = "sha256:e0221ca6ea6c2139614179808b8f8b323037d0b1bdefb0fc4cdd9a814999571e", size = 16321, upload-time = "2023-09-29T17:36:38.385Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ae/9b/d3e0f8e7d7bd09c3778b0d14fd6caff97457dc912d1c9082305d62e436d5/grpcio_health_checking-1.59.0-py3-none-any.whl", hash = "sha256:c4dfafd610241346f3978dec1a57ac9997a012ce41f28f7c258ba75bd680ddb4", size = 17944, upload-time = "2023-09-29T17:33:56.235Z" },
]

[[package]]
name = "grpcio-reflection"
version = "1.59.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "grpcio" },
    { name = "protobuf" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/be/51/de4ad1a2096323b5cd5b4714b6ec96d24a9f9dcd022617be9995c8468634/grpcio-reflection-1.59.0.tar.gz", hash = "sha256:1fe8f0dd6c180fdcf4e12ced2a8f784d9c741ccbc0b198585b1df024b7f8f3f2", size = 18349, upload-time = "2023-09-29T17:36:39.294Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/72/ae/5393de6ab8a13d4cdbe8564d6c0d785d2460b08dbaae580e23aa8babb64d/grpcio_reflection-1.59.0-py3-none-any.whl", hash = "sha256:bf4efc7e2e8162e5be9736f4d0a0b324c9bf0c04ad597a9d78fcaf1fbdf818ec", size = 21054, upload-time = "2023-09-29T17:33:57.51Z" },
]

[[package]]
name = "grpcio-tools"
version = "1.59.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "grpcio" },
    { name = "protobuf" },
    { name = "setuptools" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b8/62/bac5143fd2629d611c03832737040d1bb168ad52d59cab41131df0e06ead/grpcio-tools-1.59.0.tar.gz", hash = "sha256:aa4018f2d8662ac4d9830445d3d253a11b3e096e8afe20865547137aa1160e93", size = 4622547, upload-time = "2023-09-29T17:36:43.684Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b9/20/092b2bb503ed40654dec1c8351c83522eab42e9a4a3517c57d50097e802d/grpcio_tools-1.59.0-cp312-cp312-linux_armv7l.whl", hash = "sha256:240a7a3c2c54f77f1f66085a635bca72003d02f56a670e7db19aec531eda8f78", size = 59251675, upload-time = "2023-09-29T17:34:53.52Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ec/c7/d1d726dd9f38926201c1436e0ab747b1fcc984701aec7518a6ce8b1d3c08/grpcio_tools-1.59.0-cp312-cp312-macosx_10_10_universal2.whl", hash = "sha256:6119f62c462d119c63227b9534210f0f13506a888151b9bf586f71e7edf5088b", size = 4888261, upload-time = "2023-09-29T17:34:59.062Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/10/53/3162e5375def19be4b9ceef7b34c022ecd1e9bb132b8d2357202c775c973/grpcio_tools-1.59.0-cp312-cp312-manylinux_2_17_aarch64.whl", hash = "sha256:387662bee8e4c0b52cc0f61eaaca0ca583f5b227103f685b76083a3590a71a3e", size = 2518179, upload-time = "2023-09-29T17:35:02.429Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7a/d5/121d0ab43f9885443a8ef4d5e6dfef83f34806f410ef5c6f64e5d928ec30/grpcio_tools-1.59.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:8f0da5861ee276ca68493b217daef358960e8527cc63c7cb292ca1c9c54939af", size = 2922634, upload-time = "2023-09-29T17:35:05.385Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/11/0a/b1451420dfe3a6e0dd18d2bf37b8a7603e401764c134850c20da3c167c8a/grpcio_tools-1.59.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d0f0806de1161c7f248e4c183633ee7a58dfe45c2b77ddf0136e2e7ad0650b1b", size = 2664944, upload-time = "2023-09-29T17:35:07.755Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/85/ee/f8e1fa790816352bc8a4b082ca9bbca310d6e84760a5028aa8cca654c6b7/grpcio_tools-1.59.0-cp312-cp312-musllinux_1_1_i686.whl", hash = "sha256:c683be38a9bf4024c223929b4cd2f0a0858c94e9dc8b36d7eaa5a48ce9323a6f", size = 3473622, upload-time = "2023-09-29T17:35:09.825Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ea/c3/2db87b7d0510245f9a514951d9b128fc0b2c7dbdcdc40798a66cb70d2716/grpcio_tools-1.59.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:f965707da2b48a33128615bcfebedd215a3a30e346447e885bb3da37a143177a", size = 3087976, upload-time = "2023-09-29T17:35:11.789Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8a/a8/cdfd77cee3e2da0f8608afebf79b392b86250ed94f4a3aa70ed13963364b/grpcio_tools-1.59.0-cp312-cp312-win32.whl", hash = "sha256:2ee960904dde12a7fa48e1591a5b3eeae054bdce57bacf9fd26685a98138f5bf", size = 904335, upload-time = "2023-09-29T17:35:14.403Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/32/5c/6fdcc2c249cb2d5adbab3be5ef78f3bace065a04ed6e902972c41cca6929/grpcio_tools-1.59.0-cp312-cp312-win_amd64.whl", hash = "sha256:71cc6db1d66da3bc3730d9937bddc320f7b1f1dfdff6342bcb5741515fe4110b", size = 1057032, upload-time = "2023-09-29T17:35:16.833Z" },
]

[[package]]
name = "gunicorn"
version = "23.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "packaging" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/34/72/9614c465dc206155d93eff0ca20d42e1e35afc533971379482de953521a4/gunicorn-23.0.0.tar.gz", hash = "sha256:f014447a0101dc57e294f6c18ca6b40227a4c90e9bdb586042628030cba004ec", size = 375031, upload-time = "2024-08-10T20:25:27.378Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/7d/6dac2a6e1eba33ee43f318edbed4ff29151a49b5d37f080aad1e6469bca4/gunicorn-23.0.0-py3-none-any.whl", hash = "sha256:ec400d38950de4dfd418cff8328b2c8faed0edb0d517d3394e457c317908ca4d", size = 85029, upload-time = "2024-08-10T20:25:24.996Z" },
]

[[package]]
name = "h11"
version = "0.16.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/01/ee/02a2c011bdab74c6fb3c75474d40b3052059d95df7e73351460c8588d963/h11-0.16.0.tar.gz", hash = "sha256:4e35b956cf45792e4caa5885e69fba00bdbc6ffafbfa020300e549b208ee5ff1", size = 101250, upload-time = "2025-04-24T03:35:25.427Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl", hash = "sha256:63cf8bbe7522de3bf65932fda1d9c2772064ffb3dae62d55932da54b31cb6c86", size = 37515, upload-time = "2025-04-24T03:35:24.344Z" },
]

[[package]]
name = "handright"
version = "8.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pillow", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d6/b0/3232ad249f8a627d38be39e4839803f9b8e8f428cf9f0fbaf69e66d42897/handright-8.2.0.tar.gz", hash = "sha256:644c02506b877c15a8f0fd9e7b884ef118fb8d50c034ef58e0392d01f71ab352" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/40/00/1c5e1751c2b986b3408ef6df21c7c412d14de84cf5a7dc029774afae8a60/handright-8.2.0-py3-none-any.whl", hash = "sha256:aad7f7eb2539750ad7270cd160920258d252f0ba8216747eb48dbac95fb4fbf2" },
]

[[package]]
name = "hanziconv"
version = "0.3.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/63/71/b89cb63077fd807fe31cf7c016a06e7e579a289d8a37aa24a30282d02dd2/hanziconv-0.3.2.tar.gz", hash = "sha256:208866da6ae305bca19eb98702b65c93bb3a803b496e4287ca740d68892fc4c4", size = 276775, upload-time = "2016-09-01T05:41:15.254Z" }

[[package]]
name = "html5lib"
version = "1.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "six" },
    { name = "webencodings" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ac/b6/b55c3f49042f1df3dcd422b7f224f939892ee94f22abcf503a9b7339eaf2/html5lib-1.1.tar.gz", hash = "sha256:b2e5b40261e20f354d198eae92afc10d750afb487ed5e50f9c4eaf07c184146f", size = 272215, upload-time = "2020-06-22T23:32:38.834Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6c/dd/a834df6482147d48e225a49515aabc28974ad5a4ca3215c18a882565b028/html5lib-1.1-py2.py3-none-any.whl", hash = "sha256:0d78f8fde1c230e99fe37986a60526d7049ed4bf8a9fadbad5f00e22e58e041d", size = 112173, upload-time = "2020-06-22T23:32:36.781Z" },
]

[[package]]
name = "httpcore"
version = "1.0.9"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "certifi" },
    { name = "h11" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/06/94/82699a10bca87a5556c9c59b5963f2d039dbd239f25bc2a63907a05a14cb/httpcore-1.0.9.tar.gz", hash = "sha256:6e34463af53fd2ab5d807f399a9b45ea31c3dfa2276f15a2c3f00afff6e176e8", size = 85484, upload-time = "2025-04-24T22:06:22.219Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl", hash = "sha256:2d400746a40668fc9dec9810239072b40b4484b640a8c38fd654a024c7a1bf55", size = 78784, upload-time = "2025-04-24T22:06:20.566Z" },
]

[[package]]
name = "httptools"
version = "0.6.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/9a/ce5e1f7e131522e6d3426e8e7a490b3a01f39a6696602e1c4f33f9e94277/httptools-0.6.4.tar.gz", hash = "sha256:4e93eee4add6493b59a5c514da98c939b244fce4a0d8879cd3f466562f4b7d5c", size = 240639, upload-time = "2024-10-16T19:45:08.902Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/0e/d0b71465c66b9185f90a091ab36389a7352985fe857e352801c39d6127c8/httptools-0.6.4-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:df017d6c780287d5c80601dafa31f17bddb170232d85c066604d8558683711a2", size = 200683, upload-time = "2024-10-16T19:44:30.175Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e2/b8/412a9bb28d0a8988de3296e01efa0bd62068b33856cdda47fe1b5e890954/httptools-0.6.4-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:85071a1e8c2d051b507161f6c3e26155b5c790e4e28d7f236422dbacc2a9cc44", size = 104337, upload-time = "2024-10-16T19:44:31.786Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9b/01/6fb20be3196ffdc8eeec4e653bc2a275eca7f36634c86302242c4fbb2760/httptools-0.6.4-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:69422b7f458c5af875922cdb5bd586cc1f1033295aa9ff63ee196a87519ac8e1", size = 508796, upload-time = "2024-10-16T19:44:32.825Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f7/d8/b644c44acc1368938317d76ac991c9bba1166311880bcc0ac297cb9d6bd7/httptools-0.6.4-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:16e603a3bff50db08cd578d54f07032ca1631450ceb972c2f834c2b860c28ea2", size = 510837, upload-time = "2024-10-16T19:44:33.974Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/52/d8/254d16a31d543073a0e57f1c329ca7378d8924e7e292eda72d0064987486/httptools-0.6.4-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:ec4f178901fa1834d4a060320d2f3abc5c9e39766953d038f1458cb885f47e81", size = 485289, upload-time = "2024-10-16T19:44:35.111Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5f/3c/4aee161b4b7a971660b8be71a92c24d6c64372c1ab3ae7f366b3680df20f/httptools-0.6.4-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:f9eb89ecf8b290f2e293325c646a211ff1c2493222798bb80a530c5e7502494f", size = 489779, upload-time = "2024-10-16T19:44:36.253Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/12/b7/5cae71a8868e555f3f67a50ee7f673ce36eac970f029c0c5e9d584352961/httptools-0.6.4-cp312-cp312-win_amd64.whl", hash = "sha256:db78cb9ca56b59b016e64b6031eda5653be0589dba2b1b43453f6e8b405a0970", size = 88634, upload-time = "2024-10-16T19:44:37.357Z" },
]

[[package]]
name = "httpx"
version = "0.28.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "anyio" },
    { name = "certifi" },
    { name = "httpcore" },
    { name = "idna" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b1/df/48c586a5fe32a0f01324ee087459e112ebb7224f646c0b5023f5e79e9956/httpx-0.28.1.tar.gz", hash = "sha256:75e98c5f16b0f35b567856f597f06ff2270a374470a5c2392242528e3e3e42fc", size = 141406, upload-time = "2024-12-06T15:37:23.222Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl", hash = "sha256:d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad", size = 73517, upload-time = "2024-12-06T15:37:21.509Z" },
]

[[package]]
name = "humanfriendly"
version = "10.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pyreadline3", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cc/3f/2c29224acb2e2df4d2046e4c73ee2662023c58ff5b113c4c1adac0886c43/humanfriendly-10.0.tar.gz", hash = "sha256:6b0b831ce8f15f7300721aa49829fc4e83921a9a301cc7f606be6686a2288ddc", size = 360702, upload-time = "2021-09-17T21:40:43.31Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f0/0f/310fb31e39e2d734ccaa2c0fb981ee41f7bd5056ce9bc29b2248bd569169/humanfriendly-10.0-py2.py3-none-any.whl", hash = "sha256:1697e1a8a8f550fd43c2865cd84542fc175a61dcb779b6fee18cf6b6ccba1477", size = 86794, upload-time = "2021-09-17T21:40:39.897Z" },
]

[[package]]
name = "identify"
version = "2.6.12"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/88/d193a27416618628a5eea64e3223acd800b40749a96ffb322a9b55a49ed1/identify-2.6.12.tar.gz", hash = "sha256:d8de45749f1efb108badef65ee8386f0f7bb19a7f26185f74de6367bffbaf0e6", size = 99254, upload-time = "2025-05-23T20:37:53.3Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7a/cd/18f8da995b658420625f7ef13f037be53ae04ec5ad33f9b718240dcfd48c/identify-2.6.12-py2.py3-none-any.whl", hash = "sha256:ad9672d5a72e0d2ff7c5c8809b62dfa60458626352fb0eb7b55e69bdc45334a2", size = 99145, upload-time = "2025-05-23T20:37:51.495Z" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f1/70/7703c29685631f5a7590aa73f1f1d3fa9a380e654b86af429e0934a32f7d/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9", size = 190490, upload-time = "2024-09-15T18:07:39.745Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", size = 70442, upload-time = "2024-09-15T18:07:37.964Z" },
]

[[package]]
name = "imageio"
version = "2.37.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "numpy", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "pillow", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0c/47/57e897fb7094afb2d26e8b2e4af9a45c7cf1a405acdeeca001fdf2c98501/imageio-2.37.0.tar.gz", hash = "sha256:71b57b3669666272c818497aebba2b4c5f20d5b37c81720e5e1a56d59c492996", size = 389963, upload-time = "2025-01-20T02:42:37.089Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/bd/b394387b598ed84d8d0fa90611a90bee0adc2021820ad5729f7ced74a8e2/imageio-2.37.0-py3-none-any.whl", hash = "sha256:11efa15b87bc7871b61590326b2d635439acc321cf7f8ce996f812543ce10eed", size = 315796, upload-time = "2025-01-20T02:42:34.931Z" },
]

[[package]]
name = "imapclient"
version = "3.0.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b6/63/0eea51c9c263c18021cdc5866def55c98393f3bd74bbb8e3053e36f0f81a/IMAPClient-3.0.1.zip", hash = "sha256:78e6d62fbfbbe233e1f0e0e993160fd665eb1fd35973acddc61c15719b22bc02" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/de/8a/d1364c1c6d8f53ea390e8f1c6da220a4f9ee478ac8a473ae0669a2fb6f51/IMAPClient-3.0.1-py2.py3-none-any.whl", hash = "sha256:d77d77caa4123e0233b5cf2b9c54a078522e63270b88d3f48653a28637fd8828" },
]

[[package]]
name = "imbalanced-learn"
version = "0.12.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "joblib" },
    { name = "numpy" },
    { name = "scikit-learn" },
    { name = "scipy" },
    { name = "threadpoolctl" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/30/41/8b15f1df14ff38f1fdd4eb2b128478d99c38db4a14292270639ae9d65447/imbalanced-learn-0.12.4.tar.gz", hash = "sha256:8153ba385d296b07d97e0901a2624a86c06b48c94c2f92da3a5354827697b7a3", size = 478372, upload-time = "2024-10-04T17:00:55.162Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/0d/c3bfccc5d460eec8ff56889802aa88f5d07280d5282b307a74558e6edc44/imbalanced_learn-0.12.4-py3-none-any.whl", hash = "sha256:d47fc599160d3ea882e712a3a6b02bdd353c1a6436d8d68d41b1922e6ee4a703", size = 258324, upload-time = "2024-10-04T17:00:52.715Z" },
]

[[package]]
name = "imutils"
version = "0.5.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3f/d3/ecb4d108f6c1041d24842a345ee0123cd7f366ba75cf122601e856d42ba2/imutils-0.5.4.tar.gz", hash = "sha256:03827a9fca8b5c540305c0844a62591cf35a0caec199cb0f2f0a4a0fb15d8f24", size = 17240, upload-time = "2021-01-15T10:53:17.816Z" }

[[package]]
name = "inflate64"
version = "1.0.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e3/a7/974e6daa6c353cf080b540c18f11840e81b36d18106963a0a857b1fc2adf/inflate64-1.0.3.tar.gz", hash = "sha256:a89edd416c36eda0c3a5d32f31ff1555db2c5a3884aa8df95e8679f8203e12ee", size = 902876, upload-time = "2025-06-01T04:43:20.35Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b9/5b/c70411e396ea594153edca9607af736d11dd336cb76ddb028c5dc0ee39d3/inflate64-1.0.3-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:a25967a307aacf20ae979264fb7a1ad27b25a56fbc7e96dd28fcc12d54727479", size = 58663, upload-time = "2025-06-01T04:42:38.269Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4b/90/beb038045b076595da4f25c8dc5c0e7572f1fc56d71220e6ee6d194f92e6/inflate64-1.0.3-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:54dc4d1a17084ff15127c5e88c8dd1aa57e48f292c1ac1f4c65f226b6fd93d9c", size = 35965, upload-time = "2025-06-01T04:42:39.669Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/24/17/3a7561b21a7d5b3ca0fbc3d9e0691218c687fc82422bfc0568692bbd31e5/inflate64-1.0.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:8d8fb0bc154147437df1d45c9466b2c06c745e49d238de356b709cd6b5c45769", size = 35974, upload-time = "2025-06-01T04:42:40.897Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3e/c5/89559de5b79b14f8c229f8f96fed9d39ded32169de3d350b76099ab2b29d/inflate64-1.0.3-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:478b51748e3754200a11520fccec836fa5719b7f7fb5f90d67594e9940978834", size = 96157, upload-time = "2025-06-01T04:42:42.607Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/63/b9/a0bb6c58993a531ebaa7f83e15bfcb888b2e88b89d8173ad33d3cba3193f/inflate64-1.0.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a0cf0906c46a3224ffc96bd33c5fc108f4239c2552fbd1d10488a47ce7882465", size = 97037, upload-time = "2025-06-01T04:42:43.974Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/42/18/254ea3bc97df9d695805aed2690cf2c23c951689fe0e873f76e4ae35e240/inflate64-1.0.3-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:3dc36f76668d9174f17c27d19d0e95cb54cac0194ecb75cabbeed6244e75ab34", size = 98979, upload-time = "2025-06-01T04:42:45.632Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ff/6a/0ae6da63c9c35a7c43da1e6d0a831772fbc3c2b49a2fb2a4f75ccaa596de/inflate64-1.0.3-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:f7f25b374af2c3d5d9fc016ad3907545d0a4a31c7765830f126da9fcbd5f04c9", size = 100107, upload-time = "2025-06-01T04:42:47.11Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/da/319c627d9577dcfeb52f9f86277370676923a9d031295b61912b05a0f567/inflate64-1.0.3-cp312-cp312-win32.whl", hash = "sha256:9f5607153f294cb7ba37fdb6e744fe5c188d4b431fd6ff7b77530f39422eb026", size = 32970, upload-time = "2025-06-01T04:42:48.416Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5a/67/fef6de9945f9b72b8116c2dff3f9f24d7f53eb5f7f5c3c0613b9593f5d75/inflate64-1.0.3-cp312-cp312-win_amd64.whl", hash = "sha256:34b793dd49fcc5c46e96e7156584de47fe9745ef2b45c4976f9c7764ea0137de", size = 35756, upload-time = "2025-06-01T04:42:49.619Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a3/d7/94fd3e1af92806fe21db1d2a9af9a0f41d4031fa55b4bb4ea99857e18bbe/inflate64-1.0.3-cp312-cp312-win_arm64.whl", hash = "sha256:cd180b2a8709131a0768056f57cb0327813d55a214e76d7aed41b4413345a76b", size = 33209, upload-time = "2025-06-01T04:42:51.024Z" },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/97/ebf4da567aa6827c909642694d71c9fcf53e5b504f2d96afea02718862f3/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7", size = 4793, upload-time = "2025-03-19T20:09:59.721Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760", size = 6050, upload-time = "2025-03-19T20:10:01.071Z" },
]

[[package]]
name = "interval"
version = "1.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b3/2d/b337afbd232ea1ea9c38401135054bf763e7930ea5e2e49bc39af35c3443/interval-1.0.0.tar.bz2", hash = "sha256:6619937f3fcb5cf85bb3a60b4e4391664e8d7b30ae3dc4d04c3fc1d063ff1c3b", size = 11621, upload-time = "2005-11-10T05:48:38Z" }

[[package]]
name = "invoke"
version = "2.2.0"
source = { registry = "http://**********:3141/cheftin/pypi" }
sdist = { url = "http://**********:3141/cheftin/pypi/+f/223/e716961a1e096/invoke-2.2.0.tar.gz", hash = "sha256:223e716961a1e096310b78078ef56993e7d19037330eb2d041bdf885e70c4b57" }

[[package]]
name = "isort"
version = "6.0.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b8/21/1e2a441f74a653a144224d7d21afe8f4169e6c7c20bb13aec3a2dc3815e0/isort-6.0.1.tar.gz", hash = "sha256:1cb5df28dfbc742e490c5e41bad6da41b805b0a8be7bc93cd0fb2a8a890ac450", size = 821955, upload-time = "2025-02-26T21:13:16.955Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c1/11/114d0a5f4dabbdcedc1125dee0888514c3c3b16d3e9facad87ed96fad97c/isort-6.0.1-py3-none-any.whl", hash = "sha256:2dc5d7f65c9678d94c88dfc29161a320eec67328bc97aad576874cb4be1e9615", size = 94186, upload-time = "2025-02-26T21:13:14.911Z" },
]

[[package]]
name = "jinja2"
version = "3.1.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "markupsafe" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/df/bf/f7da0350254c0ed7c72f3e33cef02e048281fec7ecec5f032d4aac52226b/jinja2-3.1.6.tar.gz", hash = "sha256:0137fb05990d35f1275a587e9aee6d56da821fc83491a0fb838183be43f66d6d", size = 245115, upload-time = "2025-03-05T20:05:02.478Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/62/a1/3d680cbfd5f4b8f15abc1d571870c5fc3e594bb582bc3b64ea099db13e56/jinja2-3.1.6-py3-none-any.whl", hash = "sha256:85ece4451f492d0c13c5dd7c13a64681a86afae63a5f347908daf103ce6d2f67", size = 134899, upload-time = "2025-03-05T20:05:00.369Z" },
]

[[package]]
name = "jiter"
version = "0.10.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ee/9d/ae7ddb4b8ab3fb1b51faf4deb36cb48a4fbbd7cb36bad6a5fca4741306f7/jiter-0.10.0.tar.gz", hash = "sha256:07a7142c38aacc85194391108dc91b5b57093c978a9932bd86a36862759d9500", size = 162759, upload-time = "2025-05-18T19:04:59.73Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/b5/348b3313c58f5fbfb2194eb4d07e46a35748ba6e5b3b3046143f3040bafa/jiter-0.10.0-cp312-cp312-macosx_10_12_x86_64.whl", hash = "sha256:1e274728e4a5345a6dde2d343c8da018b9d4bd4350f5a472fa91f66fda44911b", size = 312262, upload-time = "2025-05-18T19:03:44.637Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9c/4a/6a2397096162b21645162825f058d1709a02965606e537e3304b02742e9b/jiter-0.10.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:7202ae396446c988cb2a5feb33a543ab2165b786ac97f53b59aafb803fef0744", size = 320124, upload-time = "2025-05-18T19:03:46.341Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/85/1ce02cade7516b726dd88f59a4ee46914bf79d1676d1228ef2002ed2f1c9/jiter-0.10.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:23ba7722d6748b6920ed02a8f1726fb4b33e0fd2f3f621816a8b486c66410ab2", size = 345330, upload-time = "2025-05-18T19:03:47.596Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/75/d0/bb6b4f209a77190ce10ea8d7e50bf3725fc16d3372d0a9f11985a2b23eff/jiter-0.10.0-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:371eab43c0a288537d30e1f0b193bc4eca90439fc08a022dd83e5e07500ed026", size = 369670, upload-time = "2025-05-18T19:03:49.334Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a0/f5/a61787da9b8847a601e6827fbc42ecb12be2c925ced3252c8ffcb56afcaf/jiter-0.10.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:6c675736059020365cebc845a820214765162728b51ab1e03a1b7b3abb70f74c", size = 489057, upload-time = "2025-05-18T19:03:50.66Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/12/e4/6f906272810a7b21406c760a53aadbe52e99ee070fc5c0cb191e316de30b/jiter-0.10.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:0c5867d40ab716e4684858e4887489685968a47e3ba222e44cde6e4a2154f959", size = 389372, upload-time = "2025-05-18T19:03:51.98Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e2/ba/77013b0b8ba904bf3762f11e0129b8928bff7f978a81838dfcc958ad5728/jiter-0.10.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:395bb9a26111b60141757d874d27fdea01b17e8fac958b91c20128ba8f4acc8a", size = 352038, upload-time = "2025-05-18T19:03:53.703Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/67/27/c62568e3ccb03368dbcc44a1ef3a423cb86778a4389e995125d3d1aaa0a4/jiter-0.10.0-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:6842184aed5cdb07e0c7e20e5bdcfafe33515ee1741a6835353bb45fe5d1bd95", size = 391538, upload-time = "2025-05-18T19:03:55.046Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c0/72/0d6b7e31fc17a8fdce76164884edef0698ba556b8eb0af9546ae1a06b91d/jiter-0.10.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:62755d1bcea9876770d4df713d82606c8c1a3dca88ff39046b85a048566d56ea", size = 523557, upload-time = "2025-05-18T19:03:56.386Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2f/09/bc1661fbbcbeb6244bd2904ff3a06f340aa77a2b94e5a7373fd165960ea3/jiter-0.10.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:533efbce2cacec78d5ba73a41756beff8431dfa1694b6346ce7af3a12c42202b", size = 514202, upload-time = "2025-05-18T19:03:57.675Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1b/84/5a5d5400e9d4d54b8004c9673bbe4403928a00d28529ff35b19e9d176b19/jiter-0.10.0-cp312-cp312-win32.whl", hash = "sha256:8be921f0cadd245e981b964dfbcd6fd4bc4e254cdc069490416dd7a2632ecc01", size = 211781, upload-time = "2025-05-18T19:03:59.025Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9b/52/7ec47455e26f2d6e5f2ea4951a0652c06e5b995c291f723973ae9e724a65/jiter-0.10.0-cp312-cp312-win_amd64.whl", hash = "sha256:a7c7d785ae9dda68c2678532a5a1581347e9c15362ae9f6e68f3fdbfb64f2e49", size = 206176, upload-time = "2025-05-18T19:04:00.305Z" },
]

[[package]]
name = "joblib"
version = "1.5.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/dc/fe/0f5a938c54105553436dbff7a61dc4fed4b1b2c98852f8833beaf4d5968f/joblib-1.5.1.tar.gz", hash = "sha256:f4f86e351f39fe3d0d32a9f2c3d8af1ee4cec285aafcb27003dda5205576b444", size = 330475, upload-time = "2025-05-23T12:04:37.097Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7d/4f/1195bbac8e0c2acc5f740661631d8d750dc38d4a32b23ee5df3cde6f4e0d/joblib-1.5.1-py3-none-any.whl", hash = "sha256:4719a31f054c7d766948dcd83e9613686b27114f190f717cec7eaa2084f8a74a", size = 307746, upload-time = "2025-05-23T12:04:35.124Z" },
]

[[package]]
name = "jsondiff"
version = "2.2.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pyyaml" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/35/48/841137f1843fa215ea284834d1514b8e9e20962bda63a636c7417e02f8fb/jsondiff-2.2.1.tar.gz", hash = "sha256:658d162c8a86ba86de26303cd86a7b37e1b2c1ec98b569a60e2ca6180545f7fe", size = 26649, upload-time = "2024-08-29T04:09:06.201Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/63/94/a8066f84d62ab666d61ef97deba1a33126e3e5c0c0da2c458ada17053ed6/jsondiff-2.2.1-py3-none-any.whl", hash = "sha256:b1f0f7e2421881848b1d556d541ac01a91680cfcc14f51a9b62cdf4da0e56722", size = 13440, upload-time = "2024-08-29T04:09:04.955Z" },
]

[[package]]
name = "kombu"
version = "5.5.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "amqp" },
    { name = "packaging" },
    { name = "tzdata" },
    { name = "vine" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0f/d3/5ff936d8319ac86b9c409f1501b07c426e6ad41966fedace9ef1b966e23f/kombu-5.5.4.tar.gz", hash = "sha256:886600168275ebeada93b888e831352fe578168342f0d1d5833d88ba0d847363", size = 461992, upload-time = "2025-06-01T10:19:22.281Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ef/70/a07dcf4f62598c8ad579df241af55ced65bed76e42e45d3c368a6d82dbc1/kombu-5.5.4-py3-none-any.whl", hash = "sha256:a12ed0557c238897d8e518f1d1fdf84bd1516c5e305af2dacd85c2015115feb8", size = 210034, upload-time = "2025-06-01T10:19:20.436Z" },
]

[package.optional-dependencies]
redis = [
    { name = "redis" },
]

[[package]]
name = "lazy-loader"
version = "0.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "packaging", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6f/6b/c875b30a1ba490860c93da4cabf479e03f584eba06fe5963f6f6644653d8/lazy_loader-0.4.tar.gz", hash = "sha256:47c75182589b91a4e1a85a136c074285a5ad4d9f39c63e0d7fb76391c4574cd1", size = 15431, upload-time = "2024-04-05T13:03:12.261Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/83/60/d497a310bde3f01cb805196ac61b7ad6dc5dcf8dce66634dc34364b20b4f/lazy_loader-0.4-py3-none-any.whl", hash = "sha256:342aa8e14d543a154047afb4ba8ef17f5563baad3fc610d7b15b213b0f119efc", size = 12097, upload-time = "2024-04-05T13:03:10.514Z" },
]

[[package]]
name = "levenshtein"
version = "0.27.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "rapidfuzz" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7e/b3/b5f8011483ba9083a0bc74c4d58705e9cf465fbe55c948a1b1357d0a2aa8/levenshtein-0.27.1.tar.gz", hash = "sha256:3e18b73564cfc846eec94dd13fab6cb006b5d2e0cc56bad1fd7d5585881302e3", size = 382571, upload-time = "2025-03-02T19:44:56.148Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0d/73/84a7126b9e6441c2547f1fbfd65f3c15c387d1fc04e0dd1d025a12107771/levenshtein-0.27.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:25fb540d8c55d1dc7bdc59b7de518ea5ed9df92eb2077e74bcb9bb6de7b06f69", size = 173953, upload-time = "2025-03-02T19:43:16.029Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8f/5c/06c01870c0cf336f9f29397bbfbfbbfd3a59918868716e7bb15828e89367/levenshtein-0.27.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:f09cfab6387e9c908c7b37961c045e8e10eb9b7ec4a700367f8e080ee803a562", size = 156399, upload-time = "2025-03-02T19:43:17.233Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c7/4a/c1d3f27ec8b3fff5a96617251bf3f61c67972869ac0a0419558fc3e2cbe6/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dafa29c0e616f322b574e0b2aeb5b1ff2f8d9a1a6550f22321f3bd9bb81036e3", size = 151061, upload-time = "2025-03-02T19:43:18.414Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4d/8f/2521081e9a265891edf46aa30e1b59c1f347a452aed4c33baafbec5216fa/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:be7a7642ea64392fa1e6ef7968c2e50ef2152c60948f95d0793361ed97cf8a6f", size = 183119, upload-time = "2025-03-02T19:43:19.975Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1f/a0/a63e3bce6376127596d04be7f57e672d2f3d5f540265b1e30b9dd9b3c5a9/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:060b48c45ed54bcea9582ce79c6365b20a1a7473767e0b3d6be712fa3a22929c", size = 185352, upload-time = "2025-03-02T19:43:21.424Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/17/8c/8352e992063952b38fb61d49bad8d193a4a713e7eeceb3ae74b719d7863d/levenshtein-0.27.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:712f562c5e64dd0398d3570fe99f8fbb88acec7cc431f101cb66c9d22d74c542", size = 159879, upload-time = "2025-03-02T19:43:22.792Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/69/b4/564866e2038acf47c3de3e9292fc7fc7cc18d2593fedb04f001c22ac6e15/levenshtein-0.27.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a6141ad65cab49aa4527a3342d76c30c48adb2393b6cdfeca65caae8d25cb4b8", size = 245005, upload-time = "2025-03-02T19:43:24.069Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ba/f9/7367f87e3a6eed282f3654ec61a174b4d1b78a7a73f2cecb91f0ab675153/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:799b8d73cda3265331116f62932f553804eae16c706ceb35aaf16fc2a704791b", size = 1116865, upload-time = "2025-03-02T19:43:25.4Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f5/02/b5b3bfb4b4cd430e9d110bad2466200d51c6061dae7c5a64e36047c8c831/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:ec99871d98e517e1cc4a15659c62d6ea63ee5a2d72c5ddbebd7bae8b9e2670c8", size = 1401723, upload-time = "2025-03-02T19:43:28.099Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ef/69/b93bccd093b3f06a99e67e11ebd6e100324735dc2834958ba5852a1b9fed/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:8799164e1f83588dbdde07f728ea80796ea72196ea23484d78d891470241b222", size = 1226276, upload-time = "2025-03-02T19:43:30.192Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ab/32/37dd1bc5ce866c136716619e6f7081d7078d7dd1c1da7025603dcfd9cf5f/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:583943813898326516ab451a83f734c6f07488cda5c361676150d3e3e8b47927", size = 1420132, upload-time = "2025-03-02T19:43:33.322Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4b/08/f3bc828dd9f0f8433b26f37c4fceab303186ad7b9b70819f2ccb493d99fc/levenshtein-0.27.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:5bb22956af44bb4eade93546bf95be610c8939b9a9d4d28b2dfa94abf454fed7", size = 1189144, upload-time = "2025-03-02T19:43:34.814Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2d/54/5ecd89066cf579223d504abe3ac37ba11f63b01a19fd12591083acc00eb6/levenshtein-0.27.1-cp312-cp312-win32.whl", hash = "sha256:d9099ed1bcfa7ccc5540e8ad27b5dc6f23d16addcbe21fdd82af6440f4ed2b6d", size = 88279, upload-time = "2025-03-02T19:43:38.86Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/53/79/4f8fabcc5aca9305b494d1d6c7a98482e90a855e0050ae9ff5d7bf4ab2c6/levenshtein-0.27.1-cp312-cp312-win_amd64.whl", hash = "sha256:7f071ecdb50aa6c15fd8ae5bcb67e9da46ba1df7bba7c6bf6803a54c7a41fd96", size = 100659, upload-time = "2025-03-02T19:43:40.082Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/81/f8e4c0f571c2aac2e0c56a6e0e41b679937a2b7013e79415e4aef555cff0/levenshtein-0.27.1-cp312-cp312-win_arm64.whl", hash = "sha256:83b9033a984ccace7703f35b688f3907d55490182fd39b33a8e434d7b2e249e6", size = 88168, upload-time = "2025-03-02T19:43:41.42Z" },
]

[[package]]
name = "lxml"
version = "6.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c5/ed/60eb6fa2923602fba988d9ca7c5cdbd7cf25faa795162ed538b527a35411/lxml-6.0.0.tar.gz", hash = "sha256:032e65120339d44cdc3efc326c9f660f5f7205f3a535c1fdbf898b29ea01fb72", size = 4096938, upload-time = "2025-06-26T16:28:19.373Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/89/c3/d01d735c298d7e0ddcedf6f028bf556577e5ab4f4da45175ecd909c79378/lxml-6.0.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:78718d8454a6e928470d511bf8ac93f469283a45c354995f7d19e77292f26108", size = 8429515, upload-time = "2025-06-26T16:26:06.776Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/06/37/0e3eae3043d366b73da55a86274a590bae76dc45aa004b7042e6f97803b1/lxml-6.0.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:84ef591495ffd3f9dcabffd6391db7bb70d7230b5c35ef5148354a134f56f2be", size = 4601387, upload-time = "2025-06-26T16:26:09.511Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a3/28/e1a9a881e6d6e29dda13d633885d13acb0058f65e95da67841c8dd02b4a8/lxml-6.0.0-cp312-cp312-manylinux2010_i686.manylinux2014_i686.manylinux_2_12_i686.manylinux_2_17_i686.whl", hash = "sha256:2930aa001a3776c3e2601cb8e0a15d21b8270528d89cc308be4843ade546b9ab", size = 5228928, upload-time = "2025-06-26T16:26:12.337Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9a/55/2cb24ea48aa30c99f805921c1c7860c1f45c0e811e44ee4e6a155668de06/lxml-6.0.0-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:219e0431ea8006e15005767f0351e3f7f9143e793e58519dc97fe9e07fae5563", size = 4952289, upload-time = "2025-06-28T18:47:25.602Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/31/c0/b25d9528df296b9a3306ba21ff982fc5b698c45ab78b94d18c2d6ae71fd9/lxml-6.0.0-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:bd5913b4972681ffc9718bc2d4c53cde39ef81415e1671ff93e9aa30b46595e7", size = 5111310, upload-time = "2025-06-28T18:47:28.136Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e9/af/681a8b3e4f668bea6e6514cbcb297beb6de2b641e70f09d3d78655f4f44c/lxml-6.0.0-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:390240baeb9f415a82eefc2e13285016f9c8b5ad71ec80574ae8fa9605093cd7", size = 5025457, upload-time = "2025-06-26T16:26:15.068Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/99/b6/3a7971aa05b7be7dfebc7ab57262ec527775c2c3c5b2f43675cac0458cad/lxml-6.0.0-cp312-cp312-manylinux_2_27_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:d6e200909a119626744dd81bae409fc44134389e03fbf1d68ed2a55a2fb10991", size = 5657016, upload-time = "2025-07-03T19:19:06.008Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/69/f8/693b1a10a891197143c0673fcce5b75fc69132afa81a36e4568c12c8faba/lxml-6.0.0-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:ca50bd612438258a91b5b3788c6621c1f05c8c478e7951899f492be42defc0da", size = 5257565, upload-time = "2025-06-26T16:26:17.906Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/96/e08ff98f2c6426c98c8964513c5dab8d6eb81dadcd0af6f0c538ada78d33/lxml-6.0.0-cp312-cp312-manylinux_2_31_armv7l.whl", hash = "sha256:c24b8efd9c0f62bad0439283c2c795ef916c5a6b75f03c17799775c7ae3c0c9e", size = 4713390, upload-time = "2025-06-26T16:26:20.292Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/83/6184aba6cc94d7413959f6f8f54807dc318fdcd4985c347fe3ea6937f772/lxml-6.0.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:afd27d8629ae94c5d863e32ab0e1d5590371d296b87dae0a751fb22bf3685741", size = 5066103, upload-time = "2025-06-26T16:26:22.765Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ee/01/8bf1f4035852d0ff2e36a4d9aacdbcc57e93a6cd35a54e05fa984cdf73ab/lxml-6.0.0-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:54c4855eabd9fc29707d30141be99e5cd1102e7d2258d2892314cf4c110726c3", size = 4791428, upload-time = "2025-06-26T16:26:26.461Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/29/31/c0267d03b16954a85ed6b065116b621d37f559553d9339c7dcc4943a76f1/lxml-6.0.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:c907516d49f77f6cd8ead1322198bdfd902003c3c330c77a1c5f3cc32a0e4d16", size = 5678523, upload-time = "2025-07-03T19:19:09.837Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5c/f7/5495829a864bc5f8b0798d2b52a807c89966523140f3d6fa3a58ab6720ea/lxml-6.0.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:36531f81c8214e293097cd2b7873f178997dae33d3667caaae8bdfb9666b76c0", size = 5281290, upload-time = "2025-06-26T16:26:29.406Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/79/56/6b8edb79d9ed294ccc4e881f4db1023af56ba451909b9ce79f2a2cd7c532/lxml-6.0.0-cp312-cp312-win32.whl", hash = "sha256:690b20e3388a7ec98e899fd54c924e50ba6693874aa65ef9cb53de7f7de9d64a", size = 3613495, upload-time = "2025-06-26T16:26:31.588Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/1e/cc32034b40ad6af80b6fd9b66301fc0f180f300002e5c3eb5a6110a93317/lxml-6.0.0-cp312-cp312-win_amd64.whl", hash = "sha256:310b719b695b3dd442cdfbbe64936b2f2e231bb91d998e99e6f0daf991a3eba3", size = 4014711, upload-time = "2025-06-26T16:26:33.723Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/55/10/dc8e5290ae4c94bdc1a4c55865be7e1f31dfd857a88b21cbba68b5fea61b/lxml-6.0.0-cp312-cp312-win_arm64.whl", hash = "sha256:8cb26f51c82d77483cdcd2b4a53cda55bbee29b3c2f3ddeb47182a2a9064e4eb", size = 3674431, upload-time = "2025-06-26T16:26:35.959Z" },
]

[[package]]
name = "magika"
version = "0.6.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "click" },
    { name = "numpy" },
    { name = "onnxruntime" },
    { name = "python-dotenv" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fe/b6/8fdd991142ad3e037179a494b153f463024e5a211ef3ad948b955c26b4de/magika-0.6.2.tar.gz", hash = "sha256:37eb6ae8020f6e68f231bc06052c0a0cbe8e6fa27492db345e8dc867dbceb067", size = 3036634, upload-time = "2025-05-02T14:54:18.88Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c2/07/4f7748f34279f2852068256992377474f9700b6fbad6735d6be58605178f/magika-0.6.2-py3-none-any.whl", hash = "sha256:5ef72fbc07723029b3684ef81454bc224ac5f60986aa0fc5a28f4456eebcb5b2", size = 2967609, upload-time = "2025-05-02T14:54:09.696Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/64/6d/0783af677e601d8a42258f0fbc47663abf435f927e58a8d2928296743099/magika-0.6.2-py3-none-macosx_11_0_arm64.whl", hash = "sha256:9109309328a1553886c8ff36c2ee9a5e9cfd36893ad81b65bf61a57debdd9d0e", size = 12404787, upload-time = "2025-05-02T14:54:16.963Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8a/ad/42e39748ddc4bbe55c2dc1093ce29079c04d096ac0d844f8ae66178bc3ed/magika-0.6.2-py3-none-manylinux_2_28_x86_64.whl", hash = "sha256:57cd1d64897634d15de552bd6b3ae9c6ff6ead9c60d384dc46497c08288e4559", size = 15091089, upload-time = "2025-05-02T14:54:11.59Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b0/1f/28e412d0ccedc068fbccdae6a6233faaa97ec3e5e2ffd242e49655b10064/magika-0.6.2-py3-none-win_amd64.whl", hash = "sha256:711f427a633e0182737dcc2074748004842f870643585813503ff2553b973b9f", size = 12385740, upload-time = "2025-05-02T14:54:14.096Z" },
]

[[package]]
name = "mako"
version = "1.3.10"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "markupsafe" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9e/38/bd5b78a920a64d708fe6bc8e0a2c075e1389d53bef8413725c63ba041535/mako-1.3.10.tar.gz", hash = "sha256:99579a6f39583fa7e5630a28c3c1f440e4e97a414b80372649c0ce338da2ea28", size = 392474, upload-time = "2025-04-10T12:44:31.16Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/87/fb/99f81ac72ae23375f22b7afdb7642aba97c00a713c217124420147681a2f/mako-1.3.10-py3-none-any.whl", hash = "sha256:baef24a52fc4fc514a0887ac600f9f1cff3d82c61d4d700a1fa84d597b88db59", size = 78509, upload-time = "2025-04-10T12:50:53.297Z" },
]

[[package]]
name = "markupsafe"
version = "3.0.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b2/97/5d42485e71dfc078108a86d6de8fa46db44a1a9295e89c5d6d4a06e23a62/markupsafe-3.0.2.tar.gz", hash = "sha256:ee55d3edf80167e48ea11a923c7386f4669df67d7994554387f84e7d8b0a2bf0", size = 20537, upload-time = "2024-10-18T15:21:54.129Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/22/09/d1f21434c97fc42f09d290cbb6350d44eb12f09cc62c9476effdb33a18aa/MarkupSafe-3.0.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9778bd8ab0a994ebf6f84c2b949e65736d5575320a17ae8984a77fab08db94cf", size = 14274, upload-time = "2024-10-18T15:21:13.777Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6b/b0/18f76bba336fa5aecf79d45dcd6c806c280ec44538b3c13671d49099fdd0/MarkupSafe-3.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:846ade7b71e3536c4e56b386c2a47adf5741d2d8b94ec9dc3e92e5e1ee1e2225", size = 12348, upload-time = "2024-10-18T15:21:14.822Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e0/25/dd5c0f6ac1311e9b40f4af06c78efde0f3b5cbf02502f8ef9501294c425b/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1c99d261bd2d5f6b59325c92c73df481e05e57f19837bdca8413b9eac4bd8028", size = 24149, upload-time = "2024-10-18T15:21:15.642Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f3/f0/89e7aadfb3749d0f52234a0c8c7867877876e0a20b60e2188e9850794c17/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e17c96c14e19278594aa4841ec148115f9c7615a47382ecb6b82bd8fea3ab0c8", size = 23118, upload-time = "2024-10-18T15:21:17.133Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d5/da/f2eeb64c723f5e3777bc081da884b414671982008c47dcc1873d81f625b6/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:88416bd1e65dcea10bc7569faacb2c20ce071dd1f87539ca2ab364bf6231393c", size = 22993, upload-time = "2024-10-18T15:21:18.064Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/da/0e/1f32af846df486dce7c227fe0f2398dc7e2e51d4a370508281f3c1c5cddc/MarkupSafe-3.0.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:2181e67807fc2fa785d0592dc2d6206c019b9502410671cc905d132a92866557", size = 24178, upload-time = "2024-10-18T15:21:18.859Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c4/f6/bb3ca0532de8086cbff5f06d137064c8410d10779c4c127e0e47d17c0b71/MarkupSafe-3.0.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:52305740fe773d09cffb16f8ed0427942901f00adedac82ec8b67752f58a1b22", size = 23319, upload-time = "2024-10-18T15:21:19.671Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/82/8be4c96ffee03c5b4a034e60a31294daf481e12c7c43ab8e34a1453ee48b/MarkupSafe-3.0.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:ad10d3ded218f1039f11a75f8091880239651b52e9bb592ca27de44eed242a48", size = 23352, upload-time = "2024-10-18T15:21:20.971Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/51/ae/97827349d3fcffee7e184bdf7f41cd6b88d9919c80f0263ba7acd1bbcb18/MarkupSafe-3.0.2-cp312-cp312-win32.whl", hash = "sha256:0f4ca02bea9a23221c0182836703cbf8930c5e9454bacce27e767509fa286a30", size = 15097, upload-time = "2024-10-18T15:21:22.646Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c1/80/a61f99dc3a936413c3ee4e1eecac96c0da5ed07ad56fd975f1a9da5bc630/MarkupSafe-3.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:8e06879fc22a25ca47312fbe7c8264eb0b662f6db27cb2d3bbbc74b1df4b9b87", size = 15601, upload-time = "2024-10-18T15:21:23.499Z" },
]

[[package]]
name = "marshmallow"
version = "4.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1e/ff/26df5a9f5ac57ccf693a5854916ab47243039d2aa9e0fe5f5a0331e7b74b/marshmallow-4.0.0.tar.gz", hash = "sha256:3b6e80aac299a7935cfb97ed01d1854fb90b5079430969af92118ea1b12a8d55", size = 220507, upload-time = "2025-04-17T02:25:54.925Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d6/26/6cc45d156f44dbe1d5696d9e54042e4dcaf7b946c0b86df6a97d29706f32/marshmallow-4.0.0-py3-none-any.whl", hash = "sha256:e7b0528337e9990fd64950f8a6b3a1baabed09ad17a0dfb844d701151f92d203", size = 48420, upload-time = "2025-04-17T02:25:53.375Z" },
]

[[package]]
name = "mccabe"
version = "0.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e7/ff/0ffefdcac38932a54d2b5eed4e0ba8a408f215002cd178ad1df0f2806ff8/mccabe-0.7.0.tar.gz", hash = "sha256:348e0240c33b60bbdf4e523192ef919f28cb2c3d7d5c7794f74009290f236325", size = 9658, upload-time = "2022-01-24T01:14:51.113Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl", hash = "sha256:6c2d30ab6be0e4a46919781807b4f0d834ebdd6c6e3dca0bda5a15f863427b6e", size = 7350, upload-time = "2022-01-24T01:14:49.62Z" },
]

[[package]]
name = "minio"
version = "6.0.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "certifi", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "configparser", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "python-dateutil", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "pytz", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "urllib3", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/99/2d/9b852055755422fb9ea9cee07678fb484e333ff62a6298ccb322e61fac8f/minio-6.0.2.tar.gz", hash = "sha256:acae9bfae0aec1b92025bd63e18135ebb4994c84600716c5323e14cb0c9a0b03", size = 114184, upload-time = "2020-11-28T03:54:07.043Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9e/6f/3df7e07e2b1642049a0d00e33e11b406d1d4686bd1f8ddb56962978c3653/minio-6.0.2-py2.py3-none-any.whl", hash = "sha256:eec4ab073ff979c34e928e532d8acc1d40d61ba4404709cf27ab3ecdcfa2a561", size = 73070, upload-time = "2020-11-28T03:54:03.555Z" },
]

[[package]]
name = "more-itertools"
version = "10.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2d/73/3557e45746fcaded71125c0a1c0f87616e8258c78391f0c365bf97bbfc99/more-itertools-10.1.0.tar.gz", hash = "sha256:626c369fa0eb37bac0291bce8259b332fd59ac792fa5497b59837309cd5b114a", size = 111235, upload-time = "2023-08-03T16:36:31.692Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5a/cb/6dce742ea14e47d6f565589e859ad225f2a5de576d7696e0623b784e226b/more_itertools-10.1.0-py3-none-any.whl", hash = "sha256:64e0735fcfdc6f3464ea133afe8ea4483b1c5fe3a3d69852e6503b43a0b222e6", size = 55824, upload-time = "2023-08-03T16:36:29.848Z" },
]

[[package]]
name = "mpmath"
version = "1.3.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e0/47/dd32fa426cc72114383ac549964eecb20ecfd886d1e5ccf5340b55b02f57/mpmath-1.3.0.tar.gz", hash = "sha256:7a28eb2a9774d00c7bc92411c19a89209d5da7c4c9a9e227be8330a23a25b91f", size = 508106, upload-time = "2023-03-07T16:47:11.061Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl", hash = "sha256:a0b2b9fe80bbcd81a6647ff13108738cfb482d481d826cc0e02f5b35e5c88d2c", size = 536198, upload-time = "2023-03-07T16:47:09.197Z" },
]

[[package]]
name = "msgspec"
version = "0.19.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cf/9b/95d8ce458462b8b71b8a70fa94563b2498b89933689f3a7b8911edfae3d7/msgspec-0.19.0.tar.gz", hash = "sha256:604037e7cd475345848116e89c553aa9a233259733ab51986ac924ab1b976f8e", size = 216934, upload-time = "2024-12-27T17:40:28.597Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b2/5f/a70c24f075e3e7af2fae5414c7048b0e11389685b7f717bb55ba282a34a7/msgspec-0.19.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:f98bd8962ad549c27d63845b50af3f53ec468b6318400c9f1adfe8b092d7b62f", size = 190485, upload-time = "2024-12-27T17:39:44.974Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/89/b0/1b9763938cfae12acf14b682fcf05c92855974d921a5a985ecc197d1c672/msgspec-0.19.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:43bbb237feab761b815ed9df43b266114203f53596f9b6e6f00ebd79d178cdf2", size = 183910, upload-time = "2024-12-27T17:39:46.401Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/87/81/0c8c93f0b92c97e326b279795f9c5b956c5a97af28ca0fbb9fd86c83737a/msgspec-0.19.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4cfc033c02c3e0aec52b71710d7f84cb3ca5eb407ab2ad23d75631153fdb1f12", size = 210633, upload-time = "2024-12-27T17:39:49.099Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d0/ef/c5422ce8af73928d194a6606f8ae36e93a52fd5e8df5abd366903a5ca8da/msgspec-0.19.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d911c442571605e17658ca2b416fd8579c5050ac9adc5e00c2cb3126c97f73bc", size = 213594, upload-time = "2024-12-27T17:39:51.204Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/19/2b/4137bc2ed45660444842d042be2cf5b18aa06efd2cda107cff18253b9653/msgspec-0.19.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:757b501fa57e24896cf40a831442b19a864f56d253679f34f260dcb002524a6c", size = 214053, upload-time = "2024-12-27T17:39:52.866Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9d/e6/8ad51bdc806aac1dc501e8fe43f759f9ed7284043d722b53323ea421c360/msgspec-0.19.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:5f0f65f29b45e2816d8bded36e6b837a4bf5fb60ec4bc3c625fa2c6da4124537", size = 219081, upload-time = "2024-12-27T17:39:55.142Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b1/ef/27dd35a7049c9a4f4211c6cd6a8c9db0a50647546f003a5867827ec45391/msgspec-0.19.0-cp312-cp312-win_amd64.whl", hash = "sha256:067f0de1c33cfa0b6a8206562efdf6be5985b988b53dd244a8e06f993f27c8c0", size = 187467, upload-time = "2024-12-27T17:39:56.531Z" },
]

[[package]]
name = "multivolumefile"
version = "0.2.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/f0/a7786212b5a4cb9ba05ae84a2bbd11d1d0279523aea0424b6d981d652a14/multivolumefile-0.2.3.tar.gz", hash = "sha256:a0648d0aafbc96e59198d5c17e9acad7eb531abea51035d08ce8060dcad709d6", size = 77984, upload-time = "2021-04-29T12:18:39.882Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/22/31/ec5f46fd4c83185b806aa9c736e228cb780f13990a9cf4da0beb70025fcc/multivolumefile-0.2.3-py3-none-any.whl", hash = "sha256:237f4353b60af1703087cf7725755a1f6fcaeeea48421e1896940cd1c920d678", size = 17037, upload-time = "2021-04-29T12:18:38.886Z" },
]

[[package]]
name = "nest-asyncio"
version = "1.6.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/83/f8/51569ac65d696c8ecbee95938f89d4abf00f47d58d48f6fbabfe8f0baefe/nest_asyncio-1.6.0.tar.gz", hash = "sha256:6f172d5449aca15afd6c646851f4e31e02c598d553a667e38cafa997cfec55fe", size = 7418, upload-time = "2024-01-21T14:25:19.227Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a0/c4/c2971a3ba4c6103a3d10c4b0f24f461ddc027f0f09763220cf35ca1401b3/nest_asyncio-1.6.0-py3-none-any.whl", hash = "sha256:87af6efd6b5e897c81050477ef65c62e2b2f35d51703cae01aff2905b1852e1c", size = 5195, upload-time = "2024-01-21T14:25:17.223Z" },
]

[[package]]
name = "networkx"
version = "3.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6c/4f/ccdb8ad3a38e583f214547fd2f7ff1fc160c43a75af88e6aec213404b96a/networkx-3.5.tar.gz", hash = "sha256:d4c6f9cf81f52d69230866796b82afbccdec3db7ae4fbd1b65ea750feed50037", size = 2471065, upload-time = "2025-05-29T11:35:07.804Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/eb/8d/776adee7bbf76365fdd7f2552710282c79a4ead5d2a46408c9043a2b70ba/networkx-3.5-py3-none-any.whl", hash = "sha256:0030d386a9a06dee3565298b4a734b68589749a544acbb6c412dc9e2489ec6ec", size = 2034406, upload-time = "2025-05-29T11:35:04.961Z" },
]

[[package]]
name = "nodeenv"
version = "1.9.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/43/16/fc88b08840de0e0a72a2f9d8c6bae36be573e475a6326ae854bcc549fc45/nodeenv-1.9.1.tar.gz", hash = "sha256:6ec12890a2dab7946721edbfbcd91f3319c6ccc9aec47be7c7e6b7011ee6645f", size = 47437, upload-time = "2024-06-04T18:44:11.171Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d2/1d/1b658dbd2b9fa9c4c9f32accbfc0205d532c8c6194dc0f2a4c0428e7128a/nodeenv-1.9.1-py2.py3-none-any.whl", hash = "sha256:ba11c9782d29c27c70ffbdda2d7415098754709be8a7056d79a737cd901155c9", size = 22314, upload-time = "2024-06-04T18:44:08.352Z" },
]

[[package]]
name = "numpy"
version = "1.26.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/65/6e/09db70a523a96d25e115e71cc56a6f9031e7b8cd166c1ac8438307c14058/numpy-1.26.4.tar.gz", hash = "sha256:2a02aba9ed12e4ac4eb3ea9421c420301a0c6460d9830d74a9df87efa4912010", size = 15786129, upload-time = "2024-02-06T00:26:44.495Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/95/12/8f2020a8e8b8383ac0177dc9570aad031a3beb12e38847f7129bacd96228/numpy-1.26.4-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:b3ce300f3644fb06443ee2222c2201dd3a89ea6040541412b8fa189341847218", size = 20335901, upload-time = "2024-02-05T23:55:32.801Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/75/5b/ca6c8bd14007e5ca171c7c03102d17b4f4e0ceb53957e8c44343a9546dcc/numpy-1.26.4-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:03a8c78d01d9781b28a6989f6fa1bb2c4f2d51201cf99d3dd875df6fbd96b23b", size = 13685868, upload-time = "2024-02-05T23:55:56.28Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/79/f8/97f10e6755e2a7d027ca783f63044d5b1bc1ae7acb12afe6a9b4286eac17/numpy-1.26.4-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9fad7dcb1aac3c7f0584a5a8133e3a43eeb2fe127f47e3632d43d677c66c102b", size = 13925109, upload-time = "2024-02-05T23:56:20.368Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0f/50/de23fde84e45f5c4fda2488c759b69990fd4512387a8632860f3ac9cd225/numpy-1.26.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:675d61ffbfa78604709862923189bad94014bef562cc35cf61d3a07bba02a7ed", size = 17950613, upload-time = "2024-02-05T23:56:56.054Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4c/0c/9c603826b6465e82591e05ca230dfc13376da512b25ccd0894709b054ed0/numpy-1.26.4-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:ab47dbe5cc8210f55aa58e4805fe224dac469cde56b9f731a4c098b91917159a", size = 13572172, upload-time = "2024-02-05T23:57:21.56Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/76/8c/2ba3902e1a0fc1c74962ea9bb33a534bb05984ad7ff9515bf8d07527cadd/numpy-1.26.4-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:1dda2e7b4ec9dd512f84935c5f126c8bd8b9f2fc001e9f54af255e8c5f16b0e0", size = 17786643, upload-time = "2024-02-05T23:57:56.585Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/28/4a/46d9e65106879492374999e76eb85f87b15328e06bd1550668f79f7b18c6/numpy-1.26.4-cp312-cp312-win32.whl", hash = "sha256:50193e430acfc1346175fcbdaa28ffec49947a06918b7b92130744e81e640110", size = 5677803, upload-time = "2024-02-05T23:58:08.963Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/2e/86f24451c2d530c88daf997cb8d6ac622c1d40d19f5a031ed68a4b73a374/numpy-1.26.4-cp312-cp312-win_amd64.whl", hash = "sha256:08beddf13648eb95f8d867350f6a018a4be2e5ad54c8d8caed89ebca558b2818", size = 15517754, upload-time = "2024-02-05T23:58:36.364Z" },
]

[[package]]
name = "onnx"
version = "1.17.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "numpy" },
    { name = "protobuf" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9a/54/0e385c26bf230d223810a9c7d06628d954008a5e5e4b73ee26ef02327282/onnx-1.17.0.tar.gz", hash = "sha256:48ca1a91ff73c1d5e3ea2eef20ae5d0e709bb8a2355ed798ffc2169753013fd3", size = 12165120, upload-time = "2024-10-01T21:48:40.63Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b4/dd/c416a11a28847fafb0db1bf43381979a0f522eb9107b831058fde012dd56/onnx-1.17.0-cp312-cp312-macosx_12_0_universal2.whl", hash = "sha256:0e906e6a83437de05f8139ea7eaf366bf287f44ae5cc44b2850a30e296421f2f", size = 16651271, upload-time = "2024-10-01T21:46:16.084Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f0/6c/f040652277f514ecd81b7251841f96caa5538365af7df07f86c6018cda2b/onnx-1.17.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:3d955ba2939878a520a97614bcf2e79c1df71b29203e8ced478fa78c9a9c63c2", size = 15907522, upload-time = "2024-10-01T21:46:18.574Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3d/7c/67f4952d1b56b3f74a154b97d0dd0630d525923b354db117d04823b8b49b/onnx-1.17.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4f3fb5cc4e2898ac5312a7dc03a65133dd2abf9a5e520e69afb880a7251ec97a", size = 16046307, upload-time = "2024-10-01T21:46:21.186Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ae/20/6da11042d2ab870dfb4ce4a6b52354d7651b6b4112038b6d2229ab9904c4/onnx-1.17.0-cp312-cp312-win32.whl", hash = "sha256:317870fca3349d19325a4b7d1b5628f6de3811e9710b1e3665c68b073d0e68d7", size = 14424235, upload-time = "2024-10-01T21:46:24.343Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/35/55/c4d11bee1fdb0c4bd84b4e3562ff811a19b63266816870ae1f95567aa6e1/onnx-1.17.0-cp312-cp312-win_amd64.whl", hash = "sha256:659b8232d627a5460d74fd3c96947ae83db6d03f035ac633e20cd69cfa029227", size = 14530453, upload-time = "2024-10-01T21:46:26.981Z" },
]

[[package]]
name = "onnxconverter-common"
version = "1.16.0"
source = { registry = "http://**********:3141/cheftin/pypi" }
dependencies = [
    { name = "numpy" },
    { name = "onnx" },
    { name = "packaging" },
    { name = "protobuf" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/280/87ebad79a3f45/onnxconverter_common-1.16.0-py2.py3-none-any.whl", hash = "sha256:28087ebad79a3f450b8ac29d9b5254f2a9e1c146744f9d21a445a672d062085f" },
]

[[package]]
name = "onnxruntime"
version = "1.22.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "coloredlogs" },
    { name = "flatbuffers" },
    { name = "numpy" },
    { name = "packaging" },
    { name = "protobuf" },
    { name = "sympy" },
]
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/48/70/ca2a4d38a5deccd98caa145581becb20c53684f451e89eb3a39915620066/onnxruntime-1.22.1-cp312-cp312-macosx_13_0_universal2.whl", hash = "sha256:a938d11c0dc811badf78e435daa3899d9af38abee950d87f3ab7430eb5b3cf5a", size = 34342883, upload-time = "2025-07-10T19:15:38.223Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/29/e5/00b099b4d4f6223b610421080d0eed9327ef9986785c9141819bbba0d396/onnxruntime-1.22.1-cp312-cp312-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:984cea2a02fcc5dfea44ade9aca9fe0f7a8a2cd6f77c258fc4388238618f3928", size = 14473861, upload-time = "2025-07-10T19:15:42.911Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0a/50/519828a5292a6ccd8d5cd6d2f72c6b36ea528a2ef68eca69647732539ffa/onnxruntime-1.22.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:2d39a530aff1ec8d02e365f35e503193991417788641b184f5b1e8c9a6d5ce8d", size = 16475713, upload-time = "2025-07-10T19:15:45.452Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5d/54/7139d463bb0a312890c9a5db87d7815d4a8cce9e6f5f28d04f0b55fcb160/onnxruntime-1.22.1-cp312-cp312-win_amd64.whl", hash = "sha256:6a64291d57ea966a245f749eb970f4fa05a64d26672e05a83fdb5db6b7d62f87", size = 12690910, upload-time = "2025-07-10T19:15:47.478Z" },
]

[[package]]
name = "openai"
version = "1.55.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "anyio" },
    { name = "distro" },
    { name = "httpx" },
    { name = "jiter" },
    { name = "pydantic" },
    { name = "sniffio" },
    { name = "tqdm" },
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1e/39/d4859d897da053b61b84403f67dbef1abd075e441cb354892ff14f98e2c7/openai-1.55.3.tar.gz", hash = "sha256:547e85b94535469f137a779d8770c8c5adebd507c2cc6340ca401a7c4d5d16f0", size = 314571, upload-time = "2024-11-28T16:56:47.832Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/10/06/691ef3f0112ecf0d7420d0bf35b5d16cf81554141f4b4913a9831031013d/openai-1.55.3-py3-none-any.whl", hash = "sha256:2a235d0e1e312cd982f561b18c27692e253852f4e5fb6ccf08cb13540a9bdaa1", size = 389558, upload-time = "2024-11-28T16:56:46.174Z" },
]

[[package]]
name = "opencc"
version = "1.1.7"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
resolution-markers = [
    "platform_machine == 'aarch64' and sys_platform == 'linux'",
    "platform_machine != 'aarch64' and sys_platform == 'linux'",
    "sys_platform != 'darwin' and sys_platform != 'linux'",
]
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/92/5a/7b1cd306d435434058ea4a97467f0f2e74408ed54be4b2ae3d40d337ab5b/OpenCC-1.1.7-cp312-cp312-manylinux1_x86_64.whl", hash = "sha256:dc5d6443b833e1a45bcad4d4312c680d9d574a365491357165d927a3eecac5f0", size = 779675, upload-time = "2023-10-16T07:26:04.006Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/71/66/1d56f4cf2c80243d3f8e715bfd81a2e4413691e9a8a2e07c827d7e2f6596/OpenCC-1.1.7-cp312-cp312-win_amd64.whl", hash = "sha256:7f3f843f0b47afa0bff91ea5b9fbf9968f19ffbaffda9508615993058246ca3c", size = 716542, upload-time = "2023-10-16T07:30:30.88Z" },
]

[[package]]
name = "opencc"
version = "1.1.7"
source = { url = "https://fm.paodingai.com/api/public/dl/jqeZI7gv/Soft/pypi/opencc-1.1.7-cp312-cp312-macosx_11_0_arm64.whl" }
resolution-markers = [
    "sys_platform == 'darwin'",
]
wheels = [
    { url = "https://fm.paodingai.com/api/public/dl/jqeZI7gv/Soft/pypi/opencc-1.1.7-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:128417f9474a5b8d42eb67d5d993a0612941ff0f5731eccd583257d909ea5e18" },
]

[[package]]
name = "opencv-python"
version = "4.7.0.72"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
resolution-markers = [
    "sys_platform == 'darwin'",
]
dependencies = [
    { name = "numpy", marker = "sys_platform == 'darwin'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/40/93/655af887bafece2a655998f53b9bd21ad94b0627d81d44aef35c79f40de6/opencv-python-4.7.0.72.tar.gz", hash = "sha256:3424794a711f33284581f3c1e4b071cfc827d02b99d6fd9a35391f517c453306", size = 91093645, upload-time = "2023-02-22T19:12:33.96Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1a/12/a94f6e1aa2d03a483c641da4e8ec20d511a3ece2a087bb3232192e6c6987/opencv_python-4.7.0.72-cp37-abi3-macosx_10_16_x86_64.whl", hash = "sha256:d4f8880440c433a0025d78804dda6901d1e8e541a561dda66892d90290aef881", size = 53880040, upload-time = "2023-02-22T19:12:36.406Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/91/b55ca570e3a96d241a7e76bcfe2f73d219cda01411bbd4214e7d12d5f98c/opencv_python-4.7.0.72-cp37-abi3-macosx_11_0_arm64.whl", hash = "sha256:7a297e7651e22eb17c265ddbbc80e2ba2a8ff4f4a1696a67c45e5f5798245842", size = 32645481, upload-time = "2023-02-22T19:11:57.92Z" },
]

[[package]]
name = "opencv-python"
version = "4.11.0.86"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
resolution-markers = [
    "platform_machine == 'aarch64' and sys_platform == 'linux'",
    "platform_machine != 'aarch64' and sys_platform == 'linux'",
]
dependencies = [
    { name = "numpy", marker = "sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/17/06/68c27a523103dad5837dc5b87e71285280c4f098c60e4fe8a8db6486ab09/opencv-python-4.11.0.86.tar.gz", hash = "sha256:03d60ccae62304860d232272e4a4fda93c39d595780cb40b161b310244b736a4", size = 95171956, upload-time = "2025-01-16T13:52:24.737Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f3/bd/29c126788da65c1fb2b5fb621b7fed0ed5f9122aa22a0868c5e2c15c6d23/opencv_python-4.11.0.86-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1b92ae2c8852208817e6776ba1ea0d6b1e0a1b5431e971a2a0ddd2a8cc398202", size = 42230439, upload-time = "2025-01-16T13:51:35.822Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2c/8b/90eb44a40476fa0e71e05a0283947cfd74a5d36121a11d926ad6f3193cc4/opencv_python-4.11.0.86-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6b02611523803495003bd87362db3e1d2a0454a6a63025dc6658a9830570aa0d", size = 62986597, upload-time = "2025-01-16T13:52:08.836Z" },
]

[[package]]
name = "openpyxl"
version = "3.1.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "et-xmlfile" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3d/f9/88d94a75de065ea32619465d2f77b29a0469500e99012523b91cc4141cd1/openpyxl-3.1.5.tar.gz", hash = "sha256:cf0e3cf56142039133628b5acffe8ef0c12bc902d2aadd3e0fe5878dc08d1050", size = 186464, upload-time = "2024-06-28T14:03:44.161Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c0/da/977ded879c29cbd04de313843e76868e6e13408a94ed6b987245dc7c8506/openpyxl-3.1.5-py2.py3-none-any.whl", hash = "sha256:5282c12b107bffeef825f4617dc029afaf41d0ea60823bbb665ef3079dc79de2", size = 250910, upload-time = "2024-06-28T14:03:41.161Z" },
]

[[package]]
name = "oracledb"
version = "2.1.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cryptography" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/be/bd/36467d9aa1b5638260dbd6fd5cd74385e91165df607effc573d8181274b4/oracledb-2.1.2.tar.gz", hash = "sha256:3054bcc295d7378834ba7a5aceb865985e954915f9b07a843ea84c3824c6a0b2", size = 569595, upload-time = "2024-04-11T19:51:27.51Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/61/ff/57dba00343c578fe7fab59982406ff00faf8f0ce29f969d135071e0c30a3/oracledb-2.1.2-cp312-cp312-macosx_10_9_universal2.whl", hash = "sha256:08aa313b801dda950918168d3962ba59a617adce143e0c2bf1ee9b847695faaa", size = 3556093, upload-time = "2024-04-11T19:52:36.024Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/ae/8cc92b249695a5068c2ee922bb960af459495dc10d61be7f7e62eb1337aa/oracledb-2.1.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:de5c932b04d3bcdd22c71c0e5c5e1d16b6a3a2fc68dc472ee3a12e677461354c", size = 11651507, upload-time = "2024-04-11T19:52:42.034Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6b/83/0c1749571905fd0f13f08a1c4823bba2f1ec40604bfc515c8a801891ce4a/oracledb-2.1.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1d590caf39b1901bcba394fcda9815438faff0afaf374025f89ef5d65993d0a4", size = 11837372, upload-time = "2024-04-11T19:52:49.956Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/39/1c/98cb195cf6d9ca3388be1eb95bce823fcee1631a03af11c908fce6f86d65/oracledb-2.1.2-cp312-cp312-win32.whl", hash = "sha256:1e3ffdfe76c97d1ca13a3fecf239c96d3889015bb5b775dc22b947108044b01e", size = 1314279, upload-time = "2024-04-11T19:52:54.049Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d9/0c/10de170751fbe74233111dcfe5142f1cce7541673994b53892dbc210b7b6/oracledb-2.1.2-cp312-cp312-win_amd64.whl", hash = "sha256:8c1eaf8c74bb6de5772de768f2f3f5eb935ab935c633d3a012ddff7e691a2073", size = 1603173, upload-time = "2024-04-11T19:52:56.605Z" },
]

[[package]]
name = "orjson"
version = "3.10.12"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e0/04/bb9f72987e7f62fb591d6c880c0caaa16238e4e530cbc3bdc84a7372d75f/orjson-3.10.12.tar.gz", hash = "sha256:0a78bbda3aea0f9f079057ee1ee8a1ecf790d4f1af88dd67493c6b8ee52506ff", size = 5438647, upload-time = "2024-11-23T19:42:56.895Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a1/2f/989adcafad49afb535da56b95d8f87d82e748548b2a86003ac129314079c/orjson-3.10.12-cp312-cp312-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl", hash = "sha256:53206d72eb656ca5ac7d3a7141e83c5bbd3ac30d5eccfe019409177a57634b0d", size = 248678, upload-time = "2024-11-23T19:41:33.346Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/69/b9/8c075e21a50c387649db262b618ebb7e4d40f4197b949c146fc225dd23da/orjson-3.10.12-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ac8010afc2150d417ebda810e8df08dd3f544e0dd2acab5370cfa6bcc0662f8f", size = 136763, upload-time = "2024-11-23T19:41:35.539Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/87/d3/78edf10b4ab14c19f6d918cf46a145818f4aca2b5a1773c894c5490d3a4c/orjson-3.10.12-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:ed459b46012ae950dd2e17150e838ab08215421487371fa79d0eced8d1461d70", size = 149137, upload-time = "2024-11-23T19:41:36.937Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/81/5db8852bdf990a0ddc997fa8f16b80895b8cc77c0fe3701569ed2b4b9e78/orjson-3.10.12-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8dcb9673f108a93c1b52bfc51b0af422c2d08d4fc710ce9c839faad25020bb69", size = 140567, upload-time = "2024-11-23T19:41:38.353Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fa/a6/9ce1e3e3db918512efadad489630c25841eb148513d21dab96f6b4157fa1/orjson-3.10.12-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:22a51ae77680c5c4652ebc63a83d5255ac7d65582891d9424b566fb3b5375ee9", size = 156620, upload-time = "2024-11-23T19:41:39.689Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/47/d4/05133d6bea24e292d2f7628b1e19986554f7d97b6412b3e51d812e38db2d/orjson-3.10.12-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:910fdf2ac0637b9a77d1aad65f803bac414f0b06f720073438a7bd8906298192", size = 131555, upload-time = "2024-11-23T19:41:41.172Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b9/7a/b3fbffda8743135c7811e95dc2ab7cdbc5f04999b83c2957d046f1b3fac9/orjson-3.10.12-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:24ce85f7100160936bc2116c09d1a8492639418633119a2224114f67f63a4559", size = 139743, upload-time = "2024-11-23T19:41:42.636Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b5/13/95bbcc9a6584aa083da5ce5004ce3d59ea362a542a0b0938d884fd8790b6/orjson-3.10.12-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:8a76ba5fc8dd9c913640292df27bff80a685bed3a3c990d59aa6ce24c352f8fc", size = 131733, upload-time = "2024-11-23T19:41:44.184Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e8/29/dddbb2ea6e7af426fcc3da65a370618a88141de75c6603313d70768d1df1/orjson-3.10.12-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:ff70ef093895fd53f4055ca75f93f047e088d1430888ca1229393a7c0521100f", size = 415788, upload-time = "2024-11-23T19:41:45.612Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/53/df/4aea59324ac539975919b4705ee086aced38e351a6eb3eea0f5071dd5661/orjson-3.10.12-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:f4244b7018b5753ecd10a6d324ec1f347da130c953a9c88432c7fbc8875d13be", size = 142347, upload-time = "2024-11-23T19:41:48.128Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/55/55/a52d83d7c49f8ff44e0daab10554490447d6c658771569e1c662aa7057fe/orjson-3.10.12-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:16135ccca03445f37921fa4b585cff9a58aa8d81ebcb27622e69bfadd220b32c", size = 130829, upload-time = "2024-11-23T19:41:49.702Z" },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a1/d4/1fc4078c65507b51b96ca8f8c3ba19e6a61c8253c72794544580a7b6c24d/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f", size = 165727, upload-time = "2025-04-19T11:48:59.673Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484", size = 66469, upload-time = "2025-04-19T11:48:57.875Z" },
]

[[package]]
name = "palladium"
version = "0.5.7"
source = { registry = "http://**********:3141/cheftin/pypi" }
resolution-markers = [
    "sys_platform == 'darwin'",
]
dependencies = [
    { name = "cython", marker = "sys_platform == 'darwin'" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/4ea/9341f26c6121e/palladium-0.5.7-cp312-cp312-macosx_13_0_arm64.whl", hash = "sha256:4ea9341f26c6121e16e8eab9c8ea3b6638f2ba35df3ad268d53e11860e4cb656" },
]

[[package]]
name = "palladium"
version = "0.5.19"
source = { registry = "http://**********:3141/cheftin/pypi" }
resolution-markers = [
    "platform_machine == 'aarch64' and sys_platform == 'linux'",
    "platform_machine != 'aarch64' and sys_platform == 'linux'",
]
dependencies = [
    { name = "cython", marker = "sys_platform == 'linux'" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/097/8dd0ddc2c25c0/palladium-0.5.19-cp312-cp312-manylinux_2_27_aarch64.whl", hash = "sha256:0978dd0ddc2c25c04442eda727bfee84c779da9b557611f404ab3998f88cbe15" },
    { url = "http://**********:3141/cheftin/pypi/+f/77c/cc21384afc99e/palladium-0.5.19-cp312-cp312-manylinux_2_27_x86_64.whl", hash = "sha256:77ccc21384afc99e7c54ff345fb2e21ddab93b78ef8b644fdda4c1a64e256e9f" },
]

[[package]]
name = "pandas"
version = "2.2.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "numpy" },
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "tzdata" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9c/d6/9f8431bacc2e19dca897724cd097b1bb224a6ad5433784a44b587c7c13af/pandas-2.2.3.tar.gz", hash = "sha256:4f18ba62b61d7e192368b84517265a99b4d7ee8912f8708660fb4a366cc82667", size = 4399213, upload-time = "2024-09-20T13:10:04.827Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/17/a3/fb2734118db0af37ea7433f57f722c0a56687e14b14690edff0cdb4b7e58/pandas-2.2.3-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:b1d432e8d08679a40e2a6d8b2f9770a5c21793a6f9f47fdd52c5ce1948a5a8a9", size = 12529893, upload-time = "2024-09-20T13:09:09.655Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e1/0c/ad295fd74bfac85358fd579e271cded3ac969de81f62dd0142c426b9da91/pandas-2.2.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:a5a1595fe639f5988ba6a8e5bc9649af3baf26df3998a0abe56c02609392e0a4", size = 11363475, upload-time = "2024-09-20T13:09:14.718Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c6/2a/4bba3f03f7d07207481fed47f5b35f556c7441acddc368ec43d6643c5777/pandas-2.2.3-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:5de54125a92bb4d1c051c0659e6fcb75256bf799a732a87184e5ea503965bce3", size = 15188645, upload-time = "2024-09-20T19:02:03.88Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/38/f8/d8fddee9ed0d0c0f4a2132c1dfcf0e3e53265055da8df952a53e7eaf178c/pandas-2.2.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fffb8ae78d8af97f849404f21411c95062db1496aeb3e56f146f0355c9989319", size = 12739445, upload-time = "2024-09-20T13:09:17.621Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/20/e8/45a05d9c39d2cea61ab175dbe6a2de1d05b679e8de2011da4ee190d7e748/pandas-2.2.3-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:6dfcb5ee8d4d50c06a51c2fffa6cff6272098ad6540aed1a76d15fb9318194d8", size = 16359235, upload-time = "2024-09-20T19:02:07.094Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1d/99/617d07a6a5e429ff90c90da64d428516605a1ec7d7bea494235e1c3882de/pandas-2.2.3-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:062309c1b9ea12a50e8ce661145c6aab431b1e99530d3cd60640e255778bd43a", size = 14056756, upload-time = "2024-09-20T13:09:20.474Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/29/d4/1244ab8edf173a10fd601f7e13b9566c1b525c4f365d6bee918e68381889/pandas-2.2.3-cp312-cp312-win_amd64.whl", hash = "sha256:59ef3764d0fe818125a5097d2ae867ca3fa64df032331b7e0917cf5d7bf66b13", size = 11504248, upload-time = "2024-09-20T13:09:23.137Z" },
]

[[package]]
name = "pdfkit"
version = "1.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/58/bb/6ddc62b4622776a6514fd749041c2b4bccd343e006d00de590f8090ac8b1/pdfkit-1.0.0.tar.gz", hash = "sha256:992f821e1e18fc8a0e701ecae24b51a2d598296a180caee0a24c0af181da02a9", size = 13288, upload-time = "2021-11-14T19:28:51.672Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/64/1b/26c080096dd93936dccfd32c682bed3d5630a84aae9d493ff68afb2ae0fb/pdfkit-1.0.0-py3-none-any.whl", hash = "sha256:a7a4ca0d978e44fa8310c4909f087052430a6e8e0b1dd7ceef657f139789f96f", size = 12099, upload-time = "2021-11-14T19:28:50.44Z" },
]

[[package]]
name = "pdfminer-six"
version = "20191110"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "chardet", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "pycryptodome", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "six", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "sortedcontainers", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e8/31/7acc148333749d6a8ef7cbf25902bdf59a462811a69d040a9a259916b6bd/pdfminer.six-20191110.tar.gz", hash = "sha256:141a53ec491bee6d45bf9b2c7f82601426fb5d32636bcf6b9c8a8f3b6431fea6", size = 10280313, upload-time = "2019-11-10T11:31:02.556Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/83/200b2723bcbf1d1248a8a7d16e6dd6cb970b5331397b11948428d7ebcf37/pdfminer.six-20191110-py2.py3-none-any.whl", hash = "sha256:ca2ca58f3ac66a486bce53a6ddba95dc2b27781612915fa41c444790ba9cd2a8", size = 5606096, upload-time = "2019-11-10T11:30:50.803Z" },
]

[[package]]
name = "pdfparser"
version = "2.3.24"
source = { registry = "http://**********:3141/cheftin/pypi" }
resolution-markers = [
    "sys_platform == 'darwin'",
]
dependencies = [
    { name = "asyncio-pool", marker = "sys_platform == 'darwin'" },
    { name = "chardet", marker = "sys_platform == 'darwin'" },
    { name = "cv-algorithms", marker = "sys_platform == 'darwin'" },
    { name = "faker", marker = "sys_platform == 'darwin'" },
    { name = "fonttools", marker = "sys_platform == 'darwin'" },
    { name = "grpcio", marker = "sys_platform == 'darwin'" },
    { name = "grpcio-tools", marker = "sys_platform == 'darwin'" },
    { name = "handright", marker = "sys_platform == 'darwin'" },
    { name = "hanziconv", marker = "sys_platform == 'darwin'" },
    { name = "imutils", marker = "sys_platform == 'darwin'" },
    { name = "minio", marker = "sys_platform == 'darwin'" },
    { name = "nest-asyncio", marker = "sys_platform == 'darwin'" },
    { name = "numpy", marker = "sys_platform == 'darwin'" },
    { name = "opencv-python", version = "4.7.0.72", source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }, marker = "sys_platform == 'darwin'" },
    { name = "orjson", marker = "sys_platform == 'darwin'" },
    { name = "palladium", version = "0.5.7", source = { registry = "http://**********:3141/cheftin/pypi" }, marker = "sys_platform == 'darwin'" },
    { name = "pdfminer-six", marker = "sys_platform == 'darwin'" },
    { name = "pikepdf", marker = "sys_platform == 'darwin'" },
    { name = "pillow", marker = "sys_platform == 'darwin'" },
    { name = "pip", marker = "sys_platform == 'darwin'" },
    { name = "protobuf", marker = "sys_platform == 'darwin'" },
    { name = "pycryptodome", marker = "sys_platform == 'darwin'" },
    { name = "pymupdf", marker = "sys_platform == 'darwin'" },
    { name = "pyyaml", marker = "sys_platform == 'darwin'" },
    { name = "qrcode", marker = "sys_platform == 'darwin'" },
    { name = "requests", marker = "sys_platform == 'darwin'" },
    { name = "rtree", marker = "sys_platform == 'darwin'" },
    { name = "scikit-image", marker = "sys_platform == 'darwin'" },
    { name = "six", marker = "sys_platform == 'darwin'" },
    { name = "symspellpy", marker = "sys_platform == 'darwin'" },
    { name = "wand", marker = "sys_platform == 'darwin'" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/2b4/3f0e02325688b/pdfparser-2.3.24-cp312-cp312-macosx_10_14_arm64.whl", hash = "sha256:2b43f0e02325688b5c40b92825058d7aa728b1794bd3adfaa067036893eade6f" },
    { url = "http://**********:3141/cheftin/pypi/+f/2b4/3f0e02325688b/pdfparser-2.3.24-cp312-cp312-macosx_13_0_arm64.whl", hash = "sha256:2b43f0e02325688b5c40b92825058d7aa728b1794bd3adfaa067036893eade6f" },
]

[[package]]
name = "pdfparser"
version = "2.3.38"
source = { registry = "http://**********:3141/cheftin/pypi" }
resolution-markers = [
    "platform_machine == 'aarch64' and sys_platform == 'linux'",
    "platform_machine != 'aarch64' and sys_platform == 'linux'",
]
dependencies = [
    { name = "asyncio-pool", marker = "sys_platform == 'linux'" },
    { name = "chardet", marker = "sys_platform == 'linux'" },
    { name = "cv-algorithms", marker = "sys_platform == 'linux'" },
    { name = "faker", marker = "sys_platform == 'linux'" },
    { name = "fonttools", marker = "sys_platform == 'linux'" },
    { name = "grpcio", marker = "sys_platform == 'linux'" },
    { name = "grpcio-tools", marker = "sys_platform == 'linux'" },
    { name = "handright", marker = "sys_platform == 'linux'" },
    { name = "hanziconv", marker = "sys_platform == 'linux'" },
    { name = "imutils", marker = "sys_platform == 'linux'" },
    { name = "minio", marker = "sys_platform == 'linux'" },
    { name = "nest-asyncio", marker = "sys_platform == 'linux'" },
    { name = "numpy", marker = "sys_platform == 'linux'" },
    { name = "opencv-python", version = "4.11.0.86", source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }, marker = "sys_platform == 'linux'" },
    { name = "orjson", marker = "sys_platform == 'linux'" },
    { name = "palladium", version = "0.5.19", source = { registry = "http://**********:3141/cheftin/pypi" }, marker = "sys_platform == 'linux'" },
    { name = "pdfminer-six", marker = "sys_platform == 'linux'" },
    { name = "pikepdf", marker = "sys_platform == 'linux'" },
    { name = "pillow", marker = "sys_platform == 'linux'" },
    { name = "pip", marker = "sys_platform == 'linux'" },
    { name = "protobuf", marker = "sys_platform == 'linux'" },
    { name = "pycryptodome", marker = "sys_platform == 'linux'" },
    { name = "pymupdf", marker = "sys_platform == 'linux'" },
    { name = "pyyaml", marker = "sys_platform == 'linux'" },
    { name = "qrcode", marker = "sys_platform == 'linux'" },
    { name = "requests", marker = "sys_platform == 'linux'" },
    { name = "rtree", marker = "sys_platform == 'linux'" },
    { name = "scikit-image", marker = "sys_platform == 'linux'" },
    { name = "six", marker = "sys_platform == 'linux'" },
    { name = "symspellpy", marker = "sys_platform == 'linux'" },
    { name = "wand", marker = "sys_platform == 'linux'" },
]
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/ac1/1540dd0028520/pdfparser-2.3.38-cp312-cp312-manylinux_2_27_aarch64.whl", hash = "sha256:ac11540dd0028520bd1ec07ffa6352fd8aab94e01e2b08353173c624e8299949" },
    { url = "http://**********:3141/cheftin/pypi/+f/8ed/fe8f6e3247095/pdfparser-2.3.38-cp312-cp312-manylinux_2_27_x86_64.whl", hash = "sha256:8edfe8f6e32470954fc2a32b199ad1202affb6df60d87b0a72ad0ed50b1865a8" },
]

[[package]]
name = "peewee"
version = "3.18.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/04/89/76f6f1b744c8608e0d416b588b9d63c2a500ff800065ae610f7c80f532d6/peewee-3.18.2.tar.gz", hash = "sha256:77a54263eb61aff2ea72f63d2eeb91b140c25c1884148e28e4c0f7c4f64996a0", size = 949220, upload-time = "2025-07-08T12:52:03.941Z" }

[[package]]
name = "peewee-async"
version = "0.10.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "peewee" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/51/4d/db12182d2fe59dca44a7434820e505fcf50bbbf5cac69a18039ffed6d929/peewee_async-0.10.0.tar.gz", hash = "sha256:d6069c9fe8999f9822ece01aa08a9e1051b1e9f63901455bc749489232cf20f3", size = 14550, upload-time = "2024-01-15T11:19:55.831Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/27/6d/1b8395e95b16e0b4fc73ab18f6a6b4e7722b3d1f067aa01de5aa569248b7/peewee_async-0.10.0-py3-none-any.whl", hash = "sha256:778c43a2a5b7d2e77241a50aac8dd2bba3ca135a565588a493fe1d0b002f289a", size = 14582, upload-time = "2024-01-15T11:19:54.684Z" },
]

[[package]]
name = "pgvector"
version = "0.3.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7d/d8/fd6009cee3e03214667df488cdcf9609461d729968da94e4f95d6359d304/pgvector-0.3.6.tar.gz", hash = "sha256:31d01690e6ea26cea8a633cde5f0f55f5b246d9c8292d68efdef8c22ec994ade", size = 25421, upload-time = "2024-10-27T00:15:09.632Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fb/81/f457d6d361e04d061bef413749a6e1ab04d98cfeec6d8abcfe40184750f3/pgvector-0.3.6-py3-none-any.whl", hash = "sha256:f6c269b3c110ccb7496bac87202148ed18f34b390a0189c783e351062400a75a", size = 24880, upload-time = "2024-10-27T00:15:08.045Z" },
]

[[package]]
name = "pika"
version = "1.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fc/89/26d3054216d869901dd461f3de1f5b35802bcc3834d1831ebf62ad16ac1e/pika-1.2.0.tar.gz", hash = "sha256:f023d6ac581086b124190cb3dc81dd581a149d216fa4540ac34f9be1e3970b89", size = 144945, upload-time = "2021-02-05T02:03:04.331Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f5/56/2590c41852df1212426bec3e5e312cba50170e12d083a0fb1e544a52d215/pika-1.2.0-py2.py3-none-any.whl", hash = "sha256:59da6701da1aeaf7e5e93bb521cc03129867f6e54b7dd352c4b3ecb2bd7ec624", size = 154322, upload-time = "2021-02-05T02:03:02.617Z" },
]

[[package]]
name = "pikepdf"
version = "8.15.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "deprecated", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "lxml", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "packaging", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "pillow", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7c/47/87a19cc2af6e82c00d79e9bcd75d9ce3a964943386f44e441d76d086c674/pikepdf-8.15.1.tar.gz", hash = "sha256:653aceedb2b3a4cdb579a55aaf78632b434b7cd53e2557b2bc8c8590bfe1d92b", size = 2870723, upload-time = "2024-04-16T09:27:21.252Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/65/9b/9b388a5e3eef1f997b692a7dc217abe5a0c1064279a001f209db9352d9fe/pikepdf-8.15.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:77a58bf66222bdb1ab32840ac77e1354b0b9733fe06336e4fa3ce54d8acba70c", size = 4710503, upload-time = "2024-04-16T09:26:15.156Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/c3/59afaa877b29b27e1f936c074c61f96cde86cdc12412808a0f1551f2cf61/pikepdf-8.15.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:2253abe1e779c247dd55384a9b2d6c6c0e1784194302a8a72f995cdb7aaebada", size = 4374305, upload-time = "2024-04-16T09:26:17.479Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/84/3a/4c0ec3e913a963e1924151b6ff23b70f5313630c93fc4a39d4bd54b7dab7/pikepdf-8.15.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a8ba8ed8dc612fc310ce35ccc90f9641fcfb258343ab619e80086089ce8e60bf", size = 2231003, upload-time = "2024-04-16T09:26:19.684Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/24/fc/2c39d4fd7be65a0f62ee53292b77fa95a1408653e79ae134155804d4c7f0/pikepdf-8.15.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:7d95bcbbd3a89bbe0817f13b46a1a0c31715e78ef8d40e57d0c09931a6bf74b7", size = 2372919, upload-time = "2024-04-16T09:26:21.861Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0d/d9/2c7657aa91b5ed31bc49fc713c343aa404ac6cfb0784588268d14363a8b5/pikepdf-8.15.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:f656b19c4d436e6f6d272cc952b0386e404402d3eadba49a88240ee0e3ebaca1", size = 3201338, upload-time = "2024-04-16T09:26:24.139Z" },
]

[[package]]
name = "pillow"
version = "10.3.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ef/43/c50c17c5f7d438e836c169e343695534c38c77f60e7c90389bd77981bc21/pillow-10.3.0.tar.gz", hash = "sha256:9d2455fbf44c914840c793e89aa82d0e1763a14253a000743719ae5946814b2d", size = 46572854, upload-time = "2024-04-01T12:19:40.048Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cc/5d/b7fcd38cba0f7706f64c1674fc9f018e4c64f791770598c44affadea7c2f/pillow-10.3.0-cp312-cp312-macosx_10_10_x86_64.whl", hash = "sha256:e46f38133e5a060d46bd630faa4d9fa0202377495df1f068a8299fd78c84de84", size = 3528535, upload-time = "2024-04-01T12:17:55.891Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5e/77/4cf407e7b033b4d8e5fcaac295b6e159cf1c70fa105d769f01ea2e1e5eca/pillow-10.3.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:50b8eae8f7334ec826d6eeffaeeb00e36b5e24aa0b9df322c247539714c6df19", size = 3352281, upload-time = "2024-04-01T12:17:58.527Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/53/7b/4f7b153a776725a87797d744ea1c73b83ac0b723f5e379297605dee118eb/pillow-10.3.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9d3bea1c75f8c53ee4d505c3e67d8c158ad4df0d83170605b50b64025917f338", size = 4321427, upload-time = "2024-04-01T12:18:00.809Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/45/08/d2cc751b790e77464f8648aa707e2327d6da5d95cf236a532e99c2e7a499/pillow-10.3.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:19aeb96d43902f0a783946a0a87dbdad5c84c936025b8419da0a0cd7724356b1", size = 4435915, upload-time = "2024-04-01T12:18:03.084Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ef/97/f69d1932cf45bf5bd9fa1e2ae57bdf716524faa4fa9fb7dc62cdb1a19113/pillow-10.3.0-cp312-cp312-manylinux_2_28_aarch64.whl", hash = "sha256:74d28c17412d9caa1066f7a31df8403ec23d5268ba46cd0ad2c50fb82ae40462", size = 4347392, upload-time = "2024-04-01T12:18:05.319Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c6/c1/3521ddb9c1f3ac106af3e4512a98c785b6ed8a39e0f778480b8a4d340165/pillow-10.3.0-cp312-cp312-manylinux_2_28_x86_64.whl", hash = "sha256:ff61bfd9253c3915e6d41c651d5f962da23eda633cf02262990094a18a55371a", size = 4514536, upload-time = "2024-04-01T12:18:08.039Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c0/6f/347c241904a6514e59515284b01ba6f61765269a0d1a19fd2e6cbe331c8a/pillow-10.3.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:d886f5d353333b4771d21267c7ecc75b710f1a73d72d03ca06df49b09015a9ef", size = 4555987, upload-time = "2024-04-01T12:18:10.106Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c3/e2/3cc490c6b2e262713da82ce849c34bd8e6c31242afb53be8595d820b9877/pillow-10.3.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:4b5ec25d8b17217d635f8935dbc1b9aa5907962fae29dff220f2659487891cd3", size = 4623526, upload-time = "2024-04-01T12:18:12.172Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c1/b3/0209f70fa29b383e7618e47db95712a45788dea03bb960601753262a2883/pillow-10.3.0-cp312-cp312-win32.whl", hash = "sha256:51243f1ed5161b9945011a7360e997729776f6e5d7005ba0c6879267d4c5139d", size = 2217547, upload-time = "2024-04-01T12:18:14.188Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d3/23/3927d888481ff7c44fdbca3bc2a2e97588c933db46723bf115201377c436/pillow-10.3.0-cp312-cp312-win_amd64.whl", hash = "sha256:412444afb8c4c7a6cc11a47dade32982439925537e483be7c0ae0cf96c4f6a0b", size = 2531641, upload-time = "2024-04-01T12:18:16.081Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/db/36/1ecaa0541d3a1b1362f937d386eeb1875847bfa06d5225f1b0e1588d1007/pillow-10.3.0-cp312-cp312-win_arm64.whl", hash = "sha256:798232c92e7665fe82ac085f9d8e8ca98826f8e27859d9a96b41d519ecd2e49a", size = 2229746, upload-time = "2024-04-01T12:18:18.174Z" },
]

[[package]]
name = "pip"
version = "25.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/20/16/650289cd3f43d5a2fadfd98c68bd1e1e7f2550a1a5326768cddfbcedb2c5/pip-25.2.tar.gz", hash = "sha256:578283f006390f85bb6282dffb876454593d637f5d1be494b5202ce4877e71f2", size = 1840021, upload-time = "2025-07-30T21:50:15.401Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b7/3f/945ef7ab14dc4f9d7f40288d2df998d1837ee0888ec3659c813487572faa/pip-25.2-py3-none-any.whl", hash = "sha256:6d67a2b4e7f14d8b31b8b52648866fa717f45a1eb70e83002f4331d07e953717", size = 1752557, upload-time = "2025-07-30T21:50:13.323Z" },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fe/8b/3c73abc9c759ecd3f1f7ceff6685840859e8070c4d947c93fae71f6a0bf2/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc", size = 21362, upload-time = "2025-05-07T22:47:42.121Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4", size = 18567, upload-time = "2025-05-07T22:47:40.376Z" },
]

[[package]]
name = "playwright"
version = "1.43.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "greenlet" },
    { name = "pyee" },
]
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/62/10/f0638cbcd25d2e5d06e23aa55f4a575b062e6f35670a46adc53dd5ebcc63/playwright-1.43.0-py3-none-macosx_10_13_x86_64.whl", hash = "sha256:b03b12bd4da9c2cfb78dff820deac8b52892fe3c2f89a4d95d6f08c59e41deb9", size = 34294956, upload-time = "2024-04-09T10:38:04.736Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4d/fc/e7c1a8696911e5faaa5c480f649deccf633aa2a235c91664748471490576/playwright-1.43.0-py3-none-macosx_11_0_arm64.whl", hash = "sha256:e9ec21b141727392f630761c7f4dec46d80c98243614257cc501b64ff636d337", size = 32632083, upload-time = "2024-04-09T10:38:11.038Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e2/c8/ad2c5ca177c0665648dff1f03d45a965fdcae7ff43849947c98e90f0082e/playwright-1.43.0-py3-none-macosx_11_0_universal2.whl", hash = "sha256:e05a8d8fb2040c630429cca07e843c8fa33059717837c8f50c01b7d1fc651ce1", size = 34294953, upload-time = "2024-04-09T10:38:15.983Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/5c/fe3e42ea46038ec69a0c0fd4b8f8d9a633577f5b3c0368b67d73ee33e910/playwright-1.43.0-py3-none-manylinux1_x86_64.whl", hash = "sha256:50d9a5c07c76456945a2296d63f78fdf6eb11aed3e8d39bb5ccbda760a8d6d41", size = 37346891, upload-time = "2024-04-09T10:38:20.963Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e4/5a/511e97a2a08a49628ab69f5ad2892e893a177ed69cdb0663c30586bfee63/playwright-1.43.0-py3-none-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:87191272c40b4c282cf2c9449ca3acaf705f38ac6e2372270f1617ce16b661b8", size = 37093964, upload-time = "2024-04-09T10:38:25.811Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4a/a3/c3d10726e3f9ffbc50a3dbf6566aefd806f614af4807674f3c07ea357a52/playwright-1.43.0-py3-none-win32.whl", hash = "sha256:bd8b818904b17e2914be23e7bc2a340b203f57fe81678520b10f908485b056ea", size = 29407139, upload-time = "2024-04-09T10:38:31.133Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/33/a1/0bfb6705415a7a268b693b10fbbf6638bcbf86a8ea7db735f67ef6252cf7/playwright-1.43.0-py3-none-win_amd64.whl", hash = "sha256:9b7bd707eeeaebee47f656b2de90aa9bd85e9ca2c6af7a08efd73896299e4d50", size = 29407143, upload-time = "2024-04-09T10:38:35.502Z" },
]

[[package]]
name = "pluggy"
version = "1.6.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f9/e2/3e91f31a7d2b083fe6ef3fa267035b518369d9511ffab804f839851d2779/pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3", size = 69412, upload-time = "2025-05-15T12:30:07.975Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746", size = 20538, upload-time = "2025-05-15T12:30:06.134Z" },
]

[[package]]
name = "pre-commit"
version = "4.0.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cfgv" },
    { name = "identify" },
    { name = "nodeenv" },
    { name = "pyyaml" },
    { name = "virtualenv" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2e/c8/e22c292035f1bac8b9f5237a2622305bc0304e776080b246f3df57c4ff9f/pre_commit-4.0.1.tar.gz", hash = "sha256:80905ac375958c0444c65e9cebebd948b3cdb518f335a091a670a89d652139d2", size = 191678, upload-time = "2024-10-08T16:09:37.641Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/8f/496e10d51edd6671ebe0432e33ff800aa86775d2d147ce7d43389324a525/pre_commit-4.0.1-py2.py3-none-any.whl", hash = "sha256:efde913840816312445dc98787724647c65473daefe420785f885e8ed9a06878", size = 218713, upload-time = "2024-10-08T16:09:35.726Z" },
]

[[package]]
name = "prompt-toolkit"
version = "3.0.51"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "wcwidth" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/6e/9d084c929dfe9e3bfe0c6a47e31f78a25c54627d64a66e884a8bf5474f1c/prompt_toolkit-3.0.51.tar.gz", hash = "sha256:931a162e3b27fc90c86f1b48bb1fb2c528c2761475e57c9c06de13311c7b54ed", size = 428940, upload-time = "2025-04-15T09:18:47.731Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ce/4f/5249960887b1fbe561d9ff265496d170b55a735b76724f10ef19f9e40716/prompt_toolkit-3.0.51-py3-none-any.whl", hash = "sha256:52742911fde84e2d423e2f9a4cf1de7d7ac4e51958f648d9540e0fb8db077b07", size = 387810, upload-time = "2025-04-15T09:18:44.753Z" },
]

[[package]]
name = "protobuf"
version = "4.24.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/52/5c/f2c0778278259089952f94b0884ca27a001a17ffbd992ebe30c841085f4c/protobuf-4.24.4.tar.gz", hash = "sha256:5a70731910cd9104762161719c3d883c960151eea077134458503723b60e3667", size = 383850, upload-time = "2023-10-04T17:08:17.627Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/00/0f/3dc2f86e9c3d6e73c56d915a3563ecc96ebee8144fb39614f0d6c1fb023d/protobuf-4.24.4-cp310-abi3-win32.whl", hash = "sha256:ec9912d5cb6714a5710e28e592ee1093d68c5ebfeda61983b3f40331da0b1ebb", size = 409968, upload-time = "2023-10-04T17:07:54.375Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c2/59/f89c04923d68595d359f4cd7adbbdf5e5d791257945f8873d88b2fd1f979/protobuf-4.24.4-cp310-abi3-win_amd64.whl", hash = "sha256:1badab72aa8a3a2b812eacfede5020472e16c6b2212d737cefd685884c191085", size = 430493, upload-time = "2023-10-04T17:07:56.476Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/88/12/efb5896c901382548ecb58d0449885a8f9aa62bb559d65e5a8a47f122629/protobuf-4.24.4-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:8e61a27f362369c2f33248a0ff6896c20dcd47b5d48239cb9720134bef6082e4", size = 409417, upload-time = "2023-10-04T17:07:58.479Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/db/61/9c7b481771fe4702fb3be1152812fecec9b06f9c36d523ad52b98cb46800/protobuf-4.24.4-cp37-abi3-manylinux2014_aarch64.whl", hash = "sha256:bffa46ad9612e6779d0e51ae586fde768339b791a50610d85eb162daeb23661e", size = 310587, upload-time = "2023-10-04T17:08:00.391Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c8/2c/03046cac73f46bfe98fc846ef629cf4f84c2f59258216aa2cc0d22bfca8f/protobuf-4.24.4-cp37-abi3-manylinux2014_x86_64.whl", hash = "sha256:b493cb590960ff863743b9ff1452c413c2ee12b782f48beca77c8da3e2ffe9d9", size = 311559, upload-time = "2023-10-04T17:08:02.672Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e5/a7/bb962b8b981dd890a44a34d0e922b76c32e5db443ff9f9b9ce6149069070/protobuf-4.24.4-py3-none-any.whl", hash = "sha256:80797ce7424f8c8d2f2547e2d42bfbb6c08230ce5832d6c099a37335c9c90a92", size = 175677, upload-time = "2023-10-04T17:08:15.684Z" },
]

[[package]]
name = "psutil"
version = "7.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/80/336820c1ad9286a4ded7e845b2eccfcb27851ab8ac6abece774a6ff4d3de/psutil-7.0.0.tar.gz", hash = "sha256:7be9c3eba38beccb6495ea33afd982a44074b78f28c434a1f51cc07fd315c456", size = 497003, upload-time = "2025-02-13T21:54:07.946Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ed/e6/2d26234410f8b8abdbf891c9da62bee396583f713fb9f3325a4760875d22/psutil-7.0.0-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:101d71dc322e3cffd7cea0650b09b3d08b8e7c4109dd6809fe452dfd00e58b25", size = 238051, upload-time = "2025-02-13T21:54:12.36Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/04/8b/30f930733afe425e3cbfc0e1468a30a18942350c1a8816acfade80c005c4/psutil-7.0.0-cp36-abi3-macosx_11_0_arm64.whl", hash = "sha256:39db632f6bb862eeccf56660871433e111b6ea58f2caea825571951d4b6aa3da", size = 239535, upload-time = "2025-02-13T21:54:16.07Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/ed/d362e84620dd22876b55389248e522338ed1bf134a5edd3b8231d7207f6d/psutil-7.0.0-cp36-abi3-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1fcee592b4c6f146991ca55919ea3d1f8926497a713ed7faaf8225e174581e91", size = 275004, upload-time = "2025-02-13T21:54:18.662Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bf/b9/b0eb3f3cbcb734d930fdf839431606844a825b23eaf9a6ab371edac8162c/psutil-7.0.0-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4b1388a4f6875d7e2aff5c4ca1cc16c545ed41dd8bb596cefea80111db353a34", size = 277986, upload-time = "2025-02-13T21:54:21.811Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/eb/a2/709e0fe2f093556c17fbafda93ac032257242cabcc7ff3369e2cb76a97aa/psutil-7.0.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a5f098451abc2828f7dc6b58d44b532b22f2088f4999a937557b603ce72b1993", size = 279544, upload-time = "2025-02-13T21:54:24.68Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/e6/eecf58810b9d12e6427369784efe814a1eec0f492084ce8eb8f4d89d6d61/psutil-7.0.0-cp37-abi3-win32.whl", hash = "sha256:ba3fcef7523064a6c9da440fc4d6bd07da93ac726b5733c29027d7dc95b39d99", size = 241053, upload-time = "2025-02-13T21:54:34.31Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/1b/6921afe68c74868b4c9fa424dad3be35b095e16687989ebbb50ce4fceb7c/psutil-7.0.0-cp37-abi3-win_amd64.whl", hash = "sha256:4cf3d4eb1aa9b348dec30105c55cd9b7d4629285735a102beb4441e38db90553", size = 244885, upload-time = "2025-02-13T21:54:37.486Z" },
]

[[package]]
name = "psycopg2-binary"
version = "2.9.10"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/0e/bdc8274dc0585090b4e3432267d7be4dfbfd8971c0fa59167c711105a6bf/psycopg2-binary-2.9.10.tar.gz", hash = "sha256:4b3df0e6990aa98acda57d983942eff13d824135fe2250e6522edaa782a06de2", size = 385764, upload-time = "2024-10-16T11:24:58.126Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/49/7d/465cc9795cf76f6d329efdafca74693714556ea3891813701ac1fee87545/psycopg2_binary-2.9.10-cp312-cp312-macosx_12_0_x86_64.whl", hash = "sha256:880845dfe1f85d9d5f7c412efea7a08946a46894537e4e5d091732eb1d34d9a0", size = 3044771, upload-time = "2024-10-16T11:20:35.234Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/31/6d225b7b641a1a2148e3ed65e1aa74fc86ba3fee850545e27be9e1de893d/psycopg2_binary-2.9.10-cp312-cp312-macosx_14_0_arm64.whl", hash = "sha256:9440fa522a79356aaa482aa4ba500b65f28e5d0e63b801abf6aa152a29bd842a", size = 3275336, upload-time = "2024-10-16T11:20:38.742Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/30/b7/a68c2b4bff1cbb1728e3ec864b2d92327c77ad52edcd27922535a8366f68/psycopg2_binary-2.9.10-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:e3923c1d9870c49a2d44f795df0c889a22380d36ef92440ff618ec315757e539", size = 2851637, upload-time = "2024-10-16T11:20:42.145Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/b1/cfedc0e0e6f9ad61f8657fd173b2f831ce261c02a08c0b09c652b127d813/psycopg2_binary-2.9.10-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7b2c956c028ea5de47ff3a8d6b3cc3330ab45cf0b7c3da35a2d6ff8420896526", size = 3082097, upload-time = "2024-10-16T11:20:46.185Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/ed/0a8e4153c9b769f59c02fb5e7914f20f0b2483a19dae7bf2db54b743d0d0/psycopg2_binary-2.9.10-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:f758ed67cab30b9a8d2833609513ce4d3bd027641673d4ebc9c067e4d208eec1", size = 3264776, upload-time = "2024-10-16T11:20:50.879Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/10/db/d09da68c6a0cdab41566b74e0a6068a425f077169bed0946559b7348ebe9/psycopg2_binary-2.9.10-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8cd9b4f2cfab88ed4a9106192de509464b75a906462fb846b936eabe45c2063e", size = 3020968, upload-time = "2024-10-16T11:20:56.819Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/94/28/4d6f8c255f0dfffb410db2b3f9ac5218d959a66c715c34cac31081e19b95/psycopg2_binary-2.9.10-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:6dc08420625b5a20b53551c50deae6e231e6371194fa0651dbe0fb206452ae1f", size = 2872334, upload-time = "2024-10-16T11:21:02.411Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/05/f7/20d7bf796593c4fea95e12119d6cc384ff1f6141a24fbb7df5a668d29d29/psycopg2_binary-2.9.10-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:d7cd730dfa7c36dbe8724426bf5612798734bff2d3c3857f36f2733f5bfc7c00", size = 2822722, upload-time = "2024-10-16T11:21:09.01Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4d/e4/0c407ae919ef626dbdb32835a03b6737013c3cc7240169843965cada2bdf/psycopg2_binary-2.9.10-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:155e69561d54d02b3c3209545fb08938e27889ff5a10c19de8d23eb5a41be8a5", size = 2920132, upload-time = "2024-10-16T11:21:16.339Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2d/70/aa69c9f69cf09a01da224909ff6ce8b68faeef476f00f7ec377e8f03be70/psycopg2_binary-2.9.10-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:c3cc28a6fd5a4a26224007712e79b81dbaee2ffb90ff406256158ec4d7b52b47", size = 2959312, upload-time = "2024-10-16T11:21:25.584Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d3/bd/213e59854fafe87ba47814bf413ace0dcee33a89c8c8c814faca6bc7cf3c/psycopg2_binary-2.9.10-cp312-cp312-win32.whl", hash = "sha256:ec8a77f521a17506a24a5f626cb2aee7850f9b69a0afe704586f63a464f3cd64", size = 1025191, upload-time = "2024-10-16T11:21:29.912Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/92/29/06261ea000e2dc1e22907dbbc483a1093665509ea586b29b8986a0e56733/psycopg2_binary-2.9.10-cp312-cp312-win_amd64.whl", hash = "sha256:18c5ee682b9c6dd3696dad6e54cc7ff3a1a9020df6a5c0f861ef8bfd338c3ca0", size = 1164031, upload-time = "2024-10-16T11:21:34.211Z" },
]

[[package]]
name = "py7zr"
version = "0.22.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "brotli", marker = "platform_python_implementation == 'CPython'" },
    { name = "brotlicffi", marker = "platform_python_implementation == 'PyPy'" },
    { name = "inflate64" },
    { name = "multivolumefile" },
    { name = "psutil", marker = "sys_platform != 'cygwin'" },
    { name = "pybcj" },
    { name = "pycryptodomex" },
    { name = "pyppmd" },
    { name = "pyzstd" },
    { name = "texttable" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/c3/0e05c711c16af0b9c47f3f77323303b338b9a871ba020d95d2b8dd6605ae/py7zr-0.22.0.tar.gz", hash = "sha256:c6c7aea5913535184003b73938490f9a4d8418598e533f9ca991d3b8e45a139e", size = 4992926, upload-time = "2024-08-08T13:10:01.514Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d0/59/dd1750002c0f46099281116f8165247bc62dc85edad41cdd26e7b26de19d/py7zr-0.22.0-py3-none-any.whl", hash = "sha256:993b951b313500697d71113da2681386589b7b74f12e48ba13cc12beca79d078", size = 67906, upload-time = "2024-08-08T13:09:58.092Z" },
]

[[package]]
name = "pybcj"
version = "1.0.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ce/75/bbcf098abf68081fa27c09d642790daa99d9156132c8b0893e3fecd946ab/pybcj-1.0.6.tar.gz", hash = "sha256:70bbe2dc185993351955bfe8f61395038f96f5de92bb3a436acb01505781f8f2", size = 2112413, upload-time = "2025-04-29T08:51:40.966Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4a/d2/42afc83cda05aed0aa77bcbd711c418437409b049ebd6c61d8d49afbd84e/pybcj-1.0.6-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f5d1dbc76f615595d7d8f3846c07f607fb1e2305d085c34556b32dacf8e88d12", size = 31805, upload-time = "2025-04-29T08:51:05.573Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b0/4f/81f46d380b61d9adc305065e966b8787c2d10650e60fc76eb1569f4ec9f2/pybcj-1.0.6-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:1398f556ed2afe16ae363a2b6e8cf6aeda3aa21861757286bc6c498278886c60", size = 23586, upload-time = "2025-04-29T08:51:07.044Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c7/b5/0d27992fbc24573aee833c1d1dc3d6fa035c9ecb19e453bde1e4babe6512/pybcj-1.0.6-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:e269cfc7b6286af87c5447c9f8c685f19cff011cac64947ffb4cd98919696a7f", size = 23961, upload-time = "2025-04-29T08:51:08.077Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c5/8d/359c8906b0337ed33e3bcb100640861a08222dbddc820b1382b58e1bf265/pybcj-1.0.6-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7393d0b0dcaa0b1a7850245def78fa14438809e9a3f73b1057a975229d623fd3", size = 51928, upload-time = "2025-04-29T08:51:09.515Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/03/4d/3ff0565ff69390f110c4d497ca96a8c757584c334dabad8451e35d6db210/pybcj-1.0.6-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e252891698d3e01d0f60eb5adfe849038cd2d429cb9510f915a0759301f1884d", size = 51670, upload-time = "2025-04-29T08:51:10.608Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1a/af/49401daf3e01014fc8576b24677b054399f891a20e6f13807a4cc0e06805/pybcj-1.0.6-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:ae5c891fcda9d5a6826a1b8e843b1e52811358594121553e6683e65b13eccce7", size = 56012, upload-time = "2025-04-29T08:51:11.646Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f5/fe/3e5e12e4d489f84e5e0ff7331cf39395c0b176e4ddae303697828d276a64/pybcj-1.0.6-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:eac3cb317df1cefed2783ce9cafdae61899dd02f2f4749dc0f4494a7c425745f", size = 55565, upload-time = "2025-04-29T08:51:12.739Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7b/42/6856e9913bbb72e77029e953dadd9d835ebd4222cd1856b90a37f494c353/pybcj-1.0.6-cp312-cp312-win_amd64.whl", hash = "sha256:72ebec5cda5a48de169c2d7548ea2ce7f48732de0175d7e0e665ca7360eaa4c4", size = 24874, upload-time = "2025-04-29T08:51:14.303Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7a/3e/94a5261ba2da1adb3555a2f0cebe2e9c1b23b0f91961cb6a268aea042a38/pybcj-1.0.6-cp312-cp312-win_arm64.whl", hash = "sha256:8f1f75a01e45d01ecf88d31910ca1ace5d345e3bfb7c18db0af3d0c393209b63", size = 23076, upload-time = "2025-04-29T08:51:15.323Z" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1d/b2/31537cf4b1ca988837256c910a668b553fceb8f069bedc4b1c826024b52c/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6", size = 172736, upload-time = "2024-03-30T13:22:22.564Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc", size = 117552, upload-time = "2024-03-30T13:22:20.476Z" },
]

[[package]]
name = "pycryptodome"
version = "3.21.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/13/52/13b9db4a913eee948152a079fe58d035bd3d1a519584155da8e786f767e6/pycryptodome-3.21.0.tar.gz", hash = "sha256:f7787e0d469bdae763b876174cf2e6c0f7be79808af26b1da96f1a64bcf47297", size = 4818071, upload-time = "2024-10-02T10:23:18.339Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/88/5e83de10450027c96c79dc65ac45e9d0d7a7fef334f39d3789a191f33602/pycryptodome-3.21.0-cp36-abi3-macosx_10_9_universal2.whl", hash = "sha256:2480ec2c72438430da9f601ebc12c518c093c13111a5c1644c82cdfc2e50b1e4", size = 2495937, upload-time = "2024-10-02T10:22:29.156Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/66/e1/8f28cd8cf7f7563319819d1e172879ccce2333781ae38da61c28fe22d6ff/pycryptodome-3.21.0-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:de18954104667f565e2fbb4783b56667f30fb49c4d79b346f52a29cb198d5b6b", size = 1634629, upload-time = "2024-10-02T10:22:31.82Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6a/c1/f75a1aaff0c20c11df8dc8e2bf8057e7f73296af7dfd8cbb40077d1c930d/pycryptodome-3.21.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2de4b7263a33947ff440412339cb72b28a5a4c769b5c1ca19e33dd6cd1dcec6e", size = 2168708, upload-time = "2024-10-02T10:22:34.5Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ea/66/6f2b7ddb457b19f73b82053ecc83ba768680609d56dd457dbc7e902c41aa/pycryptodome-3.21.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0714206d467fc911042d01ea3a1847c847bc10884cf674c82e12915cfe1649f8", size = 2254555, upload-time = "2024-10-02T10:22:37.259Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2c/2b/152c330732a887a86cbf591ed69bd1b489439b5464806adb270f169ec139/pycryptodome-3.21.0-cp36-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7d85c1b613121ed3dbaa5a97369b3b757909531a959d229406a75b912dd51dd1", size = 2294143, upload-time = "2024-10-02T10:22:39.909Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/55/92/517c5c498c2980c1b6d6b9965dffbe31f3cd7f20f40d00ec4069559c5902/pycryptodome-3.21.0-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:8898a66425a57bcf15e25fc19c12490b87bd939800f39a03ea2de2aea5e3611a", size = 2160509, upload-time = "2024-10-02T10:22:42.165Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/39/1f/c74288f54d80a20a78da87df1818c6464ac1041d10988bb7d982c4153fbc/pycryptodome-3.21.0-cp36-abi3-musllinux_1_2_i686.whl", hash = "sha256:932c905b71a56474bff8a9c014030bc3c882cee696b448af920399f730a650c2", size = 2329480, upload-time = "2024-10-02T10:22:44.482Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/39/1b/d0b013bf7d1af7cf0a6a4fce13f5fe5813ab225313755367b36e714a63f8/pycryptodome-3.21.0-cp36-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:18caa8cfbc676eaaf28613637a89980ad2fd96e00c564135bf90bc3f0b34dd93", size = 2254397, upload-time = "2024-10-02T10:22:46.875Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/14/71/4cbd3870d3e926c34706f705d6793159ac49d9a213e3ababcdade5864663/pycryptodome-3.21.0-cp36-abi3-win32.whl", hash = "sha256:280b67d20e33bb63171d55b1067f61fbd932e0b1ad976b3a184303a3dad22764", size = 1775641, upload-time = "2024-10-02T10:22:48.703Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/43/1d/81d59d228381576b92ecede5cd7239762c14001a828bdba30d64896e9778/pycryptodome-3.21.0-cp36-abi3-win_amd64.whl", hash = "sha256:b7aa25fc0baa5b1d95b7633af4f5f1838467f1815442b22487426f94e0d66c53", size = 1812863, upload-time = "2024-10-02T10:22:50.548Z" },
]

[[package]]
name = "pycryptodomex"
version = "3.23.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c9/85/e24bf90972a30b0fcd16c73009add1d7d7cd9140c2498a68252028899e41/pycryptodomex-3.23.0.tar.gz", hash = "sha256:71909758f010c82bc99b0abf4ea12012c98962fbf0583c2164f8b84533c2e4da", size = 4922157, upload-time = "2025-05-17T17:23:41.434Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/dd/9c/1a8f35daa39784ed8adf93a694e7e5dc15c23c741bbda06e1d45f8979e9e/pycryptodomex-3.23.0-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:06698f957fe1ab229a99ba2defeeae1c09af185baa909a31a5d1f9d42b1aaed6", size = 2499240, upload-time = "2025-05-17T17:22:46.953Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7a/62/f5221a191a97157d240cf6643747558759126c76ee92f29a3f4aee3197a5/pycryptodomex-3.23.0-cp37-abi3-macosx_10_9_x86_64.whl", hash = "sha256:b2c2537863eccef2d41061e82a881dcabb04944c5c06c5aa7110b577cc487545", size = 1644042, upload-time = "2025-05-17T17:22:49.098Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8c/fd/5a054543c8988d4ed7b612721d7e78a4b9bf36bc3c5ad45ef45c22d0060e/pycryptodomex-3.23.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:43c446e2ba8df8889e0e16f02211c25b4934898384c1ec1ec04d7889c0333587", size = 2186227, upload-time = "2025-05-17T17:22:51.139Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c8/a9/8862616a85cf450d2822dbd4fff1fcaba90877907a6ff5bc2672cafe42f8/pycryptodomex-3.23.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f489c4765093fb60e2edafdf223397bc716491b2b69fe74367b70d6999257a5c", size = 2272578, upload-time = "2025-05-17T17:22:53.676Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/46/9f/bda9c49a7c1842820de674ab36c79f4fbeeee03f8ff0e4f3546c3889076b/pycryptodomex-3.23.0-cp37-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:bdc69d0d3d989a1029df0eed67cc5e8e5d968f3724f4519bd03e0ec68df7543c", size = 2312166, upload-time = "2025-05-17T17:22:56.585Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/03/cc/870b9bf8ca92866ca0186534801cf8d20554ad2a76ca959538041b7a7cf4/pycryptodomex-3.23.0-cp37-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:6bbcb1dd0f646484939e142462d9e532482bc74475cecf9c4903d4e1cd21f003", size = 2185467, upload-time = "2025-05-17T17:22:59.237Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/96/e3/ce9348236d8e669fea5dd82a90e86be48b9c341210f44e25443162aba187/pycryptodomex-3.23.0-cp37-abi3-musllinux_1_2_i686.whl", hash = "sha256:8a4fcd42ccb04c31268d1efeecfccfd1249612b4de6374205376b8f280321744", size = 2346104, upload-time = "2025-05-17T17:23:02.112Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a5/e9/e869bcee87beb89040263c416a8a50204f7f7a83ac11897646c9e71e0daf/pycryptodomex-3.23.0-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:55ccbe27f049743a4caf4f4221b166560d3438d0b1e5ab929e07ae1702a4d6fd", size = 2271038, upload-time = "2025-05-17T17:23:04.872Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8d/67/09ee8500dd22614af5fbaa51a4aee6e342b5fa8aecf0a6cb9cbf52fa6d45/pycryptodomex-3.23.0-cp37-abi3-win32.whl", hash = "sha256:189afbc87f0b9f158386bf051f720e20fa6145975f1e76369303d0f31d1a8d7c", size = 1771969, upload-time = "2025-05-17T17:23:07.115Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/69/96/11f36f71a865dd6df03716d33bd07a67e9d20f6b8d39820470b766af323c/pycryptodomex-3.23.0-cp37-abi3-win_amd64.whl", hash = "sha256:52e5ca58c3a0b0bd5e100a9fbc8015059b05cffc6c66ce9d98b4b45e023443b9", size = 1803124, upload-time = "2025-05-17T17:23:09.267Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f9/93/45c1cdcbeb182ccd2e144c693eaa097763b08b38cded279f0053ed53c553/pycryptodomex-3.23.0-cp37-abi3-win_arm64.whl", hash = "sha256:02d87b80778c171445d67e23d1caef279bf4b25c3597050ccd2e13970b57fd51", size = 1707161, upload-time = "2025-05-17T17:23:11.414Z" },
]

[[package]]
name = "pydantic"
version = "2.5.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "annotated-types" },
    { name = "pydantic-core" },
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/aa/3f/56142232152145ecbee663d70a19a45d078180633321efb3847d2562b490/pydantic-2.5.3.tar.gz", hash = "sha256:b3ef57c62535b0941697cce638c08900d87fcb67e29cfa99e8a68f747f393f7a", size = 651797, upload-time = "2023-12-22T10:39:46.736Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/dd/b7/9aea7ee6c01fe3f3c03b8ca3c7797c866df5fecece9d6cb27caa138db2e2/pydantic-2.5.3-py3-none-any.whl", hash = "sha256:d0caf5954bee831b6bfe7e338c32b9e30c85dfe080c843680783ac2b631673b4", size = 381926, upload-time = "2023-12-22T10:39:43.758Z" },
]

[[package]]
name = "pydantic-core"
version = "2.14.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b2/7d/8304d8471cfe4288f95a3065ebda56f9790d087edc356ad5bd83c89e2d79/pydantic_core-2.14.6.tar.gz", hash = "sha256:1fd0c1d395372843fba13a51c28e3bb9d59bd7aebfeb17358ffaaa1e4dbbe948", size = 360305, upload-time = "2023-12-21T19:56:33.986Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b1/26/4bd7ac215215322a693c178a022993450ebf7b1e91b26941f72407e1e9a1/pydantic_core-2.14.6-cp312-cp312-macosx_10_7_x86_64.whl", hash = "sha256:667aa2eac9cd0700af1ddb38b7b1ef246d8cf94c85637cbb03d7757ca4c3fdec", size = 1855038, upload-time = "2023-12-21T19:53:14.05Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a5/f8/07a2563f40b863ba97f3db648697f3f1d7b7edf1bd679f210064cb556e74/pydantic_core-2.14.6-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:cdee837710ef6b56ebd20245b83799fce40b265b3b406e51e8ccc5b85b9099b7", size = 1718829, upload-time = "2023-12-21T19:53:16.61Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ba/98/fb42628ed811643c364e05353d3a015c74859402994420aeba8e3e34a54c/pydantic_core-2.14.6-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2c5bcf3414367e29f83fd66f7de64509a8fd2368b1edf4351e862910727d3e51", size = 1824883, upload-time = "2023-12-21T19:53:18.652Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/69/ed/6a318d3846ac45e4e8d7c81a4c4f9cad341f4715521cc2cc7baecd6be9c0/pydantic_core-2.14.6-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:26a92ae76f75d1915806b77cf459811e772d8f71fd1e4339c99750f0e7f6324f", size = 1834955, upload-time = "2023-12-21T19:53:20.879Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/d0/adf341fb8ed080bf5abb91c42752ffa099d8439e45d3fa40a21f259f724c/pydantic_core-2.14.6-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:a983cca5ed1dd9a35e9e42ebf9f278d344603bfcb174ff99a5815f953925140a", size = 1994990, upload-time = "2023-12-21T19:53:23.309Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5e/58/7cac843607f3b2d0af1768fae90ef219413db163a7cfb7557344edfeed2f/pydantic_core-2.14.6-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:cb92f9061657287eded380d7dc455bbf115430b3aa4741bdc662d02977e7d0af", size = 3001843, upload-time = "2023-12-21T19:53:25.501Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f3/62/076e6c43735950e911d80c6edf215314a8cf9b8adefe9613b72b09ccb1ee/pydantic_core-2.14.6-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e4ace1e220b078c8e48e82c081e35002038657e4b37d403ce940fa679e57113b", size = 2067417, upload-time = "2023-12-21T19:53:27.652Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d0/21/7ca5edf46bc6706152d459b560d669cfd72afe0dda24292408f1be8008d6/pydantic_core-2.14.6-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:ef633add81832f4b56d3b4c9408b43d530dfca29e68fb1b797dcb861a2c734cd", size = 1922514, upload-time = "2023-12-21T19:53:30.443Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b7/53/101aac1d63a743284cdae804ceb6f561879c385f355caf20d2d87da6d36d/pydantic_core-2.14.6-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:7e90d6cc4aad2cc1f5e16ed56e46cebf4877c62403a311af20459c15da76fd91", size = 2004496, upload-time = "2023-12-21T19:53:32.411Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9f/7a/2e906fc1a5e4ca45e730118f0afb4878a39a1d505d895835d8cc5452446c/pydantic_core-2.14.6-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:e8a5ac97ea521d7bde7621d86c30e86b798cdecd985723c4ed737a2aa9e77d0c", size = 2129106, upload-time = "2023-12-21T19:53:34.501Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d9/3e/28bd56c6aeadaae2eca12e39274b880505dd7fc9fa7b732d11167275c084/pydantic_core-2.14.6-cp312-none-win32.whl", hash = "sha256:f27207e8ca3e5e021e2402ba942e5b4c629718e665c81b8b306f3c8b1ddbb786", size = 1743090, upload-time = "2023-12-21T19:53:36.711Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/84/13/afa2b5c336d30a43592f9bc8d5769ccd15b32b4ef243bd792496fe336925/pydantic_core-2.14.6-cp312-none-win_amd64.whl", hash = "sha256:b3e5fe4538001bb82e2295b8d2a39356a84694c97cb73a566dc36328b9f83b40", size = 1866497, upload-time = "2023-12-21T19:53:39.401Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/04/33/68e91365ac5ef23fc70fbc4e24ab2f212a6ca39cd23b81589af9807946df/pydantic_core-2.14.6-cp312-none-win_arm64.whl", hash = "sha256:64634ccf9d671c6be242a664a33c4acf12882670b09b3f163cd00a24cffbd74e", size = 1844384, upload-time = "2023-12-21T19:53:41.316Z" },
]

[[package]]
name = "pydyf"
version = "0.11.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2e/c2/97fc6ce4ce0045080dc99446def812081b57750ed8aa67bfdfafa4561fe5/pydyf-0.11.0.tar.gz", hash = "sha256:394dddf619cca9d0c55715e3c55ea121a9bf9cbc780cdc1201a2427917b86b64", size = 17769, upload-time = "2024-07-12T12:26:51.95Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c9/ac/d5db977deaf28c6ecbc61bbca269eb3e8f0b3a1f55c8549e5333e606e005/pydyf-0.11.0-py3-none-any.whl", hash = "sha256:0aaf9e2ebbe786ec7a78ec3fbffa4cdcecde53fd6f563221d53c6bc1328848a3", size = 8104, upload-time = "2024-07-12T12:26:49.896Z" },
]

[[package]]
name = "pyee"
version = "11.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f7/22/b4c7f3d9579204a014c4eda0e019e6bfe56af52a96cacc82004b60eec079/pyee-11.1.0.tar.gz", hash = "sha256:b53af98f6990c810edd9b56b87791021a8f54fd13db4edd1142438d44ba2263f", size = 29806, upload-time = "2023-11-23T17:13:25.913Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/cc/5cea8a0a0d3deb90b5a0d39ad1a6a1ccaa40a9ea86d793eb8a49d32a6ed0/pyee-11.1.0-py3-none-any.whl", hash = "sha256:5d346a7d0f861a4b2e6c47960295bd895f816725b27d656181947346be98d7c1", size = 15263, upload-time = "2023-11-23T17:13:24.486Z" },
]

[[package]]
name = "pyjwt"
version = "2.8.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/30/72/8259b2bccfe4673330cea843ab23f86858a419d8f1493f66d413a76c7e3b/PyJWT-2.8.0.tar.gz", hash = "sha256:57e28d156e3d5c10088e0c68abb90bfac3df82b40a71bd0daa20c65ccd5c23de", size = 78313, upload-time = "2023-07-18T20:02:22.594Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2b/4f/e04a8067c7c96c364cef7ef73906504e2f40d690811c021e1a1901473a19/PyJWT-2.8.0-py3-none-any.whl", hash = "sha256:59127c392cc44c2da5bb3192169a91f429924e17aff6534d70fdc02ab3e04320", size = 22591, upload-time = "2023-07-18T20:02:21.561Z" },
]

[[package]]
name = "pylint"
version = "3.3.7"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "astroid" },
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "dill" },
    { name = "isort" },
    { name = "mccabe" },
    { name = "platformdirs" },
    { name = "tomlkit" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1c/e4/83e487d3ddd64ab27749b66137b26dc0c5b5c161be680e6beffdc99070b3/pylint-3.3.7.tar.gz", hash = "sha256:2b11de8bde49f9c5059452e0c310c079c746a0a8eeaa789e5aa966ecc23e4559", size = 1520709, upload-time = "2025-05-04T17:07:51.089Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e8/83/bff755d09e31b5d25cc7fdc4bf3915d1a404e181f1abf0359af376845c24/pylint-3.3.7-py3-none-any.whl", hash = "sha256:43860aafefce92fca4cf6b61fe199cdc5ae54ea28f9bf4cd49de267b5195803d", size = 522565, upload-time = "2025-05-04T17:07:48.714Z" },
]

[[package]]
name = "pymupdf"
version = "1.24.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pymupdfb", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7c/0c/ac4d70ca95f46e3abec2ce561b5d75e79dfd4b0f930a39b3f98229cb8c5a/PyMuPDF-1.24.5.tar.gz", hash = "sha256:968d30ab20be828aab56bb04621bbb9f962c59ab5f1788236473c372fe797093", size = 22456143, upload-time = "2024-05-30T19:45:54.17Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fa/03/42e9daefb45fe100b5d9532c3f0fe954dfba1d15391582b02e6424cdda60/PyMuPDF-1.24.5-cp312-none-macosx_10_9_x86_64.whl", hash = "sha256:8d6e5e3a81979ec2cc1a1647395fbc224a57dbe91a6e65d3ab3e4644e1137b90", size = 3218789, upload-time = "2024-05-30T19:45:20.515Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/16/ba/4bc3a7c23e1092b04d3102fed26b8401afecc6c14eba127ba9c3708dbca8/PyMuPDF-1.24.5-cp312-none-macosx_11_0_arm64.whl", hash = "sha256:65f999519b87747f2220194eb9251c3d18dc7191da56acd9b38641c8f093caaf", size = 2967881, upload-time = "2024-05-30T19:45:27.025Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/3d/4a9d68e73a9cb6ac6fec9be2bb6b9e6e364f3ad2842d6720ffbc6b281c81/PyMuPDF-1.24.5-cp312-none-manylinux2014_aarch64.whl", hash = "sha256:2be7be2e8e3db25f8590a2cbf9790cf316ae8719beb5a29c37f6ebcf1f1cd781", size = 3208641, upload-time = "2024-05-31T10:21:52.754Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fc/e4/eff5e9e987fe9c242f89dc89c10f64aa82e5b55ed132a0ab77cd13565bfe/PyMuPDF-1.24.5-cp312-none-manylinux2014_x86_64.whl", hash = "sha256:9f75be74de4852e7addd8b8a1baf49eb2fc9698fc522ce5a98fc046f2a1ae19c", size = 3479350, upload-time = "2024-05-30T19:45:31.464Z" },
]

[[package]]
name = "pymupdfb"
version = "1.24.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9e/0e/ece14146cd8d0397149235ded80a671a6a205b9e39fe80cc645723fe2832/PyMuPDFb-1.24.3.tar.gz", hash = "sha256:7cc5da3031d160e0f01dbb88567ddca70adc82f062a3a5b4e2dd2a57646f442c", size = 37496, upload-time = "2024-05-09T17:21:15.742Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/47/52/d7a3351eebb23b94f3caac648290c69363d5d5e28e5c49e0b7997925d8b6/PyMuPDFb-1.24.3-py3-none-macosx_10_9_x86_64.whl", hash = "sha256:d2ccca660042896d4af479f979ec10674c5a0b3cd2d9ecb0011f08dc82380cce", size = 15296057, upload-time = "2024-05-09T17:18:25.598Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7e/4a/27e4e2ce8f5d0ed1d1b2a1f7807f6158db1e8e547a7bf76ac462a800a4b4/PyMuPDFb-1.24.3-py3-none-macosx_11_0_arm64.whl", hash = "sha256:ad51d21086a16199684a3eebcb47d9c8460fc27e7bebae77f5fe64e8c34ebf34", size = 14911671, upload-time = "2024-05-09T17:19:02.714Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6c/7a/3b8347945a75e0b12817484080dd60913bc006d0cba3f11b7a3578007bda/PyMuPDFb-1.24.3-py3-none-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:3e7aab000d707c40e3254cd60152897b90952ed9a3567584d70974292f4912ce", size = 15514309, upload-time = "2024-05-09T21:04:52.903Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ed/77/8a25d8e9189f2c7f9f1c20c77dc74927630cb46e59d999056039142cce50/PyMuPDFb-1.24.3-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f39588fd2b7a63e2456df42cd8925c316202e0eb77d115d9c01ba032b2c9086f", size = 15750766, upload-time = "2024-05-09T17:19:22.641Z" },
]

[[package]]
name = "pymysql"
version = "1.1.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b3/8f/ce59b5e5ed4ce8512f879ff1fa5ab699d211ae2495f1adaa5fbba2a1eada/pymysql-1.1.1.tar.gz", hash = "sha256:e127611aaf2b417403c60bf4dc570124aeb4a57f5f37b8e95ae399a42f904cd0", size = 47678, upload-time = "2024-05-21T11:03:43.722Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0c/94/e4181a1f6286f545507528c78016e00065ea913276888db2262507693ce5/PyMySQL-1.1.1-py3-none-any.whl", hash = "sha256:4de15da4c61dc132f4fb9ab763063e693d521a80fd0e87943b9a453dd4c19d6c", size = 44972, upload-time = "2024-05-21T11:03:41.216Z" },
]

[[package]]
name = "pyparsing"
version = "3.1.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/83/08/13f3bce01b2061f2bbd582c9df82723de943784cf719a35ac886c652043a/pyparsing-3.1.4.tar.gz", hash = "sha256:f86ec8d1a83f11977c9a6ea7598e8c27fc5cddfa5b07ea2241edbbde1d7bc032", size = 900231, upload-time = "2024-08-25T15:00:47.416Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e5/0c/0e3c05b1c87bb6a1c76d281b0f35e78d2d80ac91b5f8f524cebf77f51049/pyparsing-3.1.4-py3-none-any.whl", hash = "sha256:a6a7ee4235a3f944aa1fa2249307708f893fe5717dc603503c6c7969c070fb7c", size = 104100, upload-time = "2024-08-25T15:00:45.361Z" },
]

[[package]]
name = "pyphen"
version = "0.17.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/69/56/e4d7e1bd70d997713649c5ce530b2d15a5fc2245a74ca820fc2d51d89d4d/pyphen-0.17.2.tar.gz", hash = "sha256:f60647a9c9b30ec6c59910097af82bc5dd2d36576b918e44148d8b07ef3b4aa3", size = 2079470, upload-time = "2025-01-20T13:18:36.296Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7b/1f/c2142d2edf833a90728e5cdeb10bdbdc094dde8dbac078cee0cf33f5e11b/pyphen-0.17.2-py3-none-any.whl", hash = "sha256:3a07fb017cb2341e1d9ff31b8634efb1ae4dc4b130468c7c39dd3d32e7c3affd", size = 2079358, upload-time = "2025-01-20T13:18:29.629Z" },
]

[[package]]
name = "pypinyin"
version = "0.42.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/19/49/a1b198a19d12f9ba79fd1b8bd33810f0dabd90af9c76e6efc75f8b491396/pypinyin-0.42.0.tar.gz", hash = "sha256:c2f003b303bd2338d440c7e57f7073e6db8d6f72ca6444fe45dcbca7a0a1d3a1", size = 1263534, upload-time = "2021-06-14T07:17:16.974Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/86/9c/77913020ee8cf263a519d9ac5da344af6a338bc49ed3fd1e302d592cc457/pypinyin-0.42.0-py2.py3-none-any.whl", hash = "sha256:ddc03f5d318d6276bb9fd069555ee934b6fd43548f9606f63d2be1a998b19e0b", size = 1284806, upload-time = "2021-06-14T07:17:13.435Z" },
]

[[package]]
name = "pyppmd"
version = "1.1.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/42/8e/06581a619ad31cd28fd897bd55aff2ea945d3d566969b8b3f682599e6dee/pyppmd-1.1.1.tar.gz", hash = "sha256:f1a812f1e7628f4c26d05de340b91b72165d7b62778c27d322b82ce2e8ff00cb", size = 1349281, upload-time = "2024-12-23T04:12:09.391Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/46/a8/a1fdc1b53466e9a01b0d8a8689ae4024fffa6de20330fb0d6025d745cf0f/pyppmd-1.1.1-cp312-cp312-macosx_10_9_universal2.whl", hash = "sha256:9877ef273e2c0efdec740855e28004a708ada9012e0db6673df4bb6eba3b05e0", size = 76238, upload-time = "2024-12-23T04:11:03.362Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/63/54/15a7feae1a258bc67fc2413f82a7f296da7efb076d922d939fe7ef87b537/pyppmd-1.1.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:f816a5cbccceced80e15335389eeeaf1b56a605fb7eebe135b1c85bd161e288c", size = 47192, upload-time = "2024-12-23T04:11:04.649Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/55/4f/b5d9086c3479639f1347bc04d6ed1b543a7f87212f68c38f30180cb28e35/pyppmd-1.1.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:6bddabf8f2c6b991d15d6785e603d9d414ae4a791f131b1a729bb8a5d31133d1", size = 47309, upload-time = "2024-12-23T04:11:07.426Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/df/2dc8d61008ed5da459fbef13917582b78902867451cb3467d59ca86c945f/pyppmd-1.1.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:855bc2b0d19c3fead5815d72dbe350b4f765334336cbf8bcb504d46edc9e9dd2", size = 139378, upload-time = "2024-12-23T04:11:10.525Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/85/cf/9899ec2a5a9b10f42afecdd0635186f38731c100643d9b092f5b6d7f137c/pyppmd-1.1.1-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a95b11b3717c083b912f0879678ba72f301bbdb9b69efed46dbc5df682aa3ce7", size = 137565, upload-time = "2024-12-23T04:11:13.767Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/33/33/c67c41e681e171f9bbbf184eae8899e913845e8e6186cbdad62a3ddf231d/pyppmd-1.1.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:38b645347b6ea217b0c58e8edac27473802868f152db520344ac8c7490981849", size = 142829, upload-time = "2024-12-23T04:11:15.378Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8e/c3/18e6f565cb1a3941640fd522a3f02f48e546c2996c7f0048608f35994a51/pyppmd-1.1.1-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:f8f94b6222262def5b532f2b9716554ef249ad8411fd4da303596cc8c2e8eda1", size = 143080, upload-time = "2024-12-23T04:11:17.066Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/ec/40625be3275bae8470136c98d2e63556537ba5cedb6c0a9fa8f90a541a46/pyppmd-1.1.1-cp312-cp312-musllinux_1_1_i686.whl", hash = "sha256:1c0306f69ceddf385ef689ebd0218325b7e523c48333d87157b37393466cfa1e", size = 137254, upload-time = "2024-12-23T04:11:20.568Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/04/f4/92366a1e893f0b04a4bbf1cbc8263d4b58f2ac2f53cee65b11d92d0253bf/pyppmd-1.1.1-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:a4ba510457a56535522a660098399e3fa8722e4de55808d089c9d13435d87069", size = 146613, upload-time = "2024-12-23T04:11:22.278Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6f/c2/50ffb30b7787aec4c0cb5a99eaab78d90b0c98e0006d34a22198f954084e/pyppmd-1.1.1-cp312-cp312-win32.whl", hash = "sha256:032f040a89fd8348109e8638f94311bd4c3c693fb4cad213ad06a37c203690b1", size = 41857, upload-time = "2024-12-23T04:11:23.671Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/77/94/03ff304086c5b0823bee966a5ae4915b5341dba8ed5e4c9f1b9532d9a9ff/pyppmd-1.1.1-cp312-cp312-win_amd64.whl", hash = "sha256:2be8cbd13dd59fad1a0ad38062809e28596f3673b77a799dfe82b287986265ed", size = 46564, upload-time = "2024-12-23T04:11:26.379Z" },
]

[[package]]
name = "pyreadline3"
version = "3.5.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0f/49/4cea918a08f02817aabae639e3d0ac046fef9f9180518a3ad394e22da148/pyreadline3-3.5.4.tar.gz", hash = "sha256:8d57d53039a1c75adba8e50dd3d992b28143480816187ea5efbd5c78e6c885b7", size = 99839, upload-time = "2024-09-19T02:40:10.062Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5a/dc/491b7661614ab97483abf2056be1deee4dc2490ecbf7bff9ab5cdbac86e1/pyreadline3-3.5.4-py3-none-any.whl", hash = "sha256:eaf8e6cc3c49bcccf145fc6067ba8643d1df34d604a1ec0eccbf7a18e6d3fae6", size = 83178, upload-time = "2024-09-19T02:40:08.598Z" },
]

[[package]]
name = "pytest"
version = "8.3.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ae/3c/c9d525a414d506893f0cd8a8d0de7706446213181570cdbd766691164e40/pytest-8.3.5.tar.gz", hash = "sha256:f4efe70cc14e511565ac476b57c279e12a855b11f48f212af1080ef2263d3845", size = 1450891, upload-time = "2025-03-02T12:54:54.503Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/30/3d/64ad57c803f1fa1e963a7946b6e0fea4a70df53c1a7fed304586539c2bac/pytest-8.3.5-py3-none-any.whl", hash = "sha256:c69214aa47deac29fad6c2a4f590b9c4a9fdb16a403176fe154b79c0b4d4d820", size = 343634, upload-time = "2025-03-02T12:54:52.069Z" },
]

[[package]]
name = "pytest-asyncio"
version = "0.24.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/52/6d/c6cf50ce320cf8611df7a1254d86233b3df7cc07f9b5f5cbcb82e08aa534/pytest_asyncio-0.24.0.tar.gz", hash = "sha256:d081d828e576d85f875399194281e92bf8a68d60d72d1a2faf2feddb6c46b276", size = 49855, upload-time = "2024-08-22T08:03:18.145Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/96/31/6607dab48616902f76885dfcf62c08d929796fc3b2d2318faf9fd54dbed9/pytest_asyncio-0.24.0-py3-none-any.whl", hash = "sha256:a811296ed596b69bf0b6f3dc40f83bcaf341b155a269052d82efa2b25ac7037b", size = 18024, upload-time = "2024-08-22T08:03:15.536Z" },
]

[[package]]
name = "pytest-env"
version = "1.1.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1f/31/27f28431a16b83cab7a636dce59cf397517807d247caa38ee67d65e71ef8/pytest_env-1.1.5.tar.gz", hash = "sha256:91209840aa0e43385073ac464a554ad2947cc2fd663a9debf88d03b01e0cc1cf", size = 8911, upload-time = "2024-09-17T22:39:18.566Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/de/b8/87cfb16045c9d4092cfcf526135d73b88101aac83bc1adcf82dfb5fd3833/pytest_env-1.1.5-py3-none-any.whl", hash = "sha256:ce90cf8772878515c24b31cd97c7fa1f4481cd68d588419fd45f10ecaee6bc30", size = 6141, upload-time = "2024-09-17T22:39:16.942Z" },
]

[[package]]
name = "pytest-xdist"
version = "3.6.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "execnet" },
    { name = "pytest" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/41/c4/3c310a19bc1f1e9ef50075582652673ef2bfc8cd62afef9585683821902f/pytest_xdist-3.6.1.tar.gz", hash = "sha256:ead156a4db231eec769737f57668ef58a2084a34b2e55c4a8fa20d861107300d", size = 84060, upload-time = "2024-04-28T19:29:54.414Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/82/1d96bf03ee4c0fdc3c0cbe61470070e659ca78dc0086fb88b66c185e2449/pytest_xdist-3.6.1-py3-none-any.whl", hash = "sha256:9ed4adfb68a016610848639bb7e02c9352d5d9f03d04809919e2dafc3be4cca7", size = 46108, upload-time = "2024-04-28T19:29:52.813Z" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "six" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/66/c0/0c8b6ad9f17a802ee498c46e004a0eb49bc148f2fd230864601a86dcf6db/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3", size = 342432, upload-time = "2024-03-01T18:36:20.211Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ec/57/56b9bcc3c9c6a792fcbaf139543cee77261f3651ca9da0c93f5c1221264b/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427", size = 229892, upload-time = "2024-03-01T18:36:18.57Z" },
]

[[package]]
name = "python-docx"
version = "0.8.11"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "lxml" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/a0/52729ce4aa026f31b74cc877be1d11e4ddeaa361dc7aebec148171644b33/python-docx-0.8.11.tar.gz", hash = "sha256:1105d233a0956dd8dd1e710d20b159e2d72ac3c301041b95f4d4ceb3e0ebebc4", size = 5561613, upload-time = "2021-05-15T22:18:24.824Z" }

[[package]]
name = "python-dotenv"
version = "1.1.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f6/b0/4bc07ccd3572a2f9df7e6782f52b0c6c90dcbb803ac4a167702d7d0dfe1e/python_dotenv-1.1.1.tar.gz", hash = "sha256:a8a6399716257f45be6a007360200409fce5cda2661e3dec71d23dc15f6189ab", size = 41978, upload-time = "2025-06-24T04:21:07.341Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl", hash = "sha256:31f23644fe2602f88ff55e1f5c79ba497e01224ee7737937930c448e4d0e24dc", size = 20556, upload-time = "2025-06-24T04:21:06.073Z" },
]

[[package]]
name = "python-levenshtein"
version = "0.27.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "levenshtein" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/13/f6/d865a565b7eeef4b5f9a18accafb03d5730c712420fc84a3a40555f7ea6b/python_levenshtein-0.27.1.tar.gz", hash = "sha256:3a5314a011016d373d309a68e875fd029caaa692ad3f32e78319299648045f11", size = 12326, upload-time = "2025-03-02T19:47:25.641Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/95/8c8fd923b0a702388da4f9e0368f490d123cc5224279e6a083984304a15e/python_levenshtein-0.27.1-py3-none-any.whl", hash = "sha256:e1a4bc2a70284b2ebc4c505646142fecd0f831e49aa04ed972995895aec57396", size = 9426, upload-time = "2025-03-02T19:47:24.801Z" },
]

[[package]]
name = "python-multipart"
version = "0.0.20"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f3/87/f44d7c9f274c7ee665a29b885ec97089ec5dc034c7f3fafa03da9e39a09e/python_multipart-0.0.20.tar.gz", hash = "sha256:8dd0cab45b8e23064ae09147625994d090fa46f5b0d1e13af944c331a7fa9d13", size = 37158, upload-time = "2024-12-16T19:45:46.972Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/45/58/38b5afbc1a800eeea951b9285d3912613f2603bdf897a4ab0f4bd7f405fc/python_multipart-0.0.20-py3-none-any.whl", hash = "sha256:8a62d3a8335e06589fe01f2a3e178cdcc632f3fbe0d492ad9ee0ec35aab1f104", size = 24546, upload-time = "2024-12-16T19:45:44.423Z" },
]

[[package]]
name = "python-pptx"
version = "1.0.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "lxml" },
    { name = "pillow" },
    { name = "typing-extensions" },
    { name = "xlsxwriter" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/52/a9/0c0db8d37b2b8a645666f7fd8accea4c6224e013c42b1d5c17c93590cd06/python_pptx-1.0.2.tar.gz", hash = "sha256:479a8af0eaf0f0d76b6f00b0887732874ad2e3188230315290cd1f9dd9cc7095", size = 10109297, upload-time = "2024-08-07T17:33:37.772Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d9/4f/00be2196329ebbff56ce564aa94efb0fbc828d00de250b1980de1a34ab49/python_pptx-1.0.2-py3-none-any.whl", hash = "sha256:160838e0b8565a8b1f67947675886e9fea18aa5e795db7ae531606d68e785cba", size = 472788, upload-time = "2024-08-07T17:33:28.192Z" },
]

[[package]]
name = "python-redis-lock"
version = "3.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "redis" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e3/81/25ed35e309aedba8862da9d9d3f841ba77c24304e26e092644e36898ee32/python-redis-lock-3.7.0.tar.gz", hash = "sha256:4265a476e39d476a8acf5c2766485c44c75f3a1bd6cf73bb195f3079153b8374", size = 162008, upload-time = "2020-11-20T09:10:28.11Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/41/c2/1e77e23a403b0006fbe6385e7ae53a1e1fbdb692418beb5c5ed670511917/python_redis_lock-3.7.0-py2.py3-none-any.whl", hash = "sha256:0101c9033bd38e2a7b99989e0c6111451c40551b08d19d281047bf6630337574", size = 12165, upload-time = "2020-11-20T09:10:26.474Z" },
]

[[package]]
name = "python-statemachine"
version = "2.5.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/45/91/4f05f3931d1e9b1df71b17dc08c43feddf2bed7dbf13f95323df2cc8e340/python_statemachine-2.5.0.tar.gz", hash = "sha256:ae88cd22e47930b92b983a2176e61d811e571b69897be2568ec812c2885fb93a", size = 403718, upload-time = "2024-12-03T17:58:49.833Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bf/2d/1c95ebe84df60d630f8e855d1df2c66368805444ac167e9b50f29eabe917/python_statemachine-2.5.0-py3-none-any.whl", hash = "sha256:0ed53846802c17037fcb2a92323f4bc0c833290fa9d17a3587c50886c1541e62", size = 50415, upload-time = "2024-12-03T17:58:47.375Z" },
]

[[package]]
name = "pytz"
version = "2025.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f8/bf/abbd3cdfb8fbc7fb3d4d38d320f2441b1e7cbe29be4f23797b4a2b5d8aac/pytz-2025.2.tar.gz", hash = "sha256:360b9e3dbb49a209c21ad61809c7fb453643e048b38924c765813546746e81c3", size = 320884, upload-time = "2025-03-25T02:25:00.538Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/81/c4/34e93fe5f5429d7570ec1fa436f1986fb1f00c3e0f43a589fe2bbcd22c3f/pytz-2025.2-py2.py3-none-any.whl", hash = "sha256:5ddf76296dd8c44c26eb8f4b6f35488f3ccbf6fbbd7adee0b7262d43f0ec2f00", size = 509225, upload-time = "2025-03-25T02:24:58.468Z" },
]

[[package]]
name = "pyyaml"
version = "6.0.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/54/ed/79a089b6be93607fa5cdaedf301d7dfb23af5f25c398d5ead2525b063e17/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e", size = 130631, upload-time = "2024-08-06T20:33:50.674Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/86/0c/c581167fc46d6d6d7ddcfb8c843a4de25bdd27e4466938109ca68492292c/PyYAML-6.0.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:c70c95198c015b85feafc136515252a261a84561b7b1d51e3384e0655ddf25ab", size = 183873, upload-time = "2024-08-06T20:32:25.131Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/0c/38374f5bb272c051e2a69281d71cba6fdb983413e6758b84482905e29a5d/PyYAML-6.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ce826d6ef20b1bc864f0a68340c8b3287705cae2f8b4b1d932177dcc76721725", size = 173302, upload-time = "2024-08-06T20:32:26.511Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c3/93/9916574aa8c00aa06bbac729972eb1071d002b8e158bd0e83a3b9a20a1f7/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1f71ea527786de97d1a0cc0eacd1defc0985dcf6b3f17bb77dcfc8c34bec4dc5", size = 739154, upload-time = "2024-08-06T20:32:28.363Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/95/0f/b8938f1cbd09739c6da569d172531567dbcc9789e0029aa070856f123984/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9b22676e8097e9e22e36d6b7bda33190d0d400f345f23d4065d48f4ca7ae0425", size = 766223, upload-time = "2024-08-06T20:32:30.058Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b9/2b/614b4752f2e127db5cc206abc23a8c19678e92b23c3db30fc86ab731d3bd/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476", size = 767542, upload-time = "2024-08-06T20:32:31.881Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d4/00/dd137d5bcc7efea1836d6264f049359861cf548469d18da90cd8216cf05f/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:0833f8694549e586547b576dcfaba4a6b55b9e96098b36cdc7ebefe667dfed48", size = 731164, upload-time = "2024-08-06T20:32:37.083Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c9/1f/4f998c900485e5c0ef43838363ba4a9723ac0ad73a9dc42068b12aaba4e4/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:8b9c7197f7cb2738065c481a0461e50ad02f18c78cd75775628afb4d7137fb3b", size = 756611, upload-time = "2024-08-06T20:32:38.898Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/df/d1/f5a275fdb252768b7a11ec63585bc38d0e87c9e05668a139fea92b80634c/PyYAML-6.0.2-cp312-cp312-win32.whl", hash = "sha256:ef6107725bd54b262d6dedcc2af448a266975032bc85ef0172c5f059da6325b4", size = 140591, upload-time = "2024-08-06T20:32:40.241Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0c/e8/4f648c598b17c3d06e8753d7d13d57542b30d56e6c2dedf9c331ae56312e/PyYAML-6.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:7e7401d0de89a9a855c839bc697c079a4af81cf878373abd7dc625847d25cbd8", size = 156338, upload-time = "2024-08-06T20:32:41.93Z" },
]

[[package]]
name = "pyzstd"
version = "0.17.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8f/a2/54d860ccbd07e3c67e4d0321d1c29fc7963ac82cf801a078debfc4ef7c15/pyzstd-0.17.0.tar.gz", hash = "sha256:d84271f8baa66c419204c1dd115a4dec8b266f8a2921da21b81764fa208c1db6", size = 1212160, upload-time = "2025-05-10T14:14:49.764Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/76/50/7fa47d0a13301b1ce20972aa0beb019c97f7ee8b0658d7ec66727b5967f9/pyzstd-0.17.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:2ac330fc4f64f97a411b6f3fc179d2fe3050b86b79140e75a9a6dd9d6d82087f", size = 379056, upload-time = "2025-05-10T14:13:17.091Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9d/f2/67b03b1fa4e2a0b05e147cc30ac6d271d3d11017b47b30084cb4699451f4/pyzstd-0.17.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:725180c0c4eb2e643b7048ebfb45ddf43585b740535907f70ff6088f5eda5096", size = 298381, upload-time = "2025-05-10T14:13:18.812Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/01/8b/807ff0a13cf3790fe5de85e18e10c22b96d92107d2ce88699cefd3f890cb/pyzstd-0.17.0-cp312-cp312-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:9c20fe0a60019685fa1f7137cb284f09e3f64680a503d9c0d50be4dd0a3dc5ec", size = 443770, upload-time = "2025-05-10T14:13:20.495Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f0/88/832d8d8147691ee37736a89ea39eaf94ceac5f24a6ce2be316ff5276a1f8/pyzstd-0.17.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d97f7aaadc3b6e2f8e51bfa6aa203ead9c579db36d66602382534afaf296d0db", size = 391167, upload-time = "2025-05-10T14:13:22.236Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/a5/2e09bee398dfb0d94ca43f3655552a8770a6269881dc4710b8f29c7f71aa/pyzstd-0.17.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:42dcb34c5759b59721997036ff2d94210515d3ef47a9de84814f1c51a1e07e8a", size = 478960, upload-time = "2025-05-10T14:13:23.584Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/da/b5/1f3b778ad1ccc395161fab7a3bf0dfbd85232234b6657c93213ed1ceda7e/pyzstd-0.17.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:6bf05e18be6f6c003c7129e2878cffd76fcbebda4e7ebd7774e34ae140426cbf", size = 421891, upload-time = "2025-05-10T14:13:25.417Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/83/c4/6bfb4725f4f38e9fe9735697060364fb36ee67546e7e8d78135044889619/pyzstd-0.17.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c40f7c3a5144aa4fbccf37c30411f6b1db4c0f2cb6ad4df470b37929bffe6ca0", size = 413608, upload-time = "2025-05-10T14:13:26.75Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/95/a2/c48b543e3a482e758b648ea025b94efb1abe1f4859c5185ff02c29596035/pyzstd-0.17.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:9efd4007f8369fd0890701a4fc77952a0a8c4cb3bd30f362a78a1adfb3c53c12", size = 416429, upload-time = "2025-05-10T14:13:28.096Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5c/62/2d039ee4dbc8116ca1f2a2729b88a1368f076f5dadad463f165993f7afa8/pyzstd-0.17.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:5f8add139b5fd23b95daa844ca13118197f85bd35ce7507e92fcdce66286cc34", size = 446671, upload-time = "2025-05-10T14:13:29.772Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/be/ec/9ec9f0957cf5b842c751103a2b75ecb0a73cf3d99fac57e0436aab6748e0/pyzstd-0.17.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:259a60e8ce9460367dcb4b34d8b66e44ca3d8c9c30d53ed59ae7037622b3bfc7", size = 520290, upload-time = "2025-05-10T14:13:31.585Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cc/42/2e2f4bb641c2a9ab693c31feebcffa1d7c24e946d8dde424bba371e4fcce/pyzstd-0.17.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:86011a93cc3455c5d2e35988feacffbf2fa106812a48e17eb32c2a52d25a95b3", size = 563785, upload-time = "2025-05-10T14:13:32.971Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4d/e4/25e198d382faa4d322f617d7a5ff82af4dc65749a10d90f1423af2d194f6/pyzstd-0.17.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:425c31bc3de80313054e600398e4f1bd229ee61327896d5d015e2cd0283c9012", size = 433390, upload-time = "2025-05-10T14:13:34.668Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ad/7c/1ab970f5404ace9d343a36a86f1bd0fcf2dc1adf1ef8886394cf0a58bd9e/pyzstd-0.17.0-cp312-cp312-win32.whl", hash = "sha256:7c4b88183bb36eb2cebbc0352e6e9fe8e2d594f15859ae1ef13b63ebc58be158", size = 220291, upload-time = "2025-05-10T14:13:36.005Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b2/52/d35bf3e4f0676a74359fccef015eabe3ceaba95da4ac2212f8be4dde16de/pyzstd-0.17.0-cp312-cp312-win_amd64.whl", hash = "sha256:3c31947e0120468342d74e0fa936d43f7e1dad66a2262f939735715aa6c730e8", size = 246451, upload-time = "2025-05-10T14:13:37.712Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/34/da/a44705fe44dd87e0f09861b062f93ebb114365640dbdd62cbe80da9b8306/pyzstd-0.17.0-cp312-cp312-win_arm64.whl", hash = "sha256:1d0346418abcef11507356a31bef5470520f6a5a786d4e2c69109408361b1020", size = 222967, upload-time = "2025-05-10T14:13:38.94Z" },
]

[[package]]
name = "qrcode"
version = "8.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8f/b2/7fc2931bfae0af02d5f53b174e9cf701adbb35f39d69c2af63d4a39f81a9/qrcode-8.2.tar.gz", hash = "sha256:35c3f2a4172b33136ab9f6b3ef1c00260dd2f66f858f24d88418a015f446506c", size = 43317, upload-time = "2025-05-01T15:44:24.726Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/dd/b8/d2d6d731733f51684bbf76bf34dab3b70a9148e8f2cef2bb544fccec681a/qrcode-8.2-py3-none-any.whl", hash = "sha256:16e64e0716c14960108e85d853062c9e8bba5ca8252c0b4d0231b9df4060ff4f", size = 45986, upload-time = "2025-05-01T15:44:22.781Z" },
]

[[package]]
name = "rapidfuzz"
version = "3.13.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ed/f6/6895abc3a3d056b9698da3199b04c0e56226d530ae44a470edabf8b664f0/rapidfuzz-3.13.0.tar.gz", hash = "sha256:d2eaf3839e52cbcc0accbe9817a67b4b0fcf70aaeb229cfddc1c28061f9ce5d8", size = 57904226, upload-time = "2025-04-03T20:38:51.226Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/13/4b/a326f57a4efed8f5505b25102797a58e37ee11d94afd9d9422cb7c76117e/rapidfuzz-3.13.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4a1a6a906ba62f2556372282b1ef37b26bca67e3d2ea957277cfcefc6275cca7", size = 1989501, upload-time = "2025-04-03T20:36:13.43Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b7/53/1f7eb7ee83a06c400089ec7cb841cbd581c2edd7a4b21eb2f31030b88daa/rapidfuzz-3.13.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:2fd0975e015b05c79a97f38883a11236f5a24cca83aa992bd2558ceaa5652b26", size = 1445379, upload-time = "2025-04-03T20:36:16.439Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/07/09/de8069a4599cc8e6d194e5fa1782c561151dea7d5e2741767137e2a8c1f0/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5d4e13593d298c50c4f94ce453f757b4b398af3fa0fd2fde693c3e51195b7f69", size = 1405986, upload-time = "2025-04-03T20:36:18.447Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5d/77/d9a90b39c16eca20d70fec4ca377fbe9ea4c0d358c6e4736ab0e0e78aaf6/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ed6f416bda1c9133000009d84d9409823eb2358df0950231cc936e4bf784eb97", size = 5310809, upload-time = "2025-04-03T20:36:20.324Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1e/7d/14da291b0d0f22262d19522afaf63bccf39fc027c981233fb2137a57b71f/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:1dc82b6ed01acb536b94a43996a94471a218f4d89f3fdd9185ab496de4b2a981", size = 1629394, upload-time = "2025-04-03T20:36:22.256Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b7/e4/79ed7e4fa58f37c0f8b7c0a62361f7089b221fe85738ae2dbcfb815e985a/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:e9d824de871daa6e443b39ff495a884931970d567eb0dfa213d234337343835f", size = 1600544, upload-time = "2025-04-03T20:36:24.207Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4e/20/e62b4d13ba851b0f36370060025de50a264d625f6b4c32899085ed51f980/rapidfuzz-3.13.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2d18228a2390375cf45726ce1af9d36ff3dc1f11dce9775eae1f1b13ac6ec50f", size = 3052796, upload-time = "2025-04-03T20:36:26.279Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cd/8d/55fdf4387dec10aa177fe3df8dbb0d5022224d95f48664a21d6b62a5299d/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:9f5fe634c9482ec5d4a6692afb8c45d370ae86755e5f57aa6c50bfe4ca2bdd87", size = 2464016, upload-time = "2025-04-03T20:36:28.525Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/9b/be/0872f6a56c0f473165d3b47d4170fa75263dc5f46985755aa9bf2bbcdea1/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:694eb531889f71022b2be86f625a4209c4049e74be9ca836919b9e395d5e33b3", size = 7556725, upload-time = "2025-04-03T20:36:30.629Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5d/f3/6c0750e484d885a14840c7a150926f425d524982aca989cdda0bb3bdfa57/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:11b47b40650e06147dee5e51a9c9ad73bb7b86968b6f7d30e503b9f8dd1292db", size = 2859052, upload-time = "2025-04-03T20:36:32.836Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6f/98/5a3a14701b5eb330f444f7883c9840b43fb29c575e292e09c90a270a6e07/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:98b8107ff14f5af0243f27d236bcc6e1ef8e7e3b3c25df114e91e3a99572da73", size = 3390219, upload-time = "2025-04-03T20:36:35.062Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e9/7d/f4642eaaeb474b19974332f2a58471803448be843033e5740965775760a5/rapidfuzz-3.13.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:b836f486dba0aceb2551e838ff3f514a38ee72b015364f739e526d720fdb823a", size = 4377924, upload-time = "2025-04-03T20:36:37.363Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8e/83/fa33f61796731891c3e045d0cbca4436a5c436a170e7f04d42c2423652c3/rapidfuzz-3.13.0-cp312-cp312-win32.whl", hash = "sha256:4671ee300d1818d7bdfd8fa0608580d7778ba701817216f0c17fb29e6b972514", size = 1823915, upload-time = "2025-04-03T20:36:39.451Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/03/25/5ee7ab6841ca668567d0897905eebc79c76f6297b73bf05957be887e9c74/rapidfuzz-3.13.0-cp312-cp312-win_amd64.whl", hash = "sha256:6e2065f68fb1d0bf65adc289c1bdc45ba7e464e406b319d67bb54441a1b9da9e", size = 1616985, upload-time = "2025-04-03T20:36:41.631Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/76/5e/3f0fb88db396cb692aefd631e4805854e02120a2382723b90dcae720bcc6/rapidfuzz-3.13.0-cp312-cp312-win_arm64.whl", hash = "sha256:65cc97c2fc2c2fe23586599686f3b1ceeedeca8e598cfcc1b7e56dc8ca7e2aa7", size = 860116, upload-time = "2025-04-03T20:36:43.915Z" },
]

[[package]]
name = "rarfile"
version = "4.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/3f/3118a797444e7e30e784921c4bfafb6500fb288a0c84cb8c32ed15853c16/rarfile-4.2.tar.gz", hash = "sha256:8e1c8e72d0845ad2b32a47ab11a719bc2e41165ec101fd4d3fe9e92aa3f469ef", size = 153476, upload-time = "2024-04-03T17:10:53.798Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/62/fc/ab37559419ca36dd8dd317c3a98395ed4dcee2beeb28bf6059b972906727/rarfile-4.2-py3-none-any.whl", hash = "sha256:8757e1e3757e32962e229cab2432efc1f15f210823cc96ccba0f6a39d17370c9", size = 29052, upload-time = "2024-04-03T17:10:52.632Z" },
]

[[package]]
name = "redis"
version = "5.2.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/47/da/d283a37303a995cd36f8b92db85135153dc4f7a8e4441aa827721b442cfb/redis-5.2.1.tar.gz", hash = "sha256:16f2e22dff21d5125e8481515e386711a34cbec50f0e44413dd7d9c060a54e0f", size = 4608355, upload-time = "2024-12-06T09:50:41.956Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3c/5f/fa26b9b2672cbe30e07d9a5bdf39cf16e3b80b42916757c5f92bca88e4ba/redis-5.2.1-py3-none-any.whl", hash = "sha256:ee7e1056b9aea0f04c6c2ed59452947f34c4940ee025f5dd83e6a6418b6989e4", size = 261502, upload-time = "2024-12-06T09:50:39.656Z" },
]

[[package]]
name = "regex"
version = "2022.3.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4f/17/21e7195da87bcdbdd4ec32fdfdc87ccd5cf97b0a18e0f08ecacbaefca284/regex-2022.3.2.tar.gz", hash = "sha256:79e5af1ff258bc0fe0bdd6f69bc4ae33935a898e3cbefbbccf22e88a27fa053b", size = 383148, upload-time = "2022-03-02T02:10:13.849Z" }

[[package]]
name = "requests"
version = "2.32.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e1/0a/929373653770d8a0d7ea76c37de6e41f11eb07559b103b1c02cafb3f7cf8/requests-2.32.4.tar.gz", hash = "sha256:27d0316682c8a29834d3264820024b62a36942083d52caf2f14c0591336d3422", size = 135258, upload-time = "2025-06-09T16:43:07.34Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl", hash = "sha256:27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c", size = 64847, upload-time = "2025-06-09T16:43:05.728Z" },
]

[[package]]
name = "rjieba"
version = "0.1.13"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ec/d6/00bce0f8997f01d4c626b87791597c02d40602d7eeedf785edd194ee49b6/rjieba-0.1.13.tar.gz", hash = "sha256:3ff7396e645c6ebf8c780ac2d585a6c92ddc5e135be83b0da0da9f995ae62f46", size = 11686, upload-time = "2025-01-13T13:17:29.653Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a9/55/2264d3c8766e439ab49de587b06eb34e1ac7bb4e8c62f96a0ee8be3aebbd/rjieba-0.1.13-cp38-abi3-macosx_10_12_x86_64.whl", hash = "sha256:96230084c67faf250c9e014603c816ce506662492c6edfff41043a82f47f4dd5", size = 3276395, upload-time = "2025-01-13T13:17:07.606Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/91/38/5930dd77855d51b3bc4a1f1324cdfc8b47894fced3beb61c4aa0ecca4f3a/rjieba-0.1.13-cp38-abi3-macosx_11_0_arm64.whl", hash = "sha256:6b2dc0edef2c2779a0d0f85f92edcf4e77e31999c327fc4e330db54019891157", size = 3240988, upload-time = "2025-01-13T13:16:57.813Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f6/06/38463ccc3dea4885e2b8a016f55f4ae805b16882ca2472d31ca61359e97e/rjieba-0.1.13-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:fd5ff297cacb39ad688d2af3ab21971c0fed487ba736a2058583ab71e17b959b", size = 3553258, upload-time = "2025-01-13T13:16:30.317Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bb/11/7f3aa4591125a590fde6fd14a55c7d09d7726aea4fc4da5df9dcadd0a3bc/rjieba-0.1.13-cp38-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:8b2a0f68aa56915359d1b972f536fa202d0f27e16a19a6d18c001a86e96c38ae", size = 3367320, upload-time = "2025-01-13T13:16:35.06Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/68/87/948c6c3cba34d3ecaef719edd9335433eab96c8b02d1b6112f39e6fc525b/rjieba-0.1.13-cp38-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:3af568cfb4d06aa45f0ee8f1115bf296bc95973183be75c1471cf26938a327bb", size = 3620189, upload-time = "2025-01-13T13:16:38.632Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/51/3b/98499dc4796e7964901a2d5d878cffb5525ea03df9615f08a65410cce118/rjieba-0.1.13-cp38-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:03e288f1bc5bd798feb03e6d4fec61c0a9a1c10300b635691c1c062b25c142a9", size = 3707587, upload-time = "2025-01-13T13:16:42.063Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/41/e475f2936637bcd37092b8b63173979eac4c482d9d56151af1d82bd254a3/rjieba-0.1.13-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0ddfb33110815a4cd33673679dde843133de82da4cb29abfa5f2a5b90cc836b3", size = 3575419, upload-time = "2025-01-13T13:16:53.674Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a6/f7/4c7b77eb1a8ae4e2f6bcb855580a32e3692d18d59f23576626aa5e27cf3d/rjieba-0.1.13-cp38-abi3-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:fdbd36caed6e9eab59bab4f18d4a606521c431231053b1681bb867a240a5254f", size = 3420263, upload-time = "2025-01-13T13:16:46.798Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/67/2d/6a7cf5d457cef002b6987895d300cd717b18fa2b8584fa448b056bdf2caa/rjieba-0.1.13-cp38-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:49553f1326be77f4437f17e9696b495ffb9f70866048796f2cadca9aa27918da", size = 3708172, upload-time = "2025-01-13T13:17:12.996Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cf/55/1ee396d72ebf367339b3c9caf331562c5c6d5842ba67487af82732d7e0a0/rjieba-0.1.13-cp38-abi3-musllinux_1_2_armv7l.whl", hash = "sha256:04140779f395c07677b69a0ced51e759ad66c33979135f424ebd252dcd434d87", size = 3618156, upload-time = "2025-01-13T13:17:16.401Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/06/5d/9bbb9bc6249484a39027978d953ac5c0c69f5b371dc62a609f51e5a805e2/rjieba-0.1.13-cp38-abi3-musllinux_1_2_i686.whl", hash = "sha256:77fd4a8ba12455a70c9f74c7d07b2ed812c394eabc2381d40569bec0217e39db", size = 3554012, upload-time = "2025-01-13T13:17:23.394Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/46/1a/3410ee64b6cd89ccdb1842a4204f22963d03ac8c7543e50fbbc7a6ce1d30/rjieba-0.1.13-cp38-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:0294fa41d51048c97558c1b9f712e40eb97c6b21ec793a7a21b575d983cdc81d", size = 3728366, upload-time = "2025-01-13T13:17:28.214Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/79/d3/5277c73579ac5343d6e3982e3ed3a1983c3b2ff1cb34be8debdfa7013561/rjieba-0.1.13-cp38-abi3-win32.whl", hash = "sha256:c836e064ac306c999603e89da258f8d8da57e66da5abe39240a02da8775e8385", size = 3035372, upload-time = "2025-01-13T13:17:37.492Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/61/69/948256cb4c3227970c47adce64374f419d282e657e0cb70076db862393ae/rjieba-0.1.13-cp38-abi3-win_amd64.whl", hash = "sha256:58b69e98b5291c42e39ef5c18106360881337aac654f1c869065f65c8a694b6d", size = 3116650, upload-time = "2025-01-13T13:17:32.889Z" },
]

[[package]]
name = "rtree"
version = "1.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/37/5dd86f8be05dbf6ab1be9817958ecd2dafc6913639a511ad6f71365f1c3e/Rtree-1.1.0.tar.gz", hash = "sha256:6f8ee504dde5d005b25b08aaf5be0b3404af3ad5fece6e1ddcde35908a798a95", size = 49220, upload-time = "2023-10-16T21:15:55.388Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2d/34/6ff42901722a18b81427bc240483276ebda786197125901975f7b9a6c9d2/Rtree-1.1.0-py3-none-macosx_10_9_x86_64.whl", hash = "sha256:5dc612959233cf07f31c8f1a4cf1587eea86dc2fc6d6938f35f28d312bbbbd3e", size = 436746, upload-time = "2023-10-16T21:15:40.655Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1c/a6/524326a993fad04215299e0023ed9c93b15d5bd62822d887729a90c27d52/Rtree-1.1.0-py3-none-macosx_11_0_arm64.whl", hash = "sha256:eac35fc095e36f5e5678f7777f296b6a076332c65cfbe00e5cd54b0518f6d71f", size = 398188, upload-time = "2023-10-16T21:15:42.508Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/de/23/66c3589a3de737e963859f39a1ce2bc98796b3427c2ebdc614b5c02cfda5/Rtree-1.1.0-py3-none-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:91d3e4b19efa77fc3c09fbbbed48c588bfa056c8b73b5bd084fb0d2d37654775", size = 446204, upload-time = "2023-10-16T21:15:44.217Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a3/f1/5716356fb4175df7dac7c8d96444dd28e14813f471a8a6eb09712741b1f1/Rtree-1.1.0-py3-none-manylinux2014_i686.manylinux_2_17_i686.whl", hash = "sha256:f1787187b7d74845484e2da029775928ac6af2d453d401e0082022c6552c49b1", size = 513454, upload-time = "2023-10-16T21:15:46.104Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/04/74f48b042aabdafa122289957240f4c013aec8c9045c3ce2fe3d1fa7a937/Rtree-1.1.0-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:96307058e0149b6124b5af1d48a145b3c1e0086db44de50b435f173e934fc510", size = 488610, upload-time = "2023-10-16T21:15:47.676Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b1/cc/670a9d48b7710117647444680f6897b7e398aabbc41a6b01215baaaa044d/Rtree-1.1.0-py3-none-musllinux_1_1_aarch64.whl", hash = "sha256:22c670ed503de4f42789a7ca1554b219d5820133edaf2a28a051bad2ac90bbca", size = 943869, upload-time = "2023-10-16T21:15:49.489Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/21/b2/b3d2948c6a81320df9b4eb997ec66382f518b561187bd6e4d4dc8333fc6f/Rtree-1.1.0-py3-none-musllinux_1_1_i686.whl", hash = "sha256:818dde0ef54166edfb438a3bbf97bcc2eb0b984ab9ec5e7d20779479e456bfad", size = 1066575, upload-time = "2023-10-16T21:15:50.852Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/77/41/2fdce6bd057638f9947f0eaa3971402fe01f2f272951f49cdfb1df017117/Rtree-1.1.0-py3-none-musllinux_1_1_x86_64.whl", hash = "sha256:696a5f3ecf8b622ab5827e107e762ecffa0555944433f2824dd7f46b4afc410b", size = 990047, upload-time = "2023-10-16T21:15:52.389Z" },
]

[[package]]
name = "ruff"
version = "0.9.10"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/20/8e/fafaa6f15c332e73425d9c44ada85360501045d5ab0b81400076aff27cf6/ruff-0.9.10.tar.gz", hash = "sha256:9bacb735d7bada9cfb0f2c227d3658fc443d90a727b47f206fb33f52f3c0eac7", size = 3759776, upload-time = "2025-03-07T15:27:44.363Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/73/b2/af7c2cc9e438cbc19fafeec4f20bfcd72165460fe75b2b6e9a0958c8c62b/ruff-0.9.10-py3-none-linux_armv6l.whl", hash = "sha256:eb4d25532cfd9fe461acc83498361ec2e2252795b4f40b17e80692814329e42d", size = 10049494, upload-time = "2025-03-07T15:26:51.268Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/12/03f6dfa1b95ddd47e6969f0225d60d9d7437c91938a310835feb27927ca0/ruff-0.9.10-py3-none-macosx_10_12_x86_64.whl", hash = "sha256:188a6638dab1aa9bb6228a7302387b2c9954e455fb25d6b4470cb0641d16759d", size = 10853584, upload-time = "2025-03-07T15:26:56.104Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/02/49/1c79e0906b6ff551fb0894168763f705bf980864739572b2815ecd3c9df0/ruff-0.9.10-py3-none-macosx_11_0_arm64.whl", hash = "sha256:5284dcac6b9dbc2fcb71fdfc26a217b2ca4ede6ccd57476f52a587451ebe450d", size = 10155692, upload-time = "2025-03-07T15:27:01.385Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5b/01/85e8082e41585e0e1ceb11e41c054e9e36fed45f4b210991052d8a75089f/ruff-0.9.10-py3-none-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:47678f39fa2a3da62724851107f438c8229a3470f533894b5568a39b40029c0c", size = 10369760, upload-time = "2025-03-07T15:27:04.023Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a1/90/0bc60bd4e5db051f12445046d0c85cc2c617095c0904f1aa81067dc64aea/ruff-0.9.10-py3-none-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:99713a6e2766b7a17147b309e8c915b32b07a25c9efd12ada79f217c9c778b3e", size = 9912196, upload-time = "2025-03-07T15:27:06.93Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/66/ea/0b7e8c42b1ec608033c4d5a02939c82097ddcb0b3e393e4238584b7054ab/ruff-0.9.10-py3-none-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:524ee184d92f7c7304aa568e2db20f50c32d1d0caa235d8ddf10497566ea1a12", size = 11434985, upload-time = "2025-03-07T15:27:10.082Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d5/86/3171d1eff893db4f91755175a6e1163c5887be1f1e2f4f6c0c59527c2bfd/ruff-0.9.10-py3-none-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", hash = "sha256:df92aeac30af821f9acf819fc01b4afc3dfb829d2782884f8739fb52a8119a16", size = 12155842, upload-time = "2025-03-07T15:27:12.727Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/89/9e/700ca289f172a38eb0bca752056d0a42637fa17b81649b9331786cb791d7/ruff-0.9.10-py3-none-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:de42e4edc296f520bb84954eb992a07a0ec5a02fecb834498415908469854a52", size = 11613804, upload-time = "2025-03-07T15:27:15.944Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/92/648020b3b5db180f41a931a68b1c8575cca3e63cec86fd26807422a0dbad/ruff-0.9.10-py3-none-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:d257f95b65806104b6b1ffca0ea53f4ef98454036df65b1eda3693534813ecd1", size = 13823776, upload-time = "2025-03-07T15:27:18.996Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5e/a6/cc472161cd04d30a09d5c90698696b70c169eeba2c41030344194242db45/ruff-0.9.10-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b60dec7201c0b10d6d11be00e8f2dbb6f40ef1828ee75ed739923799513db24c", size = 11302673, upload-time = "2025-03-07T15:27:21.655Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6c/db/d31c361c4025b1b9102b4d032c70a69adb9ee6fde093f6c3bf29f831c85c/ruff-0.9.10-py3-none-musllinux_1_2_aarch64.whl", hash = "sha256:d838b60007da7a39c046fcdd317293d10b845001f38bcb55ba766c3875b01e43", size = 10235358, upload-time = "2025-03-07T15:27:24.72Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d1/86/d6374e24a14d4d93ebe120f45edd82ad7dcf3ef999ffc92b197d81cdc2a5/ruff-0.9.10-py3-none-musllinux_1_2_armv7l.whl", hash = "sha256:ccaf903108b899beb8e09a63ffae5869057ab649c1e9231c05ae354ebc62066c", size = 9886177, upload-time = "2025-03-07T15:27:27.282Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/00/62/a61691f6eaaac1e945a1f3f59f1eea9a218513139d5b6c2b8f88b43b5b8f/ruff-0.9.10-py3-none-musllinux_1_2_i686.whl", hash = "sha256:f9567d135265d46e59d62dc60c0bfad10e9a6822e231f5b24032dba5a55be6b5", size = 10864747, upload-time = "2025-03-07T15:27:30.637Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ee/94/2c7065e1d92a8a8a46d46d9c3cf07b0aa7e0a1e0153d74baa5e6620b4102/ruff-0.9.10-py3-none-musllinux_1_2_x86_64.whl", hash = "sha256:5f202f0d93738c28a89f8ed9eaba01b7be339e5d8d642c994347eaa81c6d75b8", size = 11360441, upload-time = "2025-03-07T15:27:33.356Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/8f/1f545ea6f9fcd7bf4368551fb91d2064d8f0577b3079bb3f0ae5779fb773/ruff-0.9.10-py3-none-win32.whl", hash = "sha256:bfb834e87c916521ce46b1788fbb8484966e5113c02df216680102e9eb960029", size = 10247401, upload-time = "2025-03-07T15:27:35.994Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4f/18/fb703603ab108e5c165f52f5b86ee2aa9be43bb781703ec87c66a5f5d604/ruff-0.9.10-py3-none-win_amd64.whl", hash = "sha256:f2160eeef3031bf4b17df74e307d4c5fb689a6f3a26a2de3f7ef4044e3c484f1", size = 11366360, upload-time = "2025-03-07T15:27:38.66Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/35/85/338e603dc68e7d9994d5d84f24adbf69bae760ba5efd3e20f5ff2cec18da/ruff-0.9.10-py3-none-win_arm64.whl", hash = "sha256:5fd804c0327a5e5ea26615550e706942f348b197d5475ff34c19733aee4b2e69", size = 10436892, upload-time = "2025-03-07T15:27:41.687Z" },
]

[[package]]
name = "scikit-image"
version = "0.23.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "imageio", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "lazy-loader", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "networkx", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "numpy", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "packaging", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "pillow", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "scipy", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
    { name = "tifffile", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/24/ce/183ff64ed397911a9d3b671714f8a2618af407b427a40ca48550fb0f7bd7/scikit_image-0.23.2.tar.gz", hash = "sha256:c9da4b2c3117e3e30364a3d14496ee5c72b09eb1a4ab1292b302416faa360590", size = 22678261, upload-time = "2024-04-20T19:26:02.441Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/63/7624e42860d47f7a4a150de2b9ce0d07c6bc65823373e9a7da66101f3545/scikit_image-0.23.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:15bfb4e8d7bd90a967e6a3c3ab6be678063fc45e950b730684a8db46a02ff892", size = 13999693, upload-time = "2024-04-20T19:25:48.294Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/08/f5/7e834e80ce7d3b97caaba90510bb34579928c1ce811f64fb8620cf110b5c/scikit_image-0.23.2-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:5736e66d01b11cd90988ec24ab929c80a03af28f690189c951886891ebf63154", size = 13327171, upload-time = "2024-04-20T19:25:50.637Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/02/9ce6452333c4db187c77279358f990af221d60fcfdff2fcf3a1f2ab9dabc/scikit_image-0.23.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:3597ac5d8f51dafbcb7433ef1fdefdefb535f50745b2002ae0a5d651df4f063b", size = 14116316, upload-time = "2024-04-20T19:25:53.903Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ac/c3/241bdbc038dc90a57b8f2ab9a5d96e5e7fc9809eb7833dc755aeac5bcc54/scikit_image-0.23.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1978be2abe3c3c3189a99a411d48bbb1306f7c2debb3aefbf426e23947f26623", size = 14858370, upload-time = "2024-04-20T19:25:56.931Z" },
]

[[package]]
name = "scikit-learn"
version = "1.3.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "joblib" },
    { name = "numpy" },
    { name = "scipy" },
    { name = "threadpoolctl" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/88/00/835e3d280fdd7784e76bdef91dd9487582d7951a7254f59fc8004fc8b213/scikit-learn-1.3.2.tar.gz", hash = "sha256:a2f54c76accc15a34bfb9066e6c7a56c1e7235dda5762b990792330b52ccfb05", size = 7510251, upload-time = "2023-10-23T13:47:55.287Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/7e/2c3b82c8c29aa384c8bf859740419278627d2cdd0050db503c8840e72477/scikit_learn-1.3.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:8db94cd8a2e038b37a80a04df8783e09caac77cbe052146432e67800e430c028", size = 9979322, upload-time = "2023-10-23T13:47:03.977Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cf/fc/6c52ffeb587259b6b893b7cac268f1eb1b5426bcce1aa20e53523bfe6944/scikit_learn-1.3.2-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:61a6efd384258789aa89415a410dcdb39a50e19d3d8410bd29be365bcdd512d5", size = 9270688, upload-time = "2023-10-23T13:47:07.316Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e5/a7/6f4ae76f72ae9de162b97acbf1f53acbe404c555f968d13da21e4112a002/scikit_learn-1.3.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:cb06f8dce3f5ddc5dee1715a9b9f19f20d295bed8e3cd4fa51e1d050347de525", size = 10280398, upload-time = "2023-10-23T13:47:10.796Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5d/b7/ee35904c07a0666784349529412fbb9814a56382b650d30fd9d6be5e5054/scikit_learn-1.3.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:5b2de18d86f630d68fe1f87af690d451388bb186480afc719e5f770590c2ef6c", size = 10796478, upload-time = "2023-10-23T13:47:14.077Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fe/6b/db949ed5ac367987b1f250f070f340b7715d22f0c9c965bdf07de6ca75a3/scikit_learn-1.3.2-cp312-cp312-win_amd64.whl", hash = "sha256:0402638c9a7c219ee52c94cbebc8fcb5eb9fe9c773717965c1f4185588ad3107", size = 9133979, upload-time = "2023-10-23T13:47:17.389Z" },
]

[[package]]
name = "scipy"
version = "1.16.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f5/4a/b927028464795439faec8eaf0b03b011005c487bb2d07409f28bf30879c4/scipy-1.16.1.tar.gz", hash = "sha256:44c76f9e8b6e8e488a586190ab38016e4ed2f8a038af7cd3defa903c0a2238b3", size = 30580861, upload-time = "2025-07-27T16:33:30.834Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f8/d9/ec4864f5896232133f51382b54a08de91a9d1af7a76dfa372894026dfee2/scipy-1.16.1-cp312-cp312-macosx_10_14_x86_64.whl", hash = "sha256:81b433bbeaf35728dad619afc002db9b189e45eebe2cd676effe1fb93fef2b9c", size = 36575194, upload-time = "2025-07-27T16:27:41.321Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5c/6d/40e81ecfb688e9d25d34a847dca361982a6addf8e31f0957b1a54fbfa994/scipy-1.16.1-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:886cc81fdb4c6903a3bb0464047c25a6d1016fef77bb97949817d0c0d79f9e04", size = 28594590, upload-time = "2025-07-27T16:27:49.204Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0e/37/9f65178edfcc629377ce9a64fc09baebea18c80a9e57ae09a52edf84880b/scipy-1.16.1-cp312-cp312-macosx_14_0_arm64.whl", hash = "sha256:15240c3aac087a522b4eaedb09f0ad061753c5eebf1ea430859e5bf8640d5919", size = 20866458, upload-time = "2025-07-27T16:27:54.98Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2c/7b/749a66766871ea4cb1d1ea10f27004db63023074c22abed51f22f09770e0/scipy-1.16.1-cp312-cp312-macosx_14_0_x86_64.whl", hash = "sha256:65f81a25805f3659b48126b5053d9e823d3215e4a63730b5e1671852a1705921", size = 23539318, upload-time = "2025-07-27T16:28:01.604Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c4/db/8d4afec60eb833a666434d4541a3151eedbf2494ea6d4d468cbe877f00cd/scipy-1.16.1-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:6c62eea7f607f122069b9bad3f99489ddca1a5173bef8a0c75555d7488b6f725", size = 33292899, upload-time = "2025-07-27T16:28:09.147Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/51/1e/79023ca3bbb13a015d7d2757ecca3b81293c663694c35d6541b4dca53e98/scipy-1.16.1-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:f965bbf3235b01c776115ab18f092a95aa74c271a52577bcb0563e85738fd618", size = 35162637, upload-time = "2025-07-27T16:28:17.535Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b6/49/0648665f9c29fdaca4c679182eb972935b3b4f5ace41d323c32352f29816/scipy-1.16.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:f006e323874ffd0b0b816d8c6a8e7f9a73d55ab3b8c3f72b752b226d0e3ac83d", size = 35490507, upload-time = "2025-07-27T16:28:25.705Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/62/8f/66cbb9d6bbb18d8c658f774904f42a92078707a7c71e5347e8bf2f52bb89/scipy-1.16.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e8fd15fc5085ab4cca74cb91fe0a4263b1f32e4420761ddae531ad60934c2119", size = 37923998, upload-time = "2025-07-27T16:28:34.339Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/14/c3/61f273ae550fbf1667675701112e380881905e28448c080b23b5a181df7c/scipy-1.16.1-cp312-cp312-win_amd64.whl", hash = "sha256:f7b8013c6c066609577d910d1a2a077021727af07b6fab0ee22c2f901f22352a", size = 38508060, upload-time = "2025-07-27T16:28:43.242Z" },
]

[[package]]
name = "scriber"
version = "1.0.0"
source = { virtual = "." }
dependencies = [
    { name = "a2wsgi" },
    { name = "aenum" },
    { name = "aiofile" },
    { name = "aiomysql" },
    { name = "aiopg" },
    { name = "aipod" },
    { name = "alembic" },
    { name = "apispec" },
    { name = "asyncpg" },
    { name = "attrs" },
    { name = "babel" },
    { name = "beautifulsoup4" },
    { name = "calliper-diff" },
    { name = "celery", extra = ["redis"] },
    { name = "click" },
    { name = "cryptography" },
    { name = "cx-oracle" },
    { name = "dateparser" },
    { name = "docopt" },
    { name = "email-validator" },
    { name = "farm" },
    { name = "fastapi" },
    { name = "fastapi-permissions" },
    { name = "fire" },
    { name = "flashtext" },
    { name = "gino" },
    { name = "glazer-docx-convert" },
    { name = "glom" },
    { name = "gunicorn" },
    { name = "imapclient" },
    { name = "imbalanced-learn" },
    { name = "interval" },
    { name = "invoke" },
    { name = "jinja2" },
    { name = "jsondiff" },
    { name = "magika" },
    { name = "marshmallow" },
    { name = "msgspec" },
    { name = "onnxconverter-common" },
    { name = "openai" },
    { name = "opencc", version = "1.1.7", source = { url = "https://fm.paodingai.com/api/public/dl/jqeZI7gv/Soft/pypi/opencc-1.1.7-cp312-cp312-macosx_11_0_arm64.whl" }, marker = "sys_platform == 'darwin'" },
    { name = "openpyxl" },
    { name = "oracledb" },
    { name = "palladium", version = "0.5.7", source = { registry = "http://**********:3141/cheftin/pypi" }, marker = "sys_platform == 'darwin'" },
    { name = "palladium", version = "0.5.19", source = { registry = "http://**********:3141/cheftin/pypi" }, marker = "sys_platform == 'linux'" },
    { name = "pandas" },
    { name = "pdfkit" },
    { name = "pdfparser", version = "2.3.24", source = { registry = "http://**********:3141/cheftin/pypi" }, marker = "sys_platform == 'darwin'" },
    { name = "pdfparser", version = "2.3.38", source = { registry = "http://**********:3141/cheftin/pypi" }, marker = "sys_platform == 'linux'" },
    { name = "peewee-async" },
    { name = "pgvector" },
    { name = "pika" },
    { name = "playwright" },
    { name = "pydantic" },
    { name = "pyjwt" },
    { name = "pymysql" },
    { name = "pyparsing" },
    { name = "pypinyin" },
    { name = "python-docx" },
    { name = "python-pptx" },
    { name = "python-redis-lock" },
    { name = "python-statemachine" },
    { name = "rjieba" },
    { name = "scikit-learn" },
    { name = "skl2onnx" },
    { name = "speedy" },
    { name = "sqlalchemy" },
    { name = "supervisor" },
    { name = "tiktoken" },
    { name = "tornado" },
    { name = "traceback-with-variables" },
    { name = "user-agents" },
    { name = "utensils" },
    { name = "uvicorn", extra = ["standard"] },
    { name = "uvicorn-worker" },
    { name = "weasyprint" },
    { name = "webargs" },
    { name = "websocket-client" },
    { name = "wordinsight" },
    { name = "xlrd" },
    { name = "xlsxwriter" },
    { name = "xlutils" },
]

[package.dev-dependencies]
dev = [
    { name = "check-requirements-txt" },
    { name = "pre-commit" },
    { name = "pylint" },
    { name = "pytest" },
    { name = "pytest-asyncio" },
    { name = "pytest-env" },
    { name = "pytest-xdist" },
    { name = "ruff" },
]

[package.metadata]
requires-dist = [
    { name = "a2wsgi", specifier = "==1.10.0" },
    { name = "aenum", specifier = "==3.1.16" },
    { name = "aiofile" },
    { name = "aiomysql", specifier = "~=0.2.0" },
    { name = "aiopg", specifier = "~=1.4.0" },
    { name = "aipod", marker = "python_full_version >= '3.12'", specifier = "~=1.3.1", index = "http://**********:3141/cheftin/pypi" },
    { name = "alembic", specifier = "~=1.8.1" },
    { name = "apispec" },
    { name = "asyncpg", marker = "python_full_version >= '3.12'", specifier = "~=0.29.0" },
    { name = "attrs", specifier = "==21.4.0" },
    { name = "babel", specifier = "==2.9.1" },
    { name = "beautifulsoup4" },
    { name = "calliper-diff", specifier = "~=1.3.4", index = "http://**********:3141/cheftin/pypi" },
    { name = "celery", extras = ["redis"], specifier = "~=5.5.0" },
    { name = "click" },
    { name = "cryptography", specifier = "~=39.0.0" },
    { name = "cx-oracle", specifier = "~=8.3.0" },
    { name = "dateparser", specifier = "==1.1.1" },
    { name = "docopt" },
    { name = "email-validator", specifier = "~=2.2.0" },
    { name = "farm", index = "http://**********:3141/cheftin/pypi" },
    { name = "fastapi", specifier = "~=0.115.12" },
    { name = "fastapi-permissions", specifier = "~=0.2.7" },
    { name = "fire" },
    { name = "flashtext", specifier = "==2.7" },
    { name = "gino", specifier = "==1.1.0rc1" },
    { name = "glazer-docx-convert", specifier = "~=0.3.34", index = "http://**********:3141/cheftin/pypi" },
    { name = "glom", specifier = "~=24.11.0" },
    { name = "gunicorn", specifier = ">=20.0.4" },
    { name = "imapclient", specifier = "~=3.0.1" },
    { name = "imbalanced-learn", specifier = "~=0.12.3" },
    { name = "interval", specifier = "~=1.0.0" },
    { name = "invoke", specifier = "~=2.2.0", index = "http://**********:3141/cheftin/pypi" },
    { name = "jinja2", specifier = "~=3.1.4" },
    { name = "jsondiff" },
    { name = "magika", specifier = "~=0.6.2" },
    { name = "marshmallow", specifier = "~=4.0.0" },
    { name = "msgspec" },
    { name = "onnxconverter-common", specifier = "~=1.16.0", index = "http://**********:3141/cheftin/pypi" },
    { name = "openai", specifier = "~=1.55.3" },
    { name = "opencc", marker = "sys_platform == 'darwin'", url = "https://fm.paodingai.com/api/public/dl/jqeZI7gv/Soft/pypi/opencc-1.1.7-cp312-cp312-macosx_11_0_arm64.whl" },
    { name = "openpyxl", specifier = "~=3.1.0" },
    { name = "oracledb", marker = "python_full_version >= '3.12'", specifier = "~=2.1.2" },
    { name = "palladium", marker = "sys_platform == 'darwin'", specifier = "==0.5.7", index = "http://**********:3141/cheftin/pypi" },
    { name = "palladium", marker = "sys_platform == 'linux'", specifier = "==0.5.19", index = "http://**********:3141/cheftin/pypi" },
    { name = "pandas", specifier = "~=2.2.2" },
    { name = "pdfkit", specifier = "==1.0.0" },
    { name = "pdfparser", marker = "sys_platform == 'darwin'", specifier = "==2.3.24", index = "http://**********:3141/cheftin/pypi" },
    { name = "pdfparser", marker = "sys_platform == 'linux'", specifier = "==2.3.38", index = "http://**********:3141/cheftin/pypi" },
    { name = "peewee-async", specifier = "~=0.10.0" },
    { name = "pgvector", specifier = "~=0.3.0" },
    { name = "pika", specifier = "==1.2.0" },
    { name = "playwright", specifier = "~=1.43.0" },
    { name = "pydantic", specifier = "~=2.5.2" },
    { name = "pyjwt", specifier = "~=2.8.0" },
    { name = "pymysql", specifier = "==1.1.1" },
    { name = "pyparsing", specifier = "~=3.1.1" },
    { name = "pypinyin", specifier = "==0.42.0" },
    { name = "python-docx", specifier = ">=0.8,<0.9" },
    { name = "python-pptx", specifier = "~=1.0.2" },
    { name = "python-redis-lock", specifier = "~=3.7.0" },
    { name = "python-statemachine", specifier = "==2.5.0" },
    { name = "rjieba" },
    { name = "scikit-learn", specifier = "~=1.3.2" },
    { name = "skl2onnx", specifier = "~=1.17.0" },
    { name = "speedy", specifier = "~=0.1.99", index = "http://**********:3141/cheftin/pypi" },
    { name = "sqlalchemy", specifier = ">=1.3,<1.4" },
    { name = "supervisor", specifier = ">=6.6.7", index = "http://**********:3141/cheftin/pypi" },
    { name = "tiktoken", specifier = "~=0.7.0" },
    { name = "tornado", specifier = "~=6.4.2" },
    { name = "traceback-with-variables", specifier = "~=2.0.4" },
    { name = "user-agents", specifier = "~=2.2.0" },
    { name = "utensils", specifier = ">=0.1.81", index = "http://**********:3141/cheftin/pypi" },
    { name = "uvicorn", extras = ["standard"], specifier = "~=0.34.3" },
    { name = "uvicorn-worker", specifier = "~=0.3.0" },
    { name = "weasyprint", specifier = "~=62.3" },
    { name = "webargs", specifier = "~=8.7.0" },
    { name = "websocket-client" },
    { name = "wordinsight", specifier = "==0.0.88", index = "http://**********:3141/cheftin/pypi" },
    { name = "xlrd", specifier = "~=2.0.1" },
    { name = "xlsxwriter", specifier = "~=3.2.5" },
    { name = "xlutils" },
]

[package.metadata.requires-dev]
dev = [
    { name = "check-requirements-txt", specifier = "~=1.2.0" },
    { name = "pre-commit", specifier = "~=4.0.0" },
    { name = "pylint", specifier = "~=3.3.4" },
    { name = "pytest", specifier = "~=8.3.3" },
    { name = "pytest-asyncio", specifier = "~=0.24.0" },
    { name = "pytest-env", specifier = "~=1.1.4" },
    { name = "pytest-xdist", specifier = "~=3.6.1" },
    { name = "ruff", specifier = "~=0.9.7" },
]

[[package]]
name = "setuptools"
version = "80.9.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/18/5d/3bf57dcd21979b887f014ea83c24ae194cfcd12b9e0fda66b957c69d1fca/setuptools-80.9.0.tar.gz", hash = "sha256:f36b47402ecde768dbfafc46e8e4207b4360c654f1f3bb84475f0a28628fb19c", size = 1319958, upload-time = "2025-05-27T00:56:51.443Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a3/dc/17031897dae0efacfea57dfd3a82fdd2a2aeb58e0ff71b77b87e44edc772/setuptools-80.9.0-py3-none-any.whl", hash = "sha256:062d34222ad13e0cc312a4c02d73f059e86a4acbfbdea8f8f76b28c99f306922", size = 1201486, upload-time = "2025-05-27T00:56:49.664Z" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/94/e7/b2c673351809dca68a0e064b6af791aa332cf192da575fd474ed7d6f16a2/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81", size = 34031, upload-time = "2024-12-04T17:35:28.174Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274", size = 11050, upload-time = "2024-12-04T17:35:26.475Z" },
]

[[package]]
name = "skl2onnx"
version = "1.17.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "onnx" },
    { name = "onnxconverter-common" },
    { name = "scikit-learn" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/91/53c1d0085fb11c6ae2b2092160f55380fa361b8ced1144eada49add70adb/skl2onnx-1.17.0.tar.gz", hash = "sha256:7127dc84e470f489f68094ccfff9a5a815b609f700d43e708e6f658a33b06403", size = 931965, upload-time = "2024-05-30T12:28:02.129Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/61/db/343223f105c34cbf8dd1237693e8628deb5009f2a9c4f2e3d2c6546f2766/skl2onnx-1.17.0-py2.py3-none-any.whl", hash = "sha256:27942fc2743efe9dff56380001da4685812d0f5b1b0b9c1a032e80d059d6779a", size = 298421, upload-time = "2024-05-30T12:27:59.555Z" },
]

[[package]]
name = "sniffio"
version = "1.3.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/87/a6771e1546d97e7e041b6ae58d80074f81b7d5121207425c964ddf5cfdbd/sniffio-1.3.1.tar.gz", hash = "sha256:f4324edc670a0f49750a81b895f35c3adb843cca46f0530f79fc1babb23789dc", size = 20372, upload-time = "2024-02-25T23:20:04.057Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl", hash = "sha256:2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2", size = 10235, upload-time = "2024-02-25T23:20:01.196Z" },
]

[[package]]
name = "sortedcontainers"
version = "2.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e8/c4/ba2f8066cceb6f23394729afe52f3bf7adec04bf9ed2c820b39e19299111/sortedcontainers-2.4.0.tar.gz", hash = "sha256:25caa5a06cc30b6b83d11423433f65d1f9d76c4c6a0c90e3379eaa43b9bfdb88", size = 30594, upload-time = "2021-05-16T22:03:42.897Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/32/46/9cb0e58b2deb7f82b84065f37f3bffeb12413f947f9388e4cac22c4621ce/sortedcontainers-2.4.0-py2.py3-none-any.whl", hash = "sha256:a163dcaede0f1c021485e957a39245190e74249897e2ae4b2aa38595db237ee0", size = 29575, upload-time = "2021-05-16T22:03:41.177Z" },
]

[[package]]
name = "soupsieve"
version = "2.7"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3f/f4/4a80cd6ef364b2e8b65b15816a843c0980f7a5a2b4dc701fc574952aa19f/soupsieve-2.7.tar.gz", hash = "sha256:ad282f9b6926286d2ead4750552c8a6142bc4c783fd66b0293547c8fe6ae126a", size = 103418, upload-time = "2025-04-20T18:50:08.518Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e7/9c/0e6afc12c269578be5c0c1c9f4b49a8d32770a080260c333ac04cc1c832d/soupsieve-2.7-py3-none-any.whl", hash = "sha256:6e60cc5c1ffaf1cebcc12e8188320b72071e922c2e897f737cadce79ad5d30c4", size = 36677, upload-time = "2025-04-20T18:50:07.196Z" },
]

[[package]]
name = "speedy"
version = "0.1.99"
source = { registry = "http://**********:3141/cheftin/pypi" }
dependencies = [
    { name = "fastapi" },
    { name = "peewee-async" },
    { name = "utensils" },
]
sdist = { url = "http://**********:3141/cheftin/pypi/+f/580/6d46c7cd16e45/speedy-0.1.99.tar.gz", hash = "sha256:5806d46c7cd16e450735f79719931f194cd84464043e70ccb6f36fad33fac831" }
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/0b9/931e014b2b213/speedy-0.1.99-cp312-cp312-manylinux_2_27_x86_64.whl", hash = "sha256:0b9931e014b2b213aba9020d7d8028eea57d92acf22a3a5db909b1079629abd4" },
]

[[package]]
name = "sqlalchemy"
version = "1.3.24"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c5/ab/81bef2f960abf3cdaf32fbf1994f0c6f5e6a5f1667b5713ed6ebf162b6a2/SQLAlchemy-1.3.24.tar.gz", hash = "sha256:ebbb777cbf9312359b897bf81ba00dae0f5cb69fba2a18265dcc18a6f5ef7519", size = 6353598, upload-time = "2021-03-30T23:04:30.273Z" }

[[package]]
name = "starlette"
version = "0.46.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "anyio" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ce/20/08dfcd9c983f6a6f4a1000d934b9e6d626cff8d2eeb77a89a68eef20a2b7/starlette-0.46.2.tar.gz", hash = "sha256:7f7361f34eed179294600af672f565727419830b54b7b084efe44bb82d2fccd5", size = 2580846, upload-time = "2025-04-13T13:56:17.942Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/0c/9d30a4ebeb6db2b25a841afbb80f6ef9a854fc3b41be131d249a977b4959/starlette-0.46.2-py3-none-any.whl", hash = "sha256:595633ce89f8ffa71a015caed34a5b2dc1c0cdb3f0f1fbd1e69339cf2abeec35", size = 72037, upload-time = "2025-04-13T13:56:16.21Z" },
]

[[package]]
name = "supervisor"
version = "6.6.9"
source = { registry = "http://**********:3141/cheftin/pypi" }
sdist = { url = "http://**********:3141/cheftin/pypi/+f/b26/0021478c9705a/supervisor-6.6.9.tar.gz", hash = "sha256:b260021478c9705aadb3c02c211d629f5476df68d745fbcc90690baf8a88e4df" }

[[package]]
name = "sympy"
version = "1.14.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "mpmath" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/83/d3/803453b36afefb7c2bb238361cd4ae6125a569b4db67cd9e79846ba2d68c/sympy-1.14.0.tar.gz", hash = "sha256:d3d3fe8df1e5a0b42f0e7bdf50541697dbe7d23746e894990c030e2b05e72517", size = 7793921, upload-time = "2025-04-27T18:05:01.611Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a2/09/77d55d46fd61b4a135c444fc97158ef34a095e5681d0a6c10b75bf356191/sympy-1.14.0-py3-none-any.whl", hash = "sha256:e091cc3e99d2141a0ba2847328f5479b05d94a6635cb96148ccb3f34671bd8f5", size = 6299353, upload-time = "2025-04-27T18:04:59.103Z" },
]

[[package]]
name = "symspellpy"
version = "6.7.6"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "editdistpy", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/39/a7/aa1ebf3697f726303af50863ee15ec565be524dbeb4749385e836fdd0cc2/symspellpy-6.7.6.tar.gz", hash = "sha256:3f96149a424411fb5f292a15a650a17ccf8d4946c61e06eae6bf7f9ebcd67ba8", size = 2606194, upload-time = "2021-12-19T08:37:00.487Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d8/aa/a662bbee8561ed17542ccde4aab294a944e2a4eb24370bdaad841e21e17c/symspellpy-6.7.6-py3-none-any.whl", hash = "sha256:c7fb73beaf251903ea8ab71e214e7b31cf1def9351d098b6a2aacb04f4478f41", size = 2610046, upload-time = "2021-12-19T08:36:58.663Z" },
]

[[package]]
name = "termcolor"
version = "3.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ca/6c/3d75c196ac07ac8749600b60b03f4f6094d54e132c4d94ebac6ee0e0add0/termcolor-3.1.0.tar.gz", hash = "sha256:6a6dd7fbee581909eeec6a756cff1d7f7c376063b14e4a298dc4980309e55970", size = 14324, upload-time = "2025-04-30T11:37:53.791Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4f/bd/de8d508070629b6d84a30d01d57e4a65c69aa7f5abe7560b8fad3b50ea59/termcolor-3.1.0-py3-none-any.whl", hash = "sha256:591dd26b5c2ce03b9e43f391264626557873ce1d379019786f99b0c2bee140aa", size = 7684, upload-time = "2025-04-30T11:37:52.382Z" },
]

[[package]]
name = "texttable"
version = "1.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1c/dc/0aff23d6036a4d3bf4f1d8c8204c5c79c4437e25e0ae94ffe4bbb55ee3c2/texttable-1.7.0.tar.gz", hash = "sha256:2d2068fb55115807d3ac77a4ca68fa48803e84ebb0ee2340f858107a36522638" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/24/99/4772b8e00a136f3e01236de33b0efda31ee7077203ba5967fcc76da94d65/texttable-1.7.0-py2.py3-none-any.whl", hash = "sha256:72227d592c82b3d7f672731ae73e4d1f88cd8e2ef5b075a7a7f01a23a3743917" },
]

[[package]]
name = "threadpoolctl"
version = "3.6.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b7/4d/08c89e34946fce2aec4fbb45c9016efd5f4d7f24af8e5d93296e935631d8/threadpoolctl-3.6.0.tar.gz", hash = "sha256:8ab8b4aa3491d812b623328249fab5302a68d2d71745c8a4c719a2fcaba9f44e", size = 21274, upload-time = "2025-03-13T13:49:23.031Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/32/d5/f9a850d79b0851d1d4ef6456097579a9005b31fea68726a4ae5f2d82ddd9/threadpoolctl-3.6.0-py3-none-any.whl", hash = "sha256:43a0b8fd5a2928500110039e43a5eed8480b918967083ea48dc3ab9f13c4a7fb", size = 18638, upload-time = "2025-03-13T13:49:21.846Z" },
]

[[package]]
name = "tifffile"
version = "2025.6.11"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "numpy", marker = "sys_platform == 'darwin' or sys_platform == 'linux'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/11/9e/636e3e433c24da41dd639e0520db60750dbf5e938d023b83af8097382ea3/tifffile-2025.6.11.tar.gz", hash = "sha256:0ece4c2e7a10656957d568a093b07513c0728d30c1bd8cc12725901fffdb7143", size = 370125, upload-time = "2025-06-12T04:49:38.839Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3a/d8/1ba8f32bfc9cb69e37edeca93738e883f478fbe84ae401f72c0d8d507841/tifffile-2025.6.11-py3-none-any.whl", hash = "sha256:32effb78b10b3a283eb92d4ebf844ae7e93e151458b0412f38518b4e6d2d7542", size = 230800, upload-time = "2025-06-12T04:49:37.458Z" },
]

[[package]]
name = "tiktoken"
version = "0.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "regex" },
    { name = "requests" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c4/4a/abaec53e93e3ef37224a4dd9e2fc6bb871e7a538c2b6b9d2a6397271daf4/tiktoken-0.7.0.tar.gz", hash = "sha256:1077266e949c24e0291f6c350433c6f0971365ece2b173a23bc3b9f9defef6b6", size = 33437, upload-time = "2024-05-13T18:03:28.793Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1d/46/4cdda4186ce900608f522da34acf442363346688c71b938a90a52d7b84cc/tiktoken-0.7.0-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:71c55d066388c55a9c00f61d2c456a6086673ab7dec22dd739c23f77195b1908", size = 960446, upload-time = "2024-05-13T18:02:54.409Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b6/30/09ced367d280072d7a3e21f34263dfbbf6378661e7a0f6414e7c18971083/tiktoken-0.7.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:09ed925bccaa8043e34c519fbb2f99110bd07c6fd67714793c21ac298e449410", size = 906652, upload-time = "2024-05-13T18:02:56.25Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e6/7b/c949e4954441a879a67626963dff69096e3c774758b9f2bb0853f7b4e1e7/tiktoken-0.7.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:03c6c40ff1db0f48a7b4d2dafeae73a5607aacb472fa11f125e7baf9dce73704", size = 1047904, upload-time = "2024-05-13T18:02:57.707Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/50/81/1842a22f15586072280364c2ab1e40835adaf64e42fe80e52aff921ee021/tiktoken-0.7.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d20b5c6af30e621b4aca094ee61777a44118f52d886dbe4f02b70dfe05c15350", size = 1079836, upload-time = "2024-05-13T18:02:59.009Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/87/51a133a3d5307cf7ae3754249b0faaa91d3414b85c3d36f80b54d6817aa6/tiktoken-0.7.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:d427614c3e074004efa2f2411e16c826f9df427d3c70a54725cae860f09e4bf4", size = 1092472, upload-time = "2024-05-13T18:03:00.597Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a5/1f/c93517dc6d3b2c9e988b8e24f87a8b2d4a4ab28920a3a3f3ea338397ae0c/tiktoken-0.7.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:8c46d7af7b8c6987fac9b9f61041b452afe92eb087d29c9ce54951280f899a97", size = 1141881, upload-time = "2024-05-13T18:03:02.743Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bf/4b/48ca098cb580c099b5058bf62c4cb5e90ca6130fa43ef4df27088536245b/tiktoken-0.7.0-cp312-cp312-win_amd64.whl", hash = "sha256:0bc603c30b9e371e7c4c7935aba02af5994a909fc3c0fe66e7004070858d3f8f", size = 799281, upload-time = "2024-05-13T18:03:04.036Z" },
]

[[package]]
name = "tinycss2"
version = "1.4.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "webencodings" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7a/fd/7a5ee21fd08ff70d3d33a5781c255cbe779659bd03278feb98b19ee550f4/tinycss2-1.4.0.tar.gz", hash = "sha256:10c0972f6fc0fbee87c3edb76549357415e94548c1ae10ebccdea16fb404a9b7", size = 87085, upload-time = "2024-10-24T14:58:29.895Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e6/34/ebdc18bae6aa14fbee1a08b63c015c72b64868ff7dae68808ab500c492e2/tinycss2-1.4.0-py3-none-any.whl", hash = "sha256:3a49cf47b7675da0b15d0c6e1df8df4ebd96e9394bb905a5775adb0d884c5289", size = 26610, upload-time = "2024-10-24T14:58:28.029Z" },
]

[[package]]
name = "tomlkit"
version = "0.13.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cc/18/0bbf3884e9eaa38819ebe46a7bd25dcd56b67434402b66a58c4b8e552575/tomlkit-0.13.3.tar.gz", hash = "sha256:430cf247ee57df2b94ee3fbe588e71d362a941ebb545dec29b53961d61add2a1", size = 185207, upload-time = "2025-06-05T07:13:44.947Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bd/75/8539d011f6be8e29f339c42e633aae3cb73bffa95dd0f9adec09b9c58e85/tomlkit-0.13.3-py3-none-any.whl", hash = "sha256:c89c649d79ee40629a9fda55f8ace8c6a1b42deb912b2a8fd8d942ddadb606b0", size = 38901, upload-time = "2025-06-05T07:13:43.546Z" },
]

[[package]]
name = "tornado"
version = "6.4.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/59/45/a0daf161f7d6f36c3ea5fc0c2de619746cc3dd4c76402e9db545bd920f63/tornado-6.4.2.tar.gz", hash = "sha256:92bad5b4746e9879fd7bf1eb21dce4e3fc5128d71601f80005afa39237ad620b", size = 501135, upload-time = "2024-11-22T03:06:38.036Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/7e/71f604d8cea1b58f82ba3590290b66da1e72d840aeb37e0d5f7291bd30db/tornado-6.4.2-cp38-abi3-macosx_10_9_universal2.whl", hash = "sha256:e828cce1123e9e44ae2a50a9de3055497ab1d0aeb440c5ac23064d9e44880da1", size = 436299, upload-time = "2024-11-22T03:06:20.162Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/96/44/87543a3b99016d0bf54fdaab30d24bf0af2e848f1d13d34a3a5380aabe16/tornado-6.4.2-cp38-abi3-macosx_10_9_x86_64.whl", hash = "sha256:072ce12ada169c5b00b7d92a99ba089447ccc993ea2143c9ede887e0937aa803", size = 434253, upload-time = "2024-11-22T03:06:22.39Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cb/fb/fdf679b4ce51bcb7210801ef4f11fdac96e9885daa402861751353beea6e/tornado-6.4.2-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1a017d239bd1bb0919f72af256a970624241f070496635784d9bf0db640d3fec", size = 437602, upload-time = "2024-11-22T03:06:24.214Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4f/3b/e31aeffffc22b475a64dbeb273026a21b5b566f74dee48742817626c47dc/tornado-6.4.2-cp38-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c36e62ce8f63409301537222faffcef7dfc5284f27eec227389f2ad11b09d946", size = 436972, upload-time = "2024-11-22T03:06:25.559Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/22/55/b78a464de78051a30599ceb6983b01d8f732e6f69bf37b4ed07f642ac0fc/tornado-6.4.2-cp38-abi3-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bca9eb02196e789c9cb5c3c7c0f04fb447dc2adffd95265b2c7223a8a615ccbf", size = 437173, upload-time = "2024-11-22T03:06:27.584Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/79/5e/be4fb0d1684eb822c9a62fb18a3e44a06188f78aa466b2ad991d2ee31104/tornado-6.4.2-cp38-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:304463bd0772442ff4d0f5149c6f1c2135a1fae045adf070821c6cdc76980634", size = 437892, upload-time = "2024-11-22T03:06:28.933Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f5/33/4f91fdd94ea36e1d796147003b490fe60a0215ac5737b6f9c65e160d4fe0/tornado-6.4.2-cp38-abi3-musllinux_1_2_i686.whl", hash = "sha256:c82c46813ba483a385ab2a99caeaedf92585a1f90defb5693351fa7e4ea0bf73", size = 437334, upload-time = "2024-11-22T03:06:30.428Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2b/ae/c1b22d4524b0e10da2f29a176fb2890386f7bd1f63aacf186444873a88a0/tornado-6.4.2-cp38-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:932d195ca9015956fa502c6b56af9eb06106140d844a335590c1ec7f5277d10c", size = 437261, upload-time = "2024-11-22T03:06:32.458Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b5/25/36dbd49ab6d179bcfc4c6c093a51795a4f3bed380543a8242ac3517a1751/tornado-6.4.2-cp38-abi3-win32.whl", hash = "sha256:2876cef82e6c5978fde1e0d5b1f919d756968d5b4282418f3146b79b58556482", size = 438463, upload-time = "2024-11-22T03:06:34.71Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/61/cc/58b1adeb1bb46228442081e746fcdbc4540905c87e8add7c277540934edb/tornado-6.4.2-cp38-abi3-win_amd64.whl", hash = "sha256:908b71bf3ff37d81073356a5fadcc660eb10c1476ee6e2725588626ce7e5ca38", size = 438907, upload-time = "2024-11-22T03:06:36.71Z" },
]

[[package]]
name = "tqdm"
version = "4.67.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/4b/29b4ef32e036bb34e4ab51796dd745cdba7ed47ad142a9f4a1eb8e0c744d/tqdm-4.67.1.tar.gz", hash = "sha256:f8aef9c52c08c13a65f30ea34f4e5aac3fd1a34959879d7e59e63027286627f2", size = 169737, upload-time = "2024-11-24T20:12:22.481Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl", hash = "sha256:26445eca388f82e72884e0d580d5464cd801a3ea01e63e5601bdff9ba6a48de2", size = 78540, upload-time = "2024-11-24T20:12:19.698Z" },
]

[[package]]
name = "traceback-with-variables"
version = "2.0.4"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b3/a5/acfd3807d01a66ae687a2a4d20f86cb16be6de323f713fe8d679ecd5cd5a/traceback-with-variables-2.0.4.tar.gz", hash = "sha256:a30c79b7c206fb255967f3f5d125ce114b3dae4fd92d268f901dd35ee23f9cc0", size = 21445, upload-time = "2021-12-05T17:42:02.487Z" }

[[package]]
name = "typing-extensions"
version = "4.14.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/5a/da40306b885cc8c09109dc2e1abd358d5684b1425678151cdaed4731c822/typing_extensions-4.14.1.tar.gz", hash = "sha256:38b39f4aeeab64884ce9f74c94263ef78f3c22467c8724005483154c26648d36", size = 107673, upload-time = "2025-07-04T13:28:34.16Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b5/00/d631e67a838026495268c2f6884f3711a15a9a2a96cd244fdaea53b823fb/typing_extensions-4.14.1-py3-none-any.whl", hash = "sha256:d1e1e3b58374dc93031d6eda2420a48ea44a36c2b4766a4fdeb3710755731d76", size = 43906, upload-time = "2025-07-04T13:28:32.743Z" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/95/32/1a225d6164441be760d75c2c42e2780dc0873fe382da3e98a2e1e48361e5/tzdata-2025.2.tar.gz", hash = "sha256:b60a638fcc0daffadf82fe0f57e53d06bdec2f36c4df66280ae79bce6bd6f2b9", size = 196380, upload-time = "2025-03-23T13:54:43.652Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5c/23/c7abc0ca0a1526a0774eca151daeb8de62ec457e77262b66b359c3c7679e/tzdata-2025.2-py2.py3-none-any.whl", hash = "sha256:1a403fada01ff9221ca8044d701868fa132215d84beb92242d9acd2147f667a8", size = 347839, upload-time = "2025-03-23T13:54:41.845Z" },
]

[[package]]
name = "tzlocal"
version = "5.3.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "tzdata", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/2e/c14812d3d4d9cd1773c6be938f89e5735a1f11a9f184ac3639b93cef35d5/tzlocal-5.3.1.tar.gz", hash = "sha256:cceffc7edecefea1f595541dbd6e990cb1ea3d19bf01b2809f362a03dd7921fd", size = 30761, upload-time = "2025-03-05T21:17:41.549Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c2/14/e2a54fabd4f08cd7af1c07030603c3356b74da07f7cc056e600436edfa17/tzlocal-5.3.1-py3-none-any.whl", hash = "sha256:eb1a66c3ef5847adf7a834f1be0800581b683b5608e74f86ecbcef8ab91bb85d", size = 18026, upload-time = "2025-03-05T21:17:39.857Z" },
]

[[package]]
name = "ua-parser"
version = "1.0.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "ua-parser-builtins" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/70/0e/ed98be735bc89d5040e0c60f5620d0b8c04e9e7da99ed1459e8050e90a77/ua_parser-1.0.1.tar.gz", hash = "sha256:f9d92bf19d4329019cef91707aecc23c6d65143ad7e29a233f0580fb0d15547d", size = 728106, upload-time = "2025-02-01T14:13:32.508Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/94/37/be6dfbfa45719aa82c008fb4772cfe5c46db765a2ca4b6f524a1fdfee4d7/ua_parser-1.0.1-py3-none-any.whl", hash = "sha256:b059f2cb0935addea7e551251cbbf42e9a8872f86134163bc1a4f79e0945ffea", size = 31410, upload-time = "2025-02-01T14:13:28.458Z" },
]

[[package]]
name = "ua-parser-builtins"
version = "0.18.0.post1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6f/d3/13adff37f15489c784cc7669c35a6c3bf94b87540229eedf52ef2a1d0175/ua_parser_builtins-0.18.0.post1-py3-none-any.whl", hash = "sha256:eb4f93504040c3a990a6b0742a2afd540d87d7f9f05fd66e94c101db1564674d", size = 86077, upload-time = "2024-12-05T18:44:36.732Z" },
]

[[package]]
name = "urllib3"
version = "2.5.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/15/22/9ee70a2574a4f4599c47dd506532914ce044817c7752a79b6a51286319bc/urllib3-2.5.0.tar.gz", hash = "sha256:3fc47733c7e419d4bc3f6b3dc2b4f890bb743906a30d56ba4a5bfa4bbff92760", size = 393185, upload-time = "2025-06-18T14:07:41.644Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl", hash = "sha256:e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc", size = 129795, upload-time = "2025-06-18T14:07:40.39Z" },
]

[[package]]
name = "user-agents"
version = "2.2.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "ua-parser" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e3/e1/63c5bfb485a945010c8cbc7a52f85573561737648d36b30394248730a7bc/user-agents-2.2.0.tar.gz", hash = "sha256:d36d25178db65308d1458c5fa4ab39c9b2619377010130329f3955e7626ead26", size = 9525, upload-time = "2020-08-23T06:01:56.382Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8f/1c/20bb3d7b2bad56d881e3704131ddedbb16eb787101306887dff349064662/user_agents-2.2.0-py3-none-any.whl", hash = "sha256:a98c4dc72ecbc64812c4534108806fb0a0b3a11ec3fd1eafe807cee5b0a942e7", size = 9614, upload-time = "2020-08-23T06:01:54.047Z" },
]

[[package]]
name = "utensils"
version = "0.1.82"
source = { registry = "http://**********:3141/cheftin/pypi" }
dependencies = [
    { name = "aiofiles" },
    { name = "chardet" },
    { name = "cython" },
    { name = "gmssl-python" },
    { name = "more-itertools" },
    { name = "py7zr" },
    { name = "pycryptodome" },
    { name = "python-dotenv" },
    { name = "pyyaml" },
    { name = "rarfile" },
]
sdist = { url = "http://**********:3141/cheftin/pypi/+f/edf/e5e4cfbe2d018/utensils-0.1.82.tar.gz", hash = "sha256:edfe5e4cfbe2d018bda9546df1e06240e6249b2b208f1fc9d9caf5776a12329c" }
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/739/f7d5472226e83/utensils-0.1.82-cp312-cp312-macosx_10_14_x86_64.whl", hash = "sha256:739f7d5472226e833e7d3ed4d7f21df77e16a97fc2aa1ca6173dbd6b50de3175" },
    { url = "http://**********:3141/cheftin/pypi/+f/b0a/b2abf6de81444/utensils-0.1.82-cp312-cp312-manylinux_2_27_aarch64.whl", hash = "sha256:b0ab2abf6de8144475fdf4342be4b9812cfa0b22db4a8ac7ee3bf241848d21ac" },
    { url = "http://**********:3141/cheftin/pypi/+f/de3/97ebea03829da/utensils-0.1.82-cp312-cp312-manylinux_2_27_x86_64.whl", hash = "sha256:de397ebea03829da4d8268f0efb58903d4f956932f2d74d3f6826875b0017ed1" },
]

[[package]]
name = "uvicorn"
version = "0.34.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "click" },
    { name = "h11" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/de/ad/713be230bcda622eaa35c28f0d328c3675c371238470abdea52417f17a8e/uvicorn-0.34.3.tar.gz", hash = "sha256:35919a9a979d7a59334b6b10e05d77c1d0d574c50e0fc98b8b1a0f165708b55a", size = 76631, upload-time = "2025-06-01T07:48:17.531Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6d/0d/8adfeaa62945f90d19ddc461c55f4a50c258af7662d34b6a3d5d1f8646f6/uvicorn-0.34.3-py3-none-any.whl", hash = "sha256:16246631db62bdfbf069b0645177d6e8a77ba950cfedbfd093acef9444e4d885", size = 62431, upload-time = "2025-06-01T07:48:15.664Z" },
]

[package.optional-dependencies]
standard = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "httptools" },
    { name = "python-dotenv" },
    { name = "pyyaml" },
    { name = "uvloop", marker = "platform_python_implementation != 'PyPy' and sys_platform != 'cygwin' and sys_platform != 'win32'" },
    { name = "watchfiles" },
    { name = "websockets" },
]

[[package]]
name = "uvicorn-worker"
version = "0.3.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "gunicorn" },
    { name = "uvicorn" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/37/c0/b5df8c9a31b0516a47703a669902b362ca1e569fed4f3daa1d4299b28be0/uvicorn_worker-0.3.0.tar.gz", hash = "sha256:6baeab7b2162ea6b9612cbe149aa670a76090ad65a267ce8e27316ed13c7de7b", size = 9181, upload-time = "2024-12-26T12:13:07.591Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f7/1f/4e5f8770c2cf4faa2c3ed3c19f9d4485ac9db0a6b029a7866921709bdc6c/uvicorn_worker-0.3.0-py3-none-any.whl", hash = "sha256:ef0fe8aad27b0290a9e602a256b03f5a5da3a9e5f942414ca587b645ec77dd52", size = 5346, upload-time = "2024-12-26T12:13:06.026Z" },
]

[[package]]
name = "uvloop"
version = "0.21.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/af/c0/854216d09d33c543f12a44b393c402e89a920b1a0a7dc634c42de91b9cf6/uvloop-0.21.0.tar.gz", hash = "sha256:3bf12b0fda68447806a7ad847bfa591613177275d35b6724b1ee573faa3704e3", size = 2492741, upload-time = "2024-10-14T23:38:35.489Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8c/4c/03f93178830dc7ce8b4cdee1d36770d2f5ebb6f3d37d354e061eefc73545/uvloop-0.21.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:359ec2c888397b9e592a889c4d72ba3d6befba8b2bb01743f72fffbde663b59c", size = 1471284, upload-time = "2024-10-14T23:37:47.833Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/43/3e/92c03f4d05e50f09251bd8b2b2b584a2a7f8fe600008bcc4523337abe676/uvloop-0.21.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:f7089d2dc73179ce5ac255bdf37c236a9f914b264825fdaacaded6990a7fb4c2", size = 821349, upload-time = "2024-10-14T23:37:50.149Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a6/ef/a02ec5da49909dbbfb1fd205a9a1ac4e88ea92dcae885e7c961847cd51e2/uvloop-0.21.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:baa4dcdbd9ae0a372f2167a207cd98c9f9a1ea1188a8a526431eef2f8116cc8d", size = 4580089, upload-time = "2024-10-14T23:37:51.703Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/06/a7/b4e6a19925c900be9f98bec0a75e6e8f79bb53bdeb891916609ab3958967/uvloop-0.21.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:86975dca1c773a2c9864f4c52c5a55631038e387b47eaf56210f873887b6c8dc", size = 4693770, upload-time = "2024-10-14T23:37:54.122Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ce/0c/f07435a18a4b94ce6bd0677d8319cd3de61f3a9eeb1e5f8ab4e8b5edfcb3/uvloop-0.21.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:461d9ae6660fbbafedd07559c6a2e57cd553b34b0065b6550685f6653a98c1cb", size = 4451321, upload-time = "2024-10-14T23:37:55.766Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8f/eb/f7032be105877bcf924709c97b1bf3b90255b4ec251f9340cef912559f28/uvloop-0.21.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:183aef7c8730e54c9a3ee3227464daed66e37ba13040bb3f350bc2ddc040f22f", size = 4659022, upload-time = "2024-10-14T23:37:58.195Z" },
]

[[package]]
name = "vine"
version = "5.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/bd/e4/d07b5f29d283596b9727dd5275ccbceb63c44a1a82aa9e4bfd20426762ac/vine-5.1.0.tar.gz", hash = "sha256:8b62e981d35c41049211cf62a0a1242d8c1ee9bd15bb196ce38aefd6799e61e0", size = 48980, upload-time = "2023-11-05T08:46:53.857Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/03/ff/7c0c86c43b3cbb927e0ccc0255cb4057ceba4799cd44ae95174ce8e8b5b2/vine-5.1.0-py3-none-any.whl", hash = "sha256:40fdf3c48b2cfe1c38a49e9ae2da6fda88e4794c810050a728bd7413811fb1dc", size = 9636, upload-time = "2023-11-05T08:46:51.205Z" },
]

[[package]]
name = "virtualenv"
version = "20.33.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "distlib" },
    { name = "filelock" },
    { name = "platformdirs" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8b/60/4f20960df6c7b363a18a55ab034c8f2bcd5d9770d1f94f9370ec104c1855/virtualenv-20.33.1.tar.gz", hash = "sha256:1b44478d9e261b3fb8baa5e74a0ca3bc0e05f21aa36167bf9cbf850e542765b8", size = 6082160, upload-time = "2025-08-05T16:10:55.605Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ca/ff/ded57ac5ff40a09e6e198550bab075d780941e0b0f83cbeabd087c59383a/virtualenv-20.33.1-py3-none-any.whl", hash = "sha256:07c19bc66c11acab6a5958b815cbcee30891cd1c2ccf53785a28651a0d8d8a67", size = 6060362, upload-time = "2025-08-05T16:10:52.81Z" },
]

[[package]]
name = "wand"
version = "0.6.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/34/91/3ddd62f9ad04653bf410903a0ac536ead83f6ce30734d3ec46cbbba0da8d/Wand-0.6.5.tar.gz", hash = "sha256:ec981b4f07f7582fc564aba8b57763a549392e9ef8b6a338e9da54cdd229cf95" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/08/096b76e9211ca5ef338791100b76375555cb4082a53496b1c1d5897ee13c/Wand-0.6.5-py2.py3-none-any.whl", hash = "sha256:473af4a143d2c87693cc43dbd6f64d91ccfd7f0506cf4da53851cd34f114b39d" },
]

[[package]]
name = "watchfiles"
version = "1.1.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "anyio" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/9a/d451fcc97d029f5812e898fd30a53fd8c15c7bbd058fd75cfc6beb9bd761/watchfiles-1.1.0.tar.gz", hash = "sha256:693ed7ec72cbfcee399e92c895362b6e66d63dac6b91e2c11ae03d10d503e575", size = 94406, upload-time = "2025-06-15T19:06:59.42Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f6/b8/858957045a38a4079203a33aaa7d23ea9269ca7761c8a074af3524fbb240/watchfiles-1.1.0-cp312-cp312-macosx_10_12_x86_64.whl", hash = "sha256:9dc001c3e10de4725c749d4c2f2bdc6ae24de5a88a339c4bce32300a31ede179", size = 402339, upload-time = "2025-06-15T19:05:24.516Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/80/28/98b222cca751ba68e88521fabd79a4fab64005fc5976ea49b53fa205d1fa/watchfiles-1.1.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:d9ba68ec283153dead62cbe81872d28e053745f12335d037de9cbd14bd1877f5", size = 394409, upload-time = "2025-06-15T19:05:25.469Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/86/50/dee79968566c03190677c26f7f47960aff738d32087087bdf63a5473e7df/watchfiles-1.1.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:130fc497b8ee68dce163e4254d9b0356411d1490e868bd8790028bc46c5cc297", size = 450939, upload-time = "2025-06-15T19:05:26.494Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/40/45/a7b56fb129700f3cfe2594a01aa38d033b92a33dddce86c8dfdfc1247b72/watchfiles-1.1.0-cp312-cp312-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:50a51a90610d0845a5931a780d8e51d7bd7f309ebc25132ba975aca016b576a0", size = 457270, upload-time = "2025-06-15T19:05:27.466Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/b5/c8/fa5ef9476b1d02dc6b5e258f515fcaaecf559037edf8b6feffcbc097c4b8/watchfiles-1.1.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:dc44678a72ac0910bac46fa6a0de6af9ba1355669b3dfaf1ce5f05ca7a74364e", size = 483370, upload-time = "2025-06-15T19:05:28.548Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/98/68/42cfcdd6533ec94f0a7aab83f759ec11280f70b11bfba0b0f885e298f9bd/watchfiles-1.1.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:a543492513a93b001975ae283a51f4b67973662a375a403ae82f420d2c7205ee", size = 598654, upload-time = "2025-06-15T19:05:29.997Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/d3/74/b2a1544224118cc28df7e59008a929e711f9c68ce7d554e171b2dc531352/watchfiles-1.1.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:8ac164e20d17cc285f2b94dc31c384bc3aa3dd5e7490473b3db043dd70fbccfd", size = 478667, upload-time = "2025-06-15T19:05:31.172Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/8c/77/e3362fe308358dc9f8588102481e599c83e1b91c2ae843780a7ded939a35/watchfiles-1.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f7590d5a455321e53857892ab8879dce62d1f4b04748769f5adf2e707afb9d4f", size = 452213, upload-time = "2025-06-15T19:05:32.299Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6e/17/c8f1a36540c9a1558d4faf08e909399e8133599fa359bf52ec8fcee5be6f/watchfiles-1.1.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:37d3d3f7defb13f62ece99e9be912afe9dd8a0077b7c45ee5a57c74811d581a4", size = 626718, upload-time = "2025-06-15T19:05:33.415Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/45/fb599be38b4bd38032643783d7496a26a6f9ae05dea1a42e58229a20ac13/watchfiles-1.1.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:7080c4bb3efd70a07b1cc2df99a7aa51d98685be56be6038c3169199d0a1c69f", size = 623098, upload-time = "2025-06-15T19:05:34.534Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a1/e7/fdf40e038475498e160cd167333c946e45d8563ae4dd65caf757e9ffe6b4/watchfiles-1.1.0-cp312-cp312-win32.whl", hash = "sha256:cbcf8630ef4afb05dc30107bfa17f16c0896bb30ee48fc24bf64c1f970f3b1fd", size = 279209, upload-time = "2025-06-15T19:05:35.577Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3f/d3/3ae9d5124ec75143bdf088d436cba39812122edc47709cd2caafeac3266f/watchfiles-1.1.0-cp312-cp312-win_amd64.whl", hash = "sha256:cbd949bdd87567b0ad183d7676feb98136cde5bb9025403794a4c0db28ed3a47", size = 292786, upload-time = "2025-06-15T19:05:36.559Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/2f/7dd4fc8b5f2b34b545e19629b4a018bfb1de23b3a496766a2c1165ca890d/watchfiles-1.1.0-cp312-cp312-win_arm64.whl", hash = "sha256:0a7d40b77f07be87c6faa93d0951a0fcd8cbca1ddff60a1b65d741bac6f3a9f6", size = 284343, upload-time = "2025-06-15T19:05:37.5Z" },
]

[[package]]
name = "wcwidth"
version = "0.2.13"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/6c/63/53559446a878410fc5a5974feb13d31d78d752eb18aeba59c7fef1af7598/wcwidth-0.2.13.tar.gz", hash = "sha256:72ea0c06399eb286d978fdedb6923a9eb47e1c486ce63e9b4e64fc18303972b5", size = 101301, upload-time = "2024-01-06T02:10:57.829Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fd/84/fd2ba7aafacbad3c4201d395674fc6348826569da3c0937e75505ead3528/wcwidth-0.2.13-py2.py3-none-any.whl", hash = "sha256:3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859", size = 34166, upload-time = "2024-01-06T02:10:55.763Z" },
]

[[package]]
name = "weasyprint"
version = "62.3"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cffi" },
    { name = "cssselect2" },
    { name = "fonttools", extra = ["woff"] },
    { name = "html5lib" },
    { name = "pillow" },
    { name = "pydyf" },
    { name = "pyphen" },
    { name = "tinycss2" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fd/22/2d76310cd2ea5bbf03c691a08d48626f49853b7261a51bbdc0f834d746ca/weasyprint-62.3.tar.gz", hash = "sha256:8d8680d732f7fa0fcbc587692a5a5cb095c3525627066918d6e203cbf42b7fcd", size = 477181, upload-time = "2024-06-21T15:48:34.51Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1c/d1/10f4225bec3450e7c0aa5813697ce76eb2dfa1f3c5e530510253b6b276b2/weasyprint-62.3-py3-none-any.whl", hash = "sha256:d31048646ce15084e135b33e334a61f526aa68d2f679fcc109ed0e0f5edaed21", size = 289333, upload-time = "2024-06-21T15:48:30.932Z" },
]

[[package]]
name = "webargs"
version = "8.7.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "marshmallow" },
    { name = "packaging" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a8/02/d27619cb81e0e136e27276e8bd48c5ebb38272b0920ce43da0f61c598879/webargs-8.7.0.tar.gz", hash = "sha256:0c617dec19ed4f1ff6b247cd73855e949d87052d71900938b71f0cafd92f191b", size = 96803, upload-time = "2025-04-18T20:56:55.591Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/3f/0f68665037bc10d87cf18bcd094a53ad6496ffe44faff721481060fd9149/webargs-8.7.0-py3-none-any.whl", hash = "sha256:4571de9ff5aac98ef528d9cecd7dbc0e05c0e9149e8293a01d1d1398abfcf780", size = 31795, upload-time = "2025-04-18T20:56:53.786Z" },
]

[[package]]
name = "webencodings"
version = "0.5.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0b/02/ae6ceac1baeda530866a85075641cec12989bd8d31af6d5ab4a3e8c92f47/webencodings-0.5.1.tar.gz", hash = "sha256:b36a1c245f2d304965eb4e0a82848379241dc04b865afcc4aab16748587e1923", size = 9721, upload-time = "2017-04-05T20:21:34.189Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f4/24/2a3e3df732393fed8b3ebf2ec078f05546de641fe1b667ee316ec1dcf3b7/webencodings-0.5.1-py2.py3-none-any.whl", hash = "sha256:a0af1213f3c2226497a97e2b3aa01a7e4bee4f403f95be16fc9acd2947514a78", size = 11774, upload-time = "2017-04-05T20:21:32.581Z" },
]

[[package]]
name = "websocket-client"
version = "1.8.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e6/30/fba0d96b4b5fbf5948ed3f4681f7da2f9f64512e1d303f94b4cc174c24a5/websocket_client-1.8.0.tar.gz", hash = "sha256:3239df9f44da632f96012472805d40a23281a991027ce11d2f45a6f24ac4c3da", size = 54648, upload-time = "2024-04-23T22:16:16.976Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5a/84/44687a29792a70e111c5c477230a72c4b957d88d16141199bf9acb7537a3/websocket_client-1.8.0-py3-none-any.whl", hash = "sha256:17b44cc997f5c498e809b22cdf2d9c7a9e71c02c8cc2b6c56e7c2d1239bfa526", size = 58826, upload-time = "2024-04-23T22:16:14.422Z" },
]

[[package]]
name = "websockets"
version = "15.0.1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/21/e6/26d09fab466b7ca9c7737474c52be4f76a40301b08362eb2dbc19dcc16c1/websockets-15.0.1.tar.gz", hash = "sha256:82544de02076bafba038ce055ee6412d68da13ab47f0c60cab827346de828dee", size = 177016, upload-time = "2025-03-05T20:03:41.606Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/51/6b/4545a0d843594f5d0771e86463606a3988b5a09ca5123136f8a76580dd63/websockets-15.0.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:3e90baa811a5d73f3ca0bcbf32064d663ed81318ab225ee4f427ad4e26e5aff3", size = 175437, upload-time = "2025-03-05T20:02:16.706Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f4/71/809a0f5f6a06522af902e0f2ea2757f71ead94610010cf570ab5c98e99ed/websockets-15.0.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:592f1a9fe869c778694f0aa806ba0374e97648ab57936f092fd9d87f8bc03665", size = 173096, upload-time = "2025-03-05T20:02:18.832Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3d/69/1a681dd6f02180916f116894181eab8b2e25b31e484c5d0eae637ec01f7c/websockets-15.0.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:0701bc3cfcb9164d04a14b149fd74be7347a530ad3bbf15ab2c678a2cd3dd9a2", size = 173332, upload-time = "2025-03-05T20:02:20.187Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a6/02/0073b3952f5bce97eafbb35757f8d0d54812b6174ed8dd952aa08429bcc3/websockets-15.0.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:e8b56bdcdb4505c8078cb6c7157d9811a85790f2f2b3632c7d1462ab5783d215", size = 183152, upload-time = "2025-03-05T20:02:22.286Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/74/45/c205c8480eafd114b428284840da0b1be9ffd0e4f87338dc95dc6ff961a1/websockets-15.0.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0af68c55afbd5f07986df82831c7bff04846928ea8d1fd7f30052638788bc9b5", size = 182096, upload-time = "2025-03-05T20:02:24.368Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/14/8f/aa61f528fba38578ec553c145857a181384c72b98156f858ca5c8e82d9d3/websockets-15.0.1-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:64dee438fed052b52e4f98f76c5790513235efaa1ef7f3f2192c392cd7c91b65", size = 182523, upload-time = "2025-03-05T20:02:25.669Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ec/6d/0267396610add5bc0d0d3e77f546d4cd287200804fe02323797de77dbce9/websockets-15.0.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:d5f6b181bb38171a8ad1d6aa58a67a6aa9d4b38d0f8c5f496b9e42561dfc62fe", size = 182790, upload-time = "2025-03-05T20:02:26.99Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/02/05/c68c5adbf679cf610ae2f74a9b871ae84564462955d991178f95a1ddb7dd/websockets-15.0.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:5d54b09eba2bada6011aea5375542a157637b91029687eb4fdb2dab11059c1b4", size = 182165, upload-time = "2025-03-05T20:02:30.291Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/29/93/bb672df7b2f5faac89761cb5fa34f5cec45a4026c383a4b5761c6cea5c16/websockets-15.0.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:3be571a8b5afed347da347bfcf27ba12b069d9d7f42cb8c7028b5e98bbb12597", size = 182160, upload-time = "2025-03-05T20:02:31.634Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ff/83/de1f7709376dc3ca9b7eeb4b9a07b4526b14876b6d372a4dc62312bebee0/websockets-15.0.1-cp312-cp312-win32.whl", hash = "sha256:c338ffa0520bdb12fbc527265235639fb76e7bc7faafbb93f6ba80d9c06578a9", size = 176395, upload-time = "2025-03-05T20:02:33.017Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7d/71/abf2ebc3bbfa40f391ce1428c7168fb20582d0ff57019b69ea20fa698043/websockets-15.0.1-cp312-cp312-win_amd64.whl", hash = "sha256:fcd5cf9e305d7b8338754470cf69cf81f420459dbae8a3b40cee57417f4614a7", size = 176841, upload-time = "2025-03-05T20:02:34.498Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fa/a8/5b41e0da817d64113292ab1f8247140aac61cbf6cfd085d6a0fa77f4984f/websockets-15.0.1-py3-none-any.whl", hash = "sha256:f7a866fbc1e97b5c617ee4116daaa09b722101d4a3c170c787450ba409f9736f", size = 169743, upload-time = "2025-03-05T20:03:39.41Z" },
]

[[package]]
name = "wordinsight"
version = "0.0.88"
source = { registry = "http://**********:3141/cheftin/pypi" }
wheels = [
    { url = "http://**********:3141/cheftin/pypi/+f/8b0/e60fa8753f003/wordinsight-0.0.88-py3-none-any.whl", hash = "sha256:8b0e60fa8753f00398c5459b08a73da3dc15841baf959468c6f4b4d4fb945d45" },
]

[[package]]
name = "wrapt"
version = "1.17.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c3/fc/e91cc220803d7bc4db93fb02facd8461c37364151b8494762cc88b0fbcef/wrapt-1.17.2.tar.gz", hash = "sha256:41388e9d4d1522446fe79d3213196bd9e3b301a336965b9e27ca2788ebd122f3", size = 55531, upload-time = "2025-01-14T10:35:45.465Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a1/bd/ab55f849fd1f9a58ed7ea47f5559ff09741b25f00c191231f9f059c83949/wrapt-1.17.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d5e2439eecc762cd85e7bd37161d4714aa03a33c5ba884e26c81559817ca0925", size = 53799, upload-time = "2025-01-14T10:33:57.4Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/53/18/75ddc64c3f63988f5a1d7e10fb204ffe5762bc663f8023f18ecaf31a332e/wrapt-1.17.2-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:3fc7cb4c1c744f8c05cd5f9438a3caa6ab94ce8344e952d7c45a8ed59dd88392", size = 38821, upload-time = "2025-01-14T10:33:59.334Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/48/2a/97928387d6ed1c1ebbfd4efc4133a0633546bec8481a2dd5ec961313a1c7/wrapt-1.17.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:8fdbdb757d5390f7c675e558fd3186d590973244fab0c5fe63d373ade3e99d40", size = 38919, upload-time = "2025-01-14T10:34:04.093Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/73/54/3bfe5a1febbbccb7a2f77de47b989c0b85ed3a6a41614b104204a788c20e/wrapt-1.17.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5bb1d0dbf99411f3d871deb6faa9aabb9d4e744d67dcaaa05399af89d847a91d", size = 88721, upload-time = "2025-01-14T10:34:07.163Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/25/cb/7262bc1b0300b4b64af50c2720ef958c2c1917525238d661c3e9a2b71b7b/wrapt-1.17.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d18a4865f46b8579d44e4fe1e2bcbc6472ad83d98e22a26c963d46e4c125ef0b", size = 80899, upload-time = "2025-01-14T10:34:09.82Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2a/5a/04cde32b07a7431d4ed0553a76fdb7a61270e78c5fd5a603e190ac389f14/wrapt-1.17.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bc570b5f14a79734437cb7b0500376b6b791153314986074486e0b0fa8d71d98", size = 89222, upload-time = "2025-01-14T10:34:11.258Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/09/28/2e45a4f4771fcfb109e244d5dbe54259e970362a311b67a965555ba65026/wrapt-1.17.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:6d9187b01bebc3875bac9b087948a2bccefe464a7d8f627cf6e48b1bbae30f82", size = 86707, upload-time = "2025-01-14T10:34:12.49Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c6/d2/dcb56bf5f32fcd4bd9aacc77b50a539abdd5b6536872413fd3f428b21bed/wrapt-1.17.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:9e8659775f1adf02eb1e6f109751268e493c73716ca5761f8acb695e52a756ae", size = 79685, upload-time = "2025-01-14T10:34:15.043Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/80/4e/eb8b353e36711347893f502ce91c770b0b0929f8f0bed2670a6856e667a9/wrapt-1.17.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e8b2816ebef96d83657b56306152a93909a83f23994f4b30ad4573b00bd11bb9", size = 87567, upload-time = "2025-01-14T10:34:16.563Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2d/82/f56956041adef78f849db6b289b282e72b55ab8045a75abad81898c28d19/wrapt-1.17.2-py3-none-any.whl", hash = "sha256:b18f2d1533a71f069c7f82d524a52599053d4c7166e9dd374ae2136b7f40f7c8", size = 23594, upload-time = "2025-01-14T10:35:44.018Z" },
]

[[package]]
name = "xlrd"
version = "2.0.2"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/07/5a/377161c2d3538d1990d7af382c79f3b2372e880b65de21b01b1a2b78691e/xlrd-2.0.2.tar.gz", hash = "sha256:08b5e25de58f21ce71dc7db3b3b8106c1fa776f3024c54e45b45b374e89234c9", size = 100167, upload-time = "2025-06-14T08:46:39.039Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/1a/62/c8d562e7766786ba6587d09c5a8ba9f718ed3fa8af7f4553e8f91c36f302/xlrd-2.0.2-py2.py3-none-any.whl", hash = "sha256:ea762c3d29f4cca48d82df517b6d89fbce4db3107f9d78713e48cd321d5c9aa9", size = 96555, upload-time = "2025-06-14T08:46:37.766Z" },
]

[[package]]
name = "xlsxwriter"
version = "3.2.5"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/a7/47/7704bac42ac6fe1710ae099b70e6a1e68ed173ef14792b647808c357da43/xlsxwriter-3.2.5.tar.gz", hash = "sha256:7e88469d607cdc920151c0ab3ce9cf1a83992d4b7bc730c5ffdd1a12115a7dbe", size = 213306, upload-time = "2025-06-17T08:59:14.619Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fa/34/a22e6664211f0c8879521328000bdcae9bf6dbafa94a923e531f6d5b3f73/xlsxwriter-3.2.5-py3-none-any.whl", hash = "sha256:4f4824234e1eaf9d95df9a8fe974585ff91d0f5e3d3f12ace5b71e443c1c6abd", size = 172347, upload-time = "2025-06-17T08:59:13.453Z" },
]

[[package]]
name = "xlutils"
version = "2.0.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "xlrd" },
    { name = "xlwt" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/93/fe/af6d73e4bc7b0ce359d34bebb2e8d4d129763acfecd66a3a7efc587e54c9/xlutils-2.0.0.tar.gz", hash = "sha256:7e0e2c233bd185fecf5e2bd3f4e9469ca4a3bd87da64c82cfe5b2af27e7f9e54", size = 61614, upload-time = "2016-06-09T05:27:51.146Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/c7/55/e22ac73dbb316cabb5db28bef6c87044a95914f713a6e81b593f8a0d2f79/xlutils-2.0.0-py2.py3-none-any.whl", hash = "sha256:b56640862c030e9d53104e7f1d750135fdfabf6bf55e425e5ac40fdee9dbaeb9", size = 55055, upload-time = "2016-06-09T05:27:47.083Z" },
]

[[package]]
name = "xlwt"
version = "1.3.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/06/97/56a6f56ce44578a69343449aa5a0d98eefe04085d69da539f3034e2cd5c1/xlwt-1.3.0.tar.gz", hash = "sha256:c59912717a9b28f1a3c2a98fd60741014b06b043936dcecbc113eaaada156c88", size = 153929, upload-time = "2017-08-22T06:47:16.498Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/44/48/def306413b25c3d01753603b1a222a011b8621aed27cd7f89cbc27e6b0f4/xlwt-1.3.0-py2.py3-none-any.whl", hash = "sha256:a082260524678ba48a297d922cc385f58278b8aa68741596a87de01a9c628b2e", size = 99981, upload-time = "2017-08-22T06:47:15.281Z" },
]

[[package]]
name = "zopfli"
version = "0.2.3.post1"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5e/7c/a8f6696e694709e2abcbccd27d05ef761e9b6efae217e11d977471555b62/zopfli-0.2.3.post1.tar.gz", hash = "sha256:96484dc0f48be1c5d7ae9f38ed1ce41e3675fd506b27c11a6607f14b49101e99", size = 175629, upload-time = "2024-10-18T15:42:05.946Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/3f/ce/b6441cc01881d06e0b5883f32c44e7cc9772e0d04e3e59277f59f80b9a19/zopfli-0.2.3.post1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:3f0197b6aa6eb3086ae9e66d6dd86c4d502b6c68b0ec490496348ae8c05ecaef", size = 295489, upload-time = "2024-10-18T15:40:57.96Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/93/f0/24dd708f00ae0a925bc5c9edae858641c80f6a81a516810dc4d21688a930/zopfli-0.2.3.post1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:5fcfc0dc2761e4fcc15ad5d273b4d58c2e8e059d3214a7390d4d3c8e2aee644e", size = 163010, upload-time = "2024-10-18T15:40:59.444Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/65/57/0378eeeb5e3e1e83b1b0958616b2bf954f102ba5b0755b9747dafbd8cb72/zopfli-0.2.3.post1-cp312-cp312-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:cac2b37ab21c2b36a10b685b1893ebd6b0f83ae26004838ac817680881576567", size = 823649, upload-time = "2024-10-18T15:41:00.642Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ab/8a/3ab8a616d4655acf5cf63c40ca84e434289d7d95518a1a42d28b4a7228f8/zopfli-0.2.3.post1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8d5ab297d660b75c159190ce6d73035502310e40fd35170aed7d1a1aea7ddd65", size = 826557, upload-time = "2024-10-18T15:41:02.431Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ed/4d/7f6820af119c4fec6efaf007bffee7bc9052f695853a711a951be7afd26b/zopfli-0.2.3.post1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9ba214f4f45bec195ee8559651154d3ac2932470b9d91c5715fc29c013349f8c", size = 851127, upload-time = "2024-10-18T15:41:04.259Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e1/db/1ef5353ab06f9f2fb0c25ed0cddf1418fe275cc2ee548bc4a29340c44fe1/zopfli-0.2.3.post1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:c1e0ed5d84ffa2d677cc9582fc01e61dab2e7ef8b8996e055f0a76167b1b94df", size = 1754183, upload-time = "2024-10-18T15:41:05.808Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/39/03/44f8f39950354d330fa798e4bab1ac8e38ec787d3fde25d5b9c7770065a2/zopfli-0.2.3.post1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:bfa1eb759e07d8b7aa7a310a2bc535e127ee70addf90dc8d4b946b593c3e51a8", size = 1905945, upload-time = "2024-10-18T15:41:07.136Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/74/7b/94b920c33cc64255f59e3cfc77c829b5c6e60805d189baeada728854a342/zopfli-0.2.3.post1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:cd2c002f160502608dcc822ed2441a0f4509c52e86fcfd1a09e937278ed1ca14", size = 1835885, upload-time = "2024-10-18T15:41:08.705Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ad/89/c869ac844351e285a6165e2da79b715b0619a122e3160d183805adf8ab45/zopfli-0.2.3.post1-cp312-cp312-win32.whl", hash = "sha256:7be5cc6732eb7b4df17305d8a7b293223f934a31783a874a01164703bc1be6cd", size = 82743, upload-time = "2024-10-18T15:41:10.377Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/29/e6/c98912fd3a589d8a7316c408fd91519f72c237805c4400b753e3942fda0b/zopfli-0.2.3.post1-cp312-cp312-win_amd64.whl", hash = "sha256:4e50ffac74842c1c1018b9b73875a0d0a877c066ab06bf7cccbaa84af97e754f", size = 99403, upload-time = "2024-10-18T15:41:11.547Z" },
]

[[package]]
name = "zstandard"
version = "0.23.0"
source = { registry = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple" }
dependencies = [
    { name = "cffi", marker = "platform_python_implementation == 'PyPy'" },
]
sdist = { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ed/f6/2ac0287b442160a89d726b17a9184a4c615bb5237db763791a7fd16d9df1/zstandard-0.23.0.tar.gz", hash = "sha256:b2d8c62d08e7255f68f7a740bae85b3c9b8e5466baa9cbf7f57f1cde0ac6bc09", size = 681701, upload-time = "2024-07-15T00:18:06.141Z" }
wheels = [
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/7b/83/f23338c963bd9de687d47bf32efe9fd30164e722ba27fb59df33e6b1719b/zstandard-0.23.0-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:b4567955a6bc1b20e9c31612e615af6b53733491aeaa19a6b3b37f3b65477094", size = 788713, upload-time = "2024-07-15T00:15:35.815Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/5b/b3/1a028f6750fd9227ee0b937a278a434ab7f7fdc3066c3173f64366fe2466/zstandard-0.23.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:1e172f57cd78c20f13a3415cc8dfe24bf388614324d25539146594c16d78fcc8", size = 633459, upload-time = "2024-07-15T00:15:37.995Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/26/af/36d89aae0c1f95a0a98e50711bc5d92c144939efc1f81a2fcd3e78d7f4c1/zstandard-0.23.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b0e166f698c5a3e914947388c162be2583e0c638a4703fc6a543e23a88dea3c1", size = 4945707, upload-time = "2024-07-15T00:15:39.872Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/cd/2e/2051f5c772f4dfc0aae3741d5fc72c3dcfe3aaeb461cc231668a4db1ce14/zstandard-0.23.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:12a289832e520c6bd4dcaad68e944b86da3bad0d339ef7989fb7e88f92e96072", size = 5306545, upload-time = "2024-07-15T00:15:41.75Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/0a/9e/a11c97b087f89cab030fa71206963090d2fecd8eb83e67bb8f3ffb84c024/zstandard-0.23.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:d50d31bfedd53a928fed6707b15a8dbeef011bb6366297cc435accc888b27c20", size = 5337533, upload-time = "2024-07-15T00:15:44.114Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/fc/79/edeb217c57fe1bf16d890aa91a1c2c96b28c07b46afed54a5dcf310c3f6f/zstandard-0.23.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:72c68dda124a1a138340fb62fa21b9bf4848437d9ca60bd35db36f2d3345f373", size = 5436510, upload-time = "2024-07-15T00:15:46.509Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/81/4f/c21383d97cb7a422ddf1ae824b53ce4b51063d0eeb2afa757eb40804a8ef/zstandard-0.23.0-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:53dd9d5e3d29f95acd5de6802e909ada8d8d8cfa37a3ac64836f3bc4bc5512db", size = 4859973, upload-time = "2024-07-15T00:15:49.939Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/ab/15/08d22e87753304405ccac8be2493a495f529edd81d39a0870621462276ef/zstandard-0.23.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:6a41c120c3dbc0d81a8e8adc73312d668cd34acd7725f036992b1b72d22c1772", size = 4936968, upload-time = "2024-07-15T00:15:52.025Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/eb/fa/f3670a597949fe7dcf38119a39f7da49a8a84a6f0b1a2e46b2f71a0ab83f/zstandard-0.23.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:40b33d93c6eddf02d2c19f5773196068d875c41ca25730e8288e9b672897c105", size = 5467179, upload-time = "2024-07-15T00:15:54.971Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/4e/a9/dad2ab22020211e380adc477a1dbf9f109b1f8d94c614944843e20dc2a99/zstandard-0.23.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:9206649ec587e6b02bd124fb7799b86cddec350f6f6c14bc82a2b70183e708ba", size = 4848577, upload-time = "2024-07-15T00:15:57.634Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/08/03/dd28b4484b0770f1e23478413e01bee476ae8227bbc81561f9c329e12564/zstandard-0.23.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:76e79bc28a65f467e0409098fa2c4376931fd3207fbeb6b956c7c476d53746dd", size = 4693899, upload-time = "2024-07-15T00:16:00.811Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/2b/64/3da7497eb635d025841e958bcd66a86117ae320c3b14b0ae86e9e8627518/zstandard-0.23.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:66b689c107857eceabf2cf3d3fc699c3c0fe8ccd18df2219d978c0283e4c508a", size = 5199964, upload-time = "2024-07-15T00:16:03.669Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/43/a4/d82decbab158a0e8a6ebb7fc98bc4d903266bce85b6e9aaedea1d288338c/zstandard-0.23.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:9c236e635582742fee16603042553d276cca506e824fa2e6489db04039521e90", size = 5655398, upload-time = "2024-07-15T00:16:06.694Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/f2/61/ac78a1263bc83a5cf29e7458b77a568eda5a8f81980691bbc6eb6a0d45cc/zstandard-0.23.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:a8fffdbd9d1408006baaf02f1068d7dd1f016c6bcb7538682622c556e7b68e35", size = 5191313, upload-time = "2024-07-15T00:16:09.758Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/e7/54/967c478314e16af5baf849b6ee9d6ea724ae5b100eb506011f045d3d4e16/zstandard-0.23.0-cp312-cp312-win32.whl", hash = "sha256:dc1d33abb8a0d754ea4763bad944fd965d3d95b5baef6b121c0c9013eaf1907d", size = 430877, upload-time = "2024-07-15T00:16:11.758Z" },
    { url = "http://mirrors.tuna.tsinghua.edu.cn/pypi/web/packages/75/37/872d74bd7739639c4553bf94c84af7d54d8211b626b352bc57f0fd8d1e3f/zstandard-0.23.0-cp312-cp312-win_amd64.whl", hash = "sha256:64585e1dba664dc67c7cdabd56c1e5685233fbb1fc1966cfba2a340ec0dfff7b", size = 495595, upload-time = "2024-07-15T00:16:13.731Z" },
]
