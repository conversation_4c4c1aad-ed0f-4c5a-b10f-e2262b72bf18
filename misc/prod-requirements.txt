tornado~=6.4.2  # apache
alembic~=1.8.1  # MIT
SQLAlchemy>=1.3, <1.4 # MIT
attrs==21.4.0  # MIT
peewee-async~=0.10.0  # MIT
aiopg~=1.4.0  # BSD
aiomysql~=0.2.0  # MIT
pymysql==1.1.1 # MIT
asyncpg~=0.25.0; python_version < "3.11" and sys_platform == "linux"  # Apache 2.0
asyncpg~=0.27.0; python_version < "3.11" and sys_platform == "darwin"  # Apache 2.0
asyncpg~=0.29.0; python_version >= "3.11"  # Apache 2.0
babel==2.9.1  # BSD
celery[redis]~=5.5.0 # BSD
python-redis-lock~=3.7.0  # BSD-2-Clause License
scikit-learn~=1.3.2  # MIT
imbalanced-learn~=0.12.3  # MIT
pandas~=2.2.2  # BSD
docopt  # MIT
rjieba  # MIT
webargs~=8.7.0  # MIT
marshmallow~=4.0.0  # MIT
pika==1.2.0  # BSD
dateparser==1.1.1  # BSD
# TODO: remove this after we migrate to peewee.
gino==1.1.0rc1  # BSD
pypinyin==0.42.0
xlutils  # MIT
jsondiff
openpyxl~=3.1.0
aenum==3.1.16
# NOTE: Just for compatibility with reading old excel(MS03) files.
xlrd~=2.0.1  # BSD

gunicorn>=20.0.4
python-docx<0.9,>=0.8
click  # BSD-3-Clause License
cryptography~=39.0.0  # Apache 2.0&BSD-2-Clause
beautifulsoup4  # MIT
msgspec  # BSD-3-Clause License
aiofile  # Apache-2.0
apispec  # MIT
interval~=1.0.0  # LGPL
oracledb~=1.2.1; python_version < "3.12"
oracledb~=2.1.2; python_version >= "3.12"
cx-Oracle~=8.3.0
openai~=1.55.3  # MIT
websocket-client  # Apache 2.0
traceback-with-variables~=2.0.4  # MIT
pyparsing~=3.1.1
fastapi~=0.115.12
fastapi-permissions~=0.2.7
pydantic~=2.5.2
uvicorn[standard]~=0.34.0
uvicorn-worker~=0.3.0
fire  # Apache License 2.0
pyjwt~=2.8.0
playwright~=1.43.0
magika==0.6.2  # Apache License 2.0
skl2onnx~=1.17.0  # Apache License 2.0
tiktoken~=0.7.0
pgvector~=0.3.0
jinja2~=3.1.4
glom~=24.11.0
imapclient~=3.0.1
flashtext==2.7
python-statemachine==2.5.0
user-agents~=2.2.0
email-validator~=2.2.0
a2wsgi==1.10.0

python-pptx~=1.0.2
xlsxwriter~=3.2.5
unoserver==2.0
PyMuPDF==1.24.5

--index-url http://**********:3141/cheftin/pypi --trusted-host **********
aipod~=1.3.1; python_version >= "3.10"
farm
pdfparser==2.3.38
wordinsight==0.0.88
calliper-diff~=1.3.4
utensils>=0.1.82
speedy~=0.1.99
glazer_docx_convert~=0.3.34
invoke~=2.2.0
supervisor>=6.6.7
onnxconverter-common~=1.16.0  # MIT
weasyprint~=62.3  # BSD
