"""add_index_type_answer_data

Revision ID: 5def70a2ce59
Revises: 0ab80d4c5b03
Create Date: 2025-09-01 18:22:21.287358
"""

import sqlalchemy as sa
from alembic import op

from remarkable.common.migrate_util import create_array_field

# revision identifiers, used by Alembic.
revision = "5def70a2ce59"
down_revision = "0ab80d4c5b03"
branch_labels = None
depends_on = None

TABLE_ANSWER_DATA = "answer_data"
TABLE_ANSWER_DATA_STAT = "answer_data_stat"

def upgrade():
    op.add_column(TABLE_ANSWER_DATA, create_array_field("indexes", sa.ARRAY(sa.Integer), server_default=sa.text("array[]::integer[]")))
    op.add_column(TABLE_ANSWER_DATA, sa.Column("answer_type", sa.Integer, server_default=sa.text("0")))

    # op.add_column(TABLE_ANSWER_DATA_STAT, create_array_field("indexes", sa.ARRAY(sa.Integer), server_default=sa.text("array[]::integer[]")))
    op.add_column(TABLE_ANSWER_DATA_STAT, sa.Column("answer_type", sa.Integer, server_default=sa.text("0")))

def downgrade():
    op.drop_column(TABLE_ANSWER_DATA, "indexes")
    op.drop_column(TABLE_ANSWER_DATA, "answer_type")

    # op.drop_column(TABLE_ANSWER_DATA_STAT, "indexes")
    op.drop_column(TABLE_ANSWER_DATA_STAT, "answer_type")
